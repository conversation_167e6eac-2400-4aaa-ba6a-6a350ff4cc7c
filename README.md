# Project Notes
If you encounter the following error: "node:internal/modules/cjs/loader:1586
  return process.dlopen(module, path.toNamespacedPath(filename));",
please run the following command to fix it:
```bash
cp node_modules/@tensorflow/tfjs-node/deps/lib/tensorflow.dll node_modules/@tensorflow/tfjs-node/lib/napi-v8/
```
  
# Sentry Error Monitoring Setup Guide

## 🎯 **OVERVIEW**

This project is configured with **dual Sentry setup**:
- ✅ **Sentry Cloud** (immediate use - recommended for current VPS)
- ✅ **Self-hosted Sentry** (future use when upgrading VPS)

## 🚀 **OPTION 1: SENTRY CLOUD (RECOMMENDED)**

### **Why Sentry Cloud for Now?**
- ✅ **Zero setup** - Ready in 5 minutes
- ✅ **No VPS memory usage** - Runs on Sentry's servers
- ✅ **5,000 errors/month free** - Perfect for small-medium projects
- ✅ **Professional features** - Real-time alerts, performance monitoring
- ✅ **No maintenance** - <PERSON><PERSON> handles updates, backups, scaling

### **Setup Steps:**

#### **1. Create Sentry Account**
1. Go to [sentry.io](https://sentry.io)
2. Sign up for free account
3. Create new project
4. Select **Node.js** as platform
5. Copy your DSN (Data Source Name)

#### **2. Configure Environment Variables**
Add to your `.env` file:
```bash
# Sentry Cloud Configuration
SENTRY_DSN=https://<EMAIL>/123456
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=1.0.0
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1
```

#### **3. Deploy & Test**
```bash
npm run build
pm2 restart all
```

#### **4. Test Error Tracking**
```bash
# Trigger a test error
curl -X POST http://your-domain/api/v1/test-error
```

### **Sentry Cloud Free Tier Limits:**
- ✅ **5,000 errors/month**
- ✅ **10,000 performance units/month**
- ✅ **1 project**
- ✅ **30-day error retention**
- ✅ **Basic integrations**

## 🏗️ **OPTION 2: SELF-HOSTED SENTRY (FUTURE)**

### **When to Use Self-hosted?**
- 🔄 **When upgrading VPS** (8GB+ RAM recommended)
- 🔄 **For unlimited errors** (no monthly limits)
- 🔄 **For data privacy** (all data stays on your servers)
- 🔄 **For custom integrations**

### **System Requirements:**
- **RAM**: 8GB minimum (16GB recommended)
- **CPU**: 4 cores minimum
- **Storage**: 50GB+ SSD
- **Docker**: Required

### **Setup Steps (Future Use):**

#### **1. Install Docker & Docker Compose**
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### **2. Clone Sentry Self-hosted**
```bash
git clone https://github.com/getsentry/self-hosted.git
cd self-hosted
```

#### **3. Configure Sentry**
```bash
# Generate secret key
./install.sh

# Edit configuration
nano sentry/config.yml
```

#### **4. Start Sentry**
```bash
docker-compose up -d
```

#### **5. Create Superuser**
```bash
docker-compose exec web sentry createuser
```

#### **6. Configure Your App**
```bash
# Update .env with self-hosted DSN
SENTRY_DSN=https://<EMAIL>/project-id
```

## 📊 **MONITORING & FEATURES**

### **Error Tracking Features:**
- ✅ **Real-time error alerts**
- ✅ **Error grouping & deduplication**
- ✅ **Stack traces with source maps**
- ✅ **User context tracking**
- ✅ **Breadcrumb trails**
- ✅ **Release tracking**

### **Performance Monitoring:**
- ✅ **API response time tracking**
- ✅ **Database query monitoring**
- ✅ **Slow transaction alerts**
- ✅ **Performance trends**

### **Custom Context:**
```typescript
import { captureException, addBreadcrumb } from '@/lib/sentry';

// Add custom context to errors
captureException(error, {
  userId: user.id,
  action: 'attendance_record',
  workSiteId: workSite.id
});

// Add breadcrumbs for debugging
addBreadcrumb('User started attendance recording', 'user.action');
```

## 🔧 **CONFIGURATION OPTIONS**

### **Environment Variables:**

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `SENTRY_DSN` | Sentry project DSN | - | `https://<EMAIL>/123` |
| `SENTRY_ENVIRONMENT` | Environment name | `development` | `production` |
| `SENTRY_RELEASE` | Release version | - | `1.0.0` |
| `SENTRY_TRACES_SAMPLE_RATE` | Performance monitoring rate | `0.1` | `0.1` (10%) |
| `SENTRY_PROFILES_SAMPLE_RATE` | Profiling rate | `0.1` | `0.1` (10%) |

### **Sample Rates Explanation:**
- **0.1** = 10% of transactions monitored (recommended for production)
- **1.0** = 100% of transactions monitored (only for development)
- **0.0** = Disable performance monitoring

## 📈 **MONITORING DASHBOARD**

### **Key Metrics to Watch:**
1. **Error Rate** - Should be <1%
2. **Response Time** - Should be <100ms for most endpoints
3. **Throughput** - Requests per minute
4. **Apdex Score** - User satisfaction metric

### **Alert Configuration:**
1. **Error Spike** - >10 errors in 5 minutes
2. **Slow Response** - >500ms average response time
3. **High Error Rate** - >5% error rate

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. DSN Not Working**
```bash
# Check DSN format
SENTRY_DSN=https://<EMAIL>/123456
```

#### **2. No Errors Appearing**
- Check if `SENTRY_DSN` is set
- Verify environment is correct
- Check network connectivity
- Look for initialization errors in logs

#### **3. Too Many Events**
- Reduce `SENTRY_TRACES_SAMPLE_RATE`
- Add more error filtering in `beforeSend`
- Check for error loops

#### **4. Performance Impact**
- Lower sample rates
- Disable profiling in production
- Use async error reporting

## 📋 **DEPLOYMENT CHECKLIST**

### **Before Deployment:**
- [ ] Sentry account created
- [ ] DSN configured in environment
- [ ] Error filtering configured
- [ ] Sample rates optimized
- [ ] Alerts configured

### **After Deployment:**
- [ ] Test error reporting
- [ ] Verify performance monitoring
- [ ] Check alert notifications
- [ ] Monitor error rates
- [ ] Review performance metrics

## 🎉 **BENEFITS FOR YOUR PROJECT**

### **Immediate Benefits:**
- ✅ **Real-time error notifications** - Know about issues instantly
- ✅ **Detailed error context** - User info, request details, stack traces
- ✅ **Performance insights** - Identify slow endpoints
- ✅ **Release tracking** - Monitor error rates per deployment

### **Long-term Benefits:**
- ✅ **Improved reliability** - Catch and fix issues faster
- ✅ **Better user experience** - Reduce downtime and errors
- ✅ **Data-driven decisions** - Performance metrics guide optimization
- ✅ **Professional monitoring** - Enterprise-grade error tracking

## 🔄 **MIGRATION PATH**

### **Current Setup (Sentry Cloud):**
```bash
SENTRY_DSN=https://<EMAIL>/123456
```

### **Future Setup (Self-hosted):**
```bash
SENTRY_DSN=https://<EMAIL>/123456
```

**Migration is seamless** - just change the DSN when ready!

## 📞 **SUPPORT**

- **Sentry Documentation**: [docs.sentry.io](https://docs.sentry.io)
- **Self-hosted Guide**: [develop.sentry.dev/self-hosted](https://develop.sentry.dev/self-hosted/)
- **Community Forum**: [forum.sentry.io](https://forum.sentry.io)

**Your error monitoring is now production-ready! 🚀**

# Sentry Usage Guide - How to Use in Your Code

## 🎯 **JAWABAN PERTANYAAN ANDA**

> "Setup sentry nya begitu aja kah, engakk ada saya harus pake di kode2 lain kah?"

**JAWABAN:**
- ✅ **Basic setup**: Sudah otomatis capture semua unhandled errors
- ✅ **Manual usage**: Bisa tambahkan di kode untuk error tracking yang lebih detail
- ✅ **User context**: Otomatis set di AuthMiddleware
- ✅ **Performance monitoring**: Otomatis track semua HTTP requests

## 🚀 **AUTOMATIC FEATURES (SUDAH JALAN)**

### **1. Unhandled Error Capture** ✅
```typescript
// Semua error yang tidak di-catch otomatis masuk Sentry
throw new Error("This will be automatically captured");
```

### **2. User Context Tracking** ✅
```typescript
// Di AuthMiddleware, otomatis set user context
setUserContext({
    id: authUser.id,
    email: authUser.email,
    username: authUser.name,
});
```

### **3. Performance Monitoring** ✅
```typescript
// Semua HTTP requests otomatis di-track
// Response time, status codes, etc.
```

## 🔧 **MANUAL USAGE (OPTIONAL)**

### **Import Sentry Helpers**
```typescript
import { 
    captureException, 
    captureMessage, 
    addBreadcrumb, 
    setUserContext 
} from "@/lib/sentry";
```

### **1. Manual Error Capture**
```typescript
// Di try-catch blocks
try {
    await someRiskyOperation();
} catch (error) {
    // Capture dengan context tambahan
    captureException(error, {
        userId: user.id,
        action: 'attendance_record',
        workSiteId: workSite.id,
        additionalData: { ... }
    });
    
    // Tetap throw error untuk normal error handling
    throw error;
}
```

### **2. Custom Messages**
```typescript
// Log important events
captureMessage("User completed attendance recording", "info", {
    userId: user.id,
    attendanceType: "check_in",
    location: { lat, lng }
});

// Warning messages
captureMessage("Face recognition confidence low", "warning", {
    confidence: 0.6,
    threshold: 0.8,
    userId: user.id
});
```

### **3. Breadcrumbs for Debugging**
```typescript
// Add breadcrumbs untuk debugging trail
addBreadcrumb("User started attendance process", "user.action", "info", {
    step: "face_capture",
    timestamp: createdAt: new Date().toISOString()
});

addBreadcrumb("Face recognition completed", "process", "info", {
    confidence: 0.95,
    duration: "150ms"
});

addBreadcrumb("Database save initiated", "database", "info");
```

## 📍 **WHERE TO USE SENTRY**

### **1. Critical Business Logic**
```typescript
// AttendanceService.ts
public recordAttandance = async ({ user, payload }: ServiceParams<...>) => {
    try {
        addBreadcrumb("Attendance recording started", "attendance", "info", {
            userId: user.id,
            action: payload.action
        });

        // ... business logic

        addBreadcrumb("Attendance recorded successfully", "attendance", "info");
        
    } catch (error) {
        captureException(error, {
            service: "AttendanceService",
            method: "recordAttandance",
            userId: user.id,
            payload: payload
        });
        throw error;
    }
};
```

### **2. External API Calls**
```typescript
// FaceApiService.ts
public extractFaceDescriptor = async (imagePath: string) => {
    try {
        addBreadcrumb("Face API call started", "external_api", "info");
        
        const result = await faceApiCall(imagePath);
        
        addBreadcrumb("Face API call completed", "external_api", "info", {
            success: true,
            duration: "200ms"
        });
        
        return result;
    } catch (error) {
        captureException(error, {
            service: "FaceApiService",
            method: "extractFaceDescriptor",
            imagePath: imagePath
        });
        throw error;
    }
};
```

### **3. Database Operations**
```typescript
// Repository classes
public async create(data: TCreationAttributes): Promise<TModel> {
    try {
        addBreadcrumb("Database create operation", "database", "info", {
            model: this.model.name,
            operation: "create"
        });
        
        const result = await this.model.create(data);
        return result;
    } catch (error) {
        captureException(error, {
            repository: this.constructor.name,
            operation: "create",
            model: this.model.name,
            data: data
        });
        throw error;
    }
}
```

### **4. Authentication & Authorization**
```typescript
// AuthService.ts
public login = async (credentials: LoginDTO) => {
    try {
        addBreadcrumb("Login attempt started", "auth", "info", {
            email: credentials.email
        });

        // ... login logic

        captureMessage("User logged in successfully", "info", {
            userId: user.id,
            email: user.email,
            loginMethod: "email_password"
        });

    } catch (error) {
        captureException(error, {
            service: "AuthService",
            method: "login",
            email: credentials.email,
            attemptTime: createdAt: new Date().toISOString()
        });
        throw error;
    }
};
```

## 🧪 **TEST ENDPOINTS YANG SUDAH DIBUAT**

### **Health Check**
```bash
GET /health
GET /test/
```

### **Sentry Testing**
```bash
# Test error capture
GET /test/error
POST /test/error

# Test message capture
POST /test/message
{
    "message": "Test message",
    "level": "info"
}

# Test performance monitoring
GET /test/performance?delay=2000

# Test with authentication (user context)
GET /test/error-auth
GET /test/performance-auth
```

### **System Testing**
```bash
# Test database
GET /test/database

# Test Redis
GET /test/redis

# Test memory usage
GET /test/memory
```

## 📊 **SENTRY DASHBOARD FEATURES**

### **Error Tracking**
- ✅ **Error grouping** - Similar errors grouped together
- ✅ **Stack traces** - Full error stack with source maps
- ✅ **User context** - Which user experienced the error
- ✅ **Breadcrumbs** - Step-by-step trail leading to error
- ✅ **Release tracking** - Errors per deployment

### **Performance Monitoring**
- ✅ **Response times** - API endpoint performance
- ✅ **Throughput** - Requests per minute
- ✅ **Error rates** - Percentage of failed requests
- ✅ **Slow transactions** - Identify bottlenecks

### **Alerts & Notifications**
- ✅ **Email alerts** - When errors spike
- ✅ **Slack integration** - Real-time notifications
- ✅ **Custom rules** - Alert on specific conditions

## 🎯 **BEST PRACTICES**

### **1. Don't Over-Capture**
```typescript
// ❌ Don't capture every small thing
captureMessage("User clicked button", "info"); // Too noisy

// ✅ Capture important business events
captureMessage("Payment processed successfully", "info", {
    amount: payment.amount,
    userId: user.id
});
```

### **2. Add Meaningful Context**
```typescript
// ❌ Basic error capture
captureException(error);

// ✅ Rich context
captureException(error, {
    userId: user.id,
    action: "attendance_record",
    workSiteId: workSite.id,
    timestamp: createdAt: new Date().toISOString(),
    requestId: req.id
});
```

### **3. Use Breadcrumbs Strategically**
```typescript
// ✅ Good breadcrumb trail
addBreadcrumb("User authentication started", "auth");
addBreadcrumb("Face recognition initiated", "biometric");
addBreadcrumb("Location validation passed", "location");
addBreadcrumb("Attendance record created", "database");
// If error occurs, you'll see the full journey
```

## 🚀 **QUICK START CHECKLIST**

### **Already Done** ✅
- [x] Sentry initialized in main.ts
- [x] User context set in AuthMiddleware
- [x] Performance monitoring enabled
- [x] Test endpoints created
- [x] Error filtering configured

### **Optional Enhancements**
- [ ] Add manual error capture in critical services
- [ ] Add breadcrumbs in complex workflows
- [ ] Add custom messages for business events
- [ ] Setup Slack/email alerts
- [ ] Configure custom error grouping

## 🎉 **CONCLUSION**

**Your Sentry setup is COMPLETE and WORKING!** 

- ✅ **Automatic error capture** - All unhandled errors tracked
- ✅ **User context** - Know which user had issues
- ✅ **Performance monitoring** - Track API response times
- ✅ **Test endpoints** - Verify everything works

**Manual usage is OPTIONAL** - Sentry already captures the most important stuff automatically. Add manual tracking only for critical business logic where you need extra context.

**Test it now:**
```bash
curl http://your-domain/test/error
```

Check your Sentry dashboard - you should see the test error! 🎯
