/** biome-ignore-all lint/suspicious/noDuplicateObjectKeys: "" */
const { config } = require("dotenv");
const path = require("node:path");
const { kyselyCamelCaseHook, kyselyTypeFilter, makeKyselyHook } = require("kanel-kysely");

const NODE_ENV = process.env.NODE_ENV ?? "development";
const envFilePath = path.resolve(process.cwd(), `.env.${NODE_ENV}`);

config({ path: envFilePath });

module.exports = {
	connection: process.env.DATABASE_URI || "postgresql://user:password@host:port/database", // Ganti dengan connection string Anda
	preDeleteOutputFolder: true,
	outputPath: "./src/database/types",
	preRenderHooks: [makeKyselyHook({}), kyselyCamelCaseHook],
	typeFilter: kyselyTypeFilter,
	idGenerators: false,
	customTypeMap: {
		// Tipe Numerik: Semua tipe integer, big integer, dll. dijadikan 'number'
		"pg_catalog.int2": "number", // smallint
		"pg_catalog.int4": "number", // integer
		"pg_catalog.int8": "number", // bigint
		"pg_catalog.float4": "number", // real
		"pg_catalog.float8": "number", // double precision
		"pg_catalog.numeric": "number", // numeric / decimal

		// Tipe Tanggal & Waktu
		"pg_catalog.date": "string", // DATE
		"pg_catalog.timestamp": "string", // TIMESTAMP WITHOUT TIME ZONE
		"pg_catalog.timestamptz": "string", // TIMESTAMP WITH TIME ZONE
		"pg_catalog.time": "string", // TIME WITHOUT TIME ZONE (diubah menjadi string sesuai permintaan)
		"pg_catalog.timetz": "string", // TIME WITH TIME ZONE (diubah menjadi string sesuai permintaan)

		// Tipe Boolean
		"pg_catalog.bool": "boolean", // boolean

		// Tipe String & Karakter
		"pg_catalog.text": "string", // text
		"pg_catalog.varchar": "string", // varchar
		"pg_catalog.char": "string", // char
		"pg_catalog.bpchar": "string", // character (fixed-length, sering juga muncul sebagai bpchar)
		"public.citext": "string", // citext (jika Anda menggunakan ekstensi citext)

		// Tipe JSON
		"pg_catalog.json": "string", // json (disimpan sebagai string, Anda bisa parse secara manual)
		"pg_catalog.jsonb": "string", // jsonb (disimpan sebagai string, Anda bisa parse secara manual)

		// Tipe Binary
		"pg_catalog.bytea": "string", // bytea (disimpan sebagai string Base64 atau buffer, string lebih umum untuk representasi)

		// Tipe Lain yang Spesifik
		"pg_catalog.tsvector": "string", // tsvector (sering kali diwakili sebagai string)
		// Jika Anda memiliki UUID, umumnya Kanel-Kysely sudah mengenali ke string, tapi bisa ditambahkan:
		// "pg_catalog.uuid": "string",
	},
};
