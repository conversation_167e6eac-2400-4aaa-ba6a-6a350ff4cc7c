# APP
FRONTEND_URL=""
SERVER_URL=""
NODE_ENV=""
PORT=""
COMPANY_MAIL=""

# SECURITY & CORS
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com,https://admin.yourdomain.com"
TRUST_PROXY="1"
CSP_ENABLED="true"

# RATE LIMITING
RATE_LIMIT_WINDOW_MS="60000"
RATE_LIMIT_MAX_REQUESTS="60"
RATE_LIMIT_MAX_REQUESTS_DEV="100"

PRESIDENT_DIRECTOR_ID=""
ALL_PERMISSION_ID=""

DEFAULT_WORK_SITE_ID=""

ANNUAL_LEAVE_POLICY_ID=""
MARRIAGE_LEAVE_POLICY_ID=""
SICK_LEAVE_POLICY_ID=""
MATERNITY_LEAVE_POLICY_ID=""

DEFAULT_USER_IMAGE=""
DEFAULT_USER_PASSWORD=""
DEFAULT_ADMIN_PASSWORD=""

# DATABASE
DATABASE_URI=""
DB_USERNAME=""
DB_PASSWORD=""
DB_NAME=""
DB_HOST=""
DB_PORT=""

# MAIL
RESEND_API_KEY=""

# CACHE
REDIS_HOST=""
REDIS_PASSWORD=""
REDIS_PORT=""

# AMQP CLOUD
AMQP_URI=""

# TOKENIZE
ACCESS_TOKEN_SECRET=""
REFRESH_TOKEN_SECRET=""
FORGOT_PASSWORD_TOKEN_SECRET=""
ACCESS_TOKEN_EXPIRATION="30d"
REFRESH_TOKEN_EXPIRATION="30d"
FORGOT_PASSWORD_TOKEN_EXPIRATION="1h"

# SENTRY CONFIGURATION
# For Sentry Cloud (recommended for current VPS setup)
SENTRY_DSN=""

# For Self-hosted Sentry (future use when upgrading VPS)
# SENTRY_DSN=https://<EMAIL>/project-id

SENTRY_ENVIRONMENT=""
SENTRY_RELEASE=""
SENTRY_TRACES_SAMPLE_RATE=""
SENTRY_PROFILES_SAMPLE_RATE=""