# compiled output
/dist
/node_modules

# env
.env
.env.prod
.env.local
.env.dev
.env.development
.env.production

# Logs
logs
*.log
.*-audit.json
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# CERT
*.pem

# storage
storage/