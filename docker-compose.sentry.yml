# Docker Compose with <PERSON><PERSON> Self-hosted
# Use this when upgrading to 8GB+ VPS and want Sen<PERSON> on same server

version: '3.8'

services:
  # Your Attendance API
  attendance-api:
    build: .
    ports:
      - "9000:9000"
    environment:
      - NODE_ENV=production
      - SENTRY_DSN=http://sentry-key@sentry:9000/1
      # Add your other environment variables
    depends_on:
      - postgres
      - redis
      - sentry
    networks:
      - app-network
    restart: unless-stopped

  # PostgreSQL for your app
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=attendance_db
      - POSTGRES_USER=attendance_user
      - POSTGRES_PASSWORD=your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  # Redis for your app
  redis:
    image: redis:6-alpine
    command: redis-server --requirepass your_redis_password
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped

  # Sentry PostgreSQL
  sentry-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=sentry
      - POSTGRES_USER=sentry
      - POSTGRES_PASSWORD=sentry_password
    volumes:
      - sentry_postgres_data:/var/lib/postgresql/data
    networks:
      - sentry-network
    restart: unless-stopped

  # Sentry Redis
  sentry-redis:
    image: redis:6-alpine
    volumes:
      - sentry_redis_data:/data
    networks:
      - sentry-network
    restart: unless-stopped

  # Sentry Web
  sentry:
    image: sentry:latest
    ports:
      - "9001:9000"  # Sentry on port 9001
    environment:
      - SENTRY_SECRET_KEY=your-sentry-secret-key-here
      - SENTRY_POSTGRES_HOST=sentry-postgres
      - SENTRY_POSTGRES_PORT=5432
      - SENTRY_DB_NAME=sentry
      - SENTRY_DB_USER=sentry
      - SENTRY_DB_PASSWORD=sentry_password
      - SENTRY_REDIS_HOST=sentry-redis
      - SENTRY_REDIS_PORT=6379
    depends_on:
      - sentry-postgres
      - sentry-redis
    volumes:
      - sentry_data:/var/lib/sentry/files
    networks:
      - sentry-network
    restart: unless-stopped

  # Sentry Worker
  sentry-worker:
    image: sentry:latest
    command: sentry run worker
    environment:
      - SENTRY_SECRET_KEY=your-sentry-secret-key-here
      - SENTRY_POSTGRES_HOST=sentry-postgres
      - SENTRY_POSTGRES_PORT=5432
      - SENTRY_DB_NAME=sentry
      - SENTRY_DB_USER=sentry
      - SENTRY_DB_PASSWORD=sentry_password
      - SENTRY_REDIS_HOST=sentry-redis
      - SENTRY_REDIS_PORT=6379
    depends_on:
      - sentry-postgres
      - sentry-redis
    volumes:
      - sentry_data:/var/lib/sentry/files
    networks:
      - sentry-network
    restart: unless-stopped

  # Sentry Cron
  sentry-cron:
    image: sentry:latest
    command: sentry run cron
    environment:
      - SENTRY_SECRET_KEY=your-sentry-secret-key-here
      - SENTRY_POSTGRES_HOST=sentry-postgres
      - SENTRY_POSTGRES_PORT=5432
      - SENTRY_DB_NAME=sentry
      - SENTRY_DB_USER=sentry
      - SENTRY_DB_PASSWORD=sentry_password
      - SENTRY_REDIS_HOST=sentry-redis
      - SENTRY_REDIS_PORT=6379
    depends_on:
      - sentry-postgres
      - sentry-redis
    volumes:
      - sentry_data:/var/lib/sentry/files
    networks:
      - sentry-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - attendance-api
      - sentry
    networks:
      - app-network
      - sentry-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  sentry_postgres_data:
  sentry_redis_data:
  sentry_data:

networks:
  app-network:
    driver: bridge
  sentry-network:
    driver: bridge
