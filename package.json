{"name": "simple-expressts-boilerplate", "version": "1.0.0", "description": "", "scripts": {"generate-openapi": "tsx src/scripts/openapi-generator.script.ts", "build": "cross-env NODE_ENV=production tsc && tsc-alias && ts-add-js-extension --dir=dist && npm run generate-openapi", "dev": "cross-env NODE_ENV=development npm run generate-openapi && nodemon -r tsconfig-paths/register --watch src --exec tsx src/main.ts", "start": "cross-env NODE_ENV=production node dist/src/main.js", "biome:lint": "biome lint ./src", "biome:format": "biome format ./src --write", "migrate:dev": "cross-env NODE_ENV=development sequelize-cli db:migrate", "migrate:dev:undo": "cross-env NODE_ENV=development sequelize-cli db:migrate:undo", "migrate:dev:undo:all": "cross-env NODE_ENV=development sequelize-cli db:migrate:undo:all", "migrate:dev:fresh": "npm run migrate:dev:undo:all && npm run migrate:dev", "migrate": "cross-env NODE_ENV=production sequelize-cli db:migrate", "migrate:generate": "sequelize-cli migration:generate --name", "migrate:undo": "cross-env NODE_ENV=production sequelize-cli db:migrate:undo", "migrate:undo:all": "cross-env NODE_ENV=production sequelize-cli db:migrate:undo:all", "migrate:fresh": "npm run migrate:undo:all && npm run migrate", "seed:generate": "sequelize-cli seed:generate --name", "seed:dev:undo": "cross-env NODE_ENV=development sequelize-cli db:seed:undo:all", "seed:undo": "sequelize-cli db:seed:undo:all", "seed:dev": "cross-env NODE_ENV=development sequelize-cli db:seed:all", "seed": "cross-env NODE_ENV=production sequelize-cli db:seed:all", "generate-types:dev": "cross-env NODE_ENV=development kanel", "generate-types": "cross-env NODE_ENV=production kanel", "worker:dev:email": "nodemon --watch src --exec tsx src/modules/auth/jobs/send-email.job.ts", "worker:dev:main-worker": "nodemon --watch src --exec tsx src/modules/common/jobs/index.ts", "worker:email": "cross-env NODE_ENV=production node dist/src/modules/auth/jobs/send-email.job.js", "worker:main-worker": "cross-env NODE_ENV=production node dist/src/modules/common/jobs/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@asteasolutions/zod-to-openapi": "^8.0.0", "@sentry/node": "^9.22.0", "@sentry/profiling-node": "^9.22.0", "@tensorflow/tfjs-node": "^4.22.0", "@vladmandic/face-api": "^1.7.15", "bcryptjs": "^2.4.3", "bullmq": "^5.53.0", "canvas": "^3.1.0", "colors": "^1.4.0", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "csv-parse": "^5.6.0", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "drizzle-orm": "^0.44.3", "express": "^5.0.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^7.4.1", "express-slow-down": "^2.1.0", "express-useragent": "^1.0.15", "face-api.js": "^0.22.2", "fast-csv": "^5.0.2", "helmet": "^8.0.0", "hpp": "^0.2.3", "i18next": "^23.16.4", "i18next-fs-backend": "^2.3.2", "i18next-http-middleware": "^3.6.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "kanel": "^3.14.1", "kysely": "^0.28.2", "lodash": "^4.17.21", "luxon": "^3.7.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nanoid": "^3.3.11", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "rate-limit-redis": "^4.2.1", "resend": "^4.0.1-alpha.0", "sharp": "^0.34.3", "winston": "^3.16.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.0 || ^4.0.0", "zod-validation-error": "^3.5.3"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.5", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-useragent": "^1.0.5", "@types/helmet": "^4.0.0", "@types/hpp": "^0.2.6", "@types/jsonwebtoken": "^9.0.7", "@types/luxon": "^3.6.2", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.9.0", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.4", "kanel-kysely": "^0.7.1", "nodemon": "^3.1.7", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "ts-add-js-extension": "^1.6.5", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "typescript": "^5.6.0"}, "peerDependencies": {"zod": "^3.25.0 || ^4.0.0"}}