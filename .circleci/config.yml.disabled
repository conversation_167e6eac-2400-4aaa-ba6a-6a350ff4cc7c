version: 2.1

jobs:
  deploy:
    docker:
      - image: cimg/node:lts # Menggunakan LTS Node.js

    steps:
      - checkout

      # Setup caching untuk node_modules
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package.json" }}
            - v1-dependencies-

      # Install dependencies
      - run:
          name: Install Dependencies
          command: npm install --frozen-lockfile

      # Save cache
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package.json" }}

      # Build project di CircleCI
      # Pastikan npm run build menghasilkan output ke folder 'dist' di root proyek,
      # dengan subfolder 'src' di dalamnya (yaitu dist/src/).
      - run:
          name: Build Project
          command: npm run build

      # Verifikasi bahwa folder 'dist/src' ada dan berisi file setelah build.
      - run:
          name: Verify Build Output
          command: |
            if [ ! -d "dist/src" ]; then
              echo "Error: 'dist/src' directory was not created by the build process. Please check your build configuration."
              exit 1
            fi
            echo "Contents of 'dist/src' folder:"
            ls -la dist/src
            echo "Contents of 'dist' folder:"
            ls -la dist # Ini akan menunjukkan dist/src di dalamnya

      # Install rsync
      - run:
          name: Install rsync
          command: sudo apt-get update && sudo apt-get install -y rsync

      # Setup SSH
      - add_ssh_keys:
          fingerprints:
            - "${SSH_KEY_FINGERPRINT}"

      # Deploy hanya folder 'dist' dan file penting di root ke server
      - run:
          name: Deploy Build & Essential Files to Server
          command: |
            # Bypass StrictHostKeyChecking untuk SSH
            mkdir -p ~/.ssh
            echo "StrictHostKeyChecking no" >> ~/.ssh/config

            # 1. Salin folder 'dist' (termasuk isinya dist/src/ dan dist/public/docs/) ke server.
            # Ini akan memastikan hanya hasil build yang dikirim.
            # Gunakan --delete untuk folder 'dist' karena ini adalah hasil build yang harus bersih.
            rsync -avz --delete \
              ./dist/ ${VPS_USER}@${VPS_HOST}:${VPS_PATH}/dist/

            # 2. Salin folder 'storage' TANPA --delete.
            # Ini akan menambahkan file baru tanpa menghapus yang sudah ada.
            # Pastikan VPS_PATH/storage/ sudah ada di server atau dibuat jika belum.
            rsync -avz \
              ./storage/ ${VPS_USER}@${VPS_HOST}:${VPS_PATH}/storage/

            # 3. Salin file-file penting di root proyek (misal: package.json, package-lock.json).
            rsync -avz \
              package.json \
              package-lock.json \
              default-user.webp \
              ${VPS_USER}@${VPS_HOST}:${VPS_PATH}/

            # 4. Pastikan default-user.webp ada di path tujuan kalau belum ada
            ssh ${VPS_USER}@${VPS_HOST} '
              mkdir -p ${VPS_PATH}/storage/public/uploads/users &&
              if [ ! -f ${VPS_PATH}/storage/public/uploads/users/default-user.webp ]; then
                echo "default-user.webp not found, copying...";
                cp ${VPS_PATH}/default-user.webp ${VPS_PATH}/storage/public/uploads/users/default-user.webp;
              else
                echo "default-user.webp already exists, skipping copy.";
              fi
              # Optional: hapus dari root kalau tidak dibutuhkan
              rm -f ${VPS_PATH}/default-user.webp
            '

      # Install dependencies di server (production only)
      # Ini akan berjalan di path VPS_PATH, yang sekarang memiliki package.json
      # dan folder dist/ dengan isinya.
      - run:
          name: Install Production Dependencies on Server (with libvips-dev)
          command: |
            ssh ${VPS_USER}@${VPS_HOST} "
              # Instalasi dependensi Node.js
              cd ${VPS_PATH} && npm ci --omit=dev && npm install --platform=linux --arch=x64 sharp
            "

      # Restart PM2 (jika Anda menggunakan PM2 untuk mengelola proses Node.js)
      - run:
          name: Restart PM2
          command: |
            ssh ${VPS_USER}@${VPS_HOST} "pm2 restart ${PM2_NAME}"

      - run:
          name: Install sharp on Server
          command: |
            ssh ${VPS_USER}@${VPS_HOST} "cd ${VPS_PATH} && npm install --platform=linux --arch=x64 sharp"

      # Notifikasi jika gagal
      - run:
          name: Notify if Failed
          command: |
            if [ "${CIRCLE_JOB}" != "0" ]; then
              curl -H "Content-Type: application/json" -d '{"username": "Satpam Build Backend E-Money", "content": "Hello, **Backend E-Money** failed to build 😔, please check <@809657628476178452> "}' "https://discord.com/api/webhooks/1080830222548602951/ge0M1avU9v8LN0OkMc9RFv4kx3olmTZvuyZXkf4RrCR2zwHdG6ZVNLeoWInF3ssRJe3o"
            fi
          when: on_fail

workflows:
  version: 2
  deploy_workflow:
    jobs:
      - deploy:
          filters:
            branches:
              only: main # Hanya deploy dari branch main
