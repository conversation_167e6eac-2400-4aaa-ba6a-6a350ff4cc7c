import compression from "compression";
import cookieParser from "cookie-parser";
import cors from "cors";
import express, { type Request, type Response } from "express";
import slowDown from "express-slow-down";
import useragent from "express-useragent";
import helmet from "helmet";
import morgan from "morgan";
import passport from "passport";
import { adminAttendanceRouter } from "./modules/attendance/routes/admin-attendance.route";
import { adminAttendanceRuleRouter } from "./modules/attendance/routes/admin-attendance-rule.route";
import { attendanceDebugRouter } from "./modules/attendance/routes/attendance.debug.route";
import { attendanceRouter } from "./modules/attendance/routes/attendance.route";
import { adminAuthRouter } from "./modules/auth/routes/admin-auth.route";
import { authRouter } from "./modules/auth/routes/auth.route";
import { secureFileRouter } from "./modules/common/routes/secure-file.route";
import { incentiveRouter } from "./modules/incentive/routes/incentive.route";
import { kpiRouter } from "./modules/kpi/routes/kpi.route";
import { adminLeavePolicyRouter } from "./modules/leave/routes/admin-leave-policy.route";
import { adminLeaveRequestRouter } from "./modules/leave/routes/admin-leave-request.route";
import { leavePolicyRouter } from "./modules/leave/routes/leave-policy.route";
import { leaveRequestRouter } from "./modules/leave/routes/leave-request.route";
import { adminOfficeLeaveRouter } from "./modules/office-leave/routes/admin-office-leave.route";
import { officeLeaveRouter } from "./modules/office-leave/routes/office-leave.route";
import { adminRoleRouter } from "./modules/role/routes/admin-role.route";
import { adminTaskRouter } from "./modules/task/routes/admin-task.route";
import { taskRouter } from "./modules/task/routes/task.route";
import { adminAdministratorUserRouter } from "./modules/user/routes/admin-administrator-user.route";
import { adminUserRouter } from "./modules/user/routes/admin-user.route";
import { adminViolationTypeRouter } from "./modules/violation/routes/admin-violation-type.route";
import { violationRouter } from "./modules/violation/routes/violation.route";
import { adminWorksiteRouter } from "./modules/worksite/routes/admin-worksite.route";
import { Env } from "./shared/config/env.config";
import { initializeFaceApi } from "./shared/lib/face-api";
import { PATHS } from "./shared/lib/paths";
import { flushSentry, initSentry } from "./shared/lib/sentry";
import {
	apiRateLimiter,
	errorMiddleware,
	localeMiddleware,
	loggerMiddleware,
	performanceMiddleware,
} from "./shared/middlewares";
import { unifiedValidationErrorHandler } from "./shared/middlewares/zod-validation.middleware";

import "./shared/strategies/jwt-user.strategy";
import "./shared/strategies/jwt-admin.strategy";
import "./shared/strategies/jwt-refresh-token.strategy";
import { adminHolidayRouter } from "./modules/attendance/routes/admin-holiday.route";
import { adminDashboardRouter } from "./modules/common/routes/admin-dashboard.route";
import { adminIncentiveRouter } from "./modules/incentive/routes/admin-incentive.route";
import { adminKPIRouter } from "./modules/kpi/routes/admin-kpi.route";
import { adminViolationRouter } from "./modules/violation/routes/admin-violation.route";

const app = express();

async function main() {
	// Initialize Sentry first (before any other initialization)
	initSentry();
	initializeFaceApi();

	const allowedOrigins = Env.ALLOWED_ORIGINS?.split(",").map((origin: string) =>
		origin.trim(),
	) || [Env.FRONTEND_URL];
	console.log(allowedOrigins, "ALLOWED");
	// CORS configuration - more secure for production
	const corsOptions = {
		origin:
			Env.NODE_ENV === "production"
				? allowedOrigins
				: [
						"http://localhost:3000",
						"http://localhost:5173",
						"http://localhost:4173",
						"http://127.0.0.1:3000",
					],
		methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
		// allowedHeaders: [
		// 	"Content-Type",
		// 	"Authorization",
		// 	"X-Requested-With",
		// 	"X-Client-Type",
		// 	"Accept",
		// 	"Origin",
		// 	"User-Agent",
		// 	"X-CSRF-Token",
		// 	"X-API-Key",
		// ],
		credentials: true,
		optionsSuccessStatus: 200,
		maxAge: 86400, // Cache preflight for 24 hours
	};

	app.set("trust proxy", Env.TRUST_PROXY);
	app.use(cors(corsOptions));
	app.use(cookieParser());
	app.options("*splat", cors(corsOptions));
	app.use(useragent.express());
	app.use(
		helmet({
			contentSecurityPolicy:
				Env.NODE_ENV === "production" && Env.CSP_ENABLED
					? {
							directives: {
								defaultSrc: ["'self'"],
								styleSrc: [
									"'self'",
									"'unsafe-inline'",
									"https://fonts.googleapis.com",
								],
								fontSrc: ["'self'", "https://fonts.gstatic.com"],
								imgSrc: ["'self'", "data:", "https:"],
								scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
								connectSrc: [
									"'self'",
									Env.SERVER_URL,
									"https://proxy.scalar.com",
								],
							},
						}
					: false,
			crossOriginResourcePolicy: { policy: "cross-origin" },
			hsts: {
				maxAge: 31536000,
				includeSubDomains: true,
				preload: true,
			},
		}),
	);

	app.use(
		compression({
			filter: (req, res) => {
				// Don't compress responses with this request header
				if (req.headers["x-no-compression"]) {
					return false;
				}
				// Use compression filter function
				return compression.filter(req, res);
			},
			level: 6, // Good balance between compression and CPU usage
			threshold: 1024, // Only compress responses larger than 1KB
		}),
	);

	app.get("/api-docs", (_req: Request, res: Response) => {
		res.send(`
    <!doctype html>
			<html>
				<head>
					<title>Scalar API Reference</title>
					<meta charset="utf-8" />
					<meta
						name="viewport"
						content="width=device-width, initial-scale=1" />
				</head>

				<body>
					<div id="app"></div>

					<!-- Load the Script -->
					<script src="https://cdn.jsdelivr.net/npm/@scalar/api-reference"></script>

					<!-- Initialize the Scalar API Reference -->
					<script src="/api-doc.js"></script>
				</body>
			</html>
  `);
	});

	app.use(morgan(Env.NODE_ENV === "production" ? "combined" : "dev"));
	app.use(performanceMiddleware);
	app.use(
		express.urlencoded({
			extended: true,
			limit: "10mb",
			parameterLimit: 1000,
		}),
	);
	app.use(
		express.json({
			limit: "10mb",
			strict: true,
		}),
	);

	// HTTP Parameter Pollution protection - configured for complex admin filtering
	// app.use(
	// 	hpp({
	// 		whitelist: [
	// 			// Pagination & basic params
	// 			"sort",
	// 			"fields",
	// 			"page",
	// 			"limit",
	// 			"search",
	// 			"all",
	// 			// Common filter fields that might need arrays
	// 			"type",
	// 			"status",
	// 			"userId",
	// 			"workSiteId",
	// 			"logDate",
	// 			"createdAt",
	// 			// Date range filters
	// 			"startDate",
	// 			"endDate",
	// 			"dateFrom",
	// 			"dateTo",
	// 			// Admin-specific filters
	// 			"role",
	// 			"permission",
	// 			"department",
	// 			"category",
	// 			// Violation-specific filters
	// 			"violationTypeId",
	// 			"recordedBy",
	// 			// TanStack Table specific
	// 			"columnFilters",
	// 			"globalFilter",
	// 			"sorting",
	// 		],
	// 	}),
	// );

	app.use(apiRateLimiter);
	app.use(
		slowDown({
			windowMs: 15 * 60 * 1000, // 15 minutes
			delayAfter: 5, // Allow 5 requests per windowMs without delay
			delayMs: (hits) => hits * 100, // Add 100ms delay per request after delayAfter
			maxDelayMs: 20000, // Maximum delay of 20 seconds
			skipSuccessfulRequests: true, // Don't count successful requests
			skipFailedRequests: false, // Count failed requests
		}),
	);

	// Custom logging middleware
	app.use(loggerMiddleware);
	app.use(localeMiddleware);

	// Passport initialization
	app.use(passport.initialize());

	// Security: Add request ID for tracking
	app.use((req, _res, next) => {
		req.headers["x-request-id"] =
			req.headers["x-request-id"] ||
			Math.random().toString(36).substring(2, 15) +
				Math.random().toString(36).substring(2, 15);
		next();
	});

	// Security: Remove sensitive headers from responses
	app.use((_req, res, next) => {
		res.removeHeader("X-Powered-By");
		res.removeHeader("Server");
		next();
	});

	// HERE IS APP ROUTES

	// Health check and test routes (public)
	// app.use("/health", healthCheckRoutes);
	// app.use("/test", testRoutes);

	// Private files: requires authentication
	app.use("/secure-files", secureFileRouter);
	app.use("/api/v1/auth", authRouter);
	app.use("/api/v1/attendance", attendanceRouter);
	app.use("/api/v1/leave-requests", leaveRequestRouter);
	app.use("/api/v1/leave-policies", leavePolicyRouter);
	app.use("/api/v1/violations", violationRouter);
	app.use("/api/v1/tasks", taskRouter);
	app.use("/api/v1/incentives", incentiveRouter);
	app.use("/api/v1/kpi", kpiRouter);
	app.use("/api/v1/office-leaves", officeLeaveRouter);

	app.use("/api/v1/admin/dashboard", adminDashboardRouter);
	app.use("/api/v1/admin/auth", adminAuthRouter);
	app.use("/api/v1/admin/attendance", adminAttendanceRouter);
	app.use("/api/v1/admin/attendance-rules", adminAttendanceRuleRouter);
	app.use("/api/v1/admin/holidays", adminHolidayRouter);
	app.use("/api/v1/admin/leave-policies", adminLeavePolicyRouter);
	app.use("/api/v1/admin/leave-requests", adminLeaveRequestRouter);
	app.use("/api/v1/admin/kpi", adminKPIRouter);
	app.use("/api/v1/admin/incentives", adminIncentiveRouter);
	app.use("/api/v1/admin/violations", adminViolationRouter);
	app.use("/api/v1/admin/violation-types", adminViolationTypeRouter);
	app.use("/api/v1/admin/tasks", adminTaskRouter);
	app.use("/api/v1/admin/office-leaves", adminOfficeLeaveRouter);
	app.use("/api/v1/admin/users", adminUserRouter);
	app.use("/api/v1/admin/administrator-users", adminAdministratorUserRouter);
	app.use("/api/v1/admin/worksites", adminWorksiteRouter);
	app.use("/api/v1/admin/roles", adminRoleRouter);

	app.use("/api/v1/debug/attendance", attendanceDebugRouter);
	// Static file serving
	// Public files: accessible without authentication (serves entire public/ directory)
	// This allows access to /uploads, /assets, /docs, etc.
	app.use(express.static(PATHS.PUBLIC.BASE));
	// END END APP ROUTES

	app.use(unifiedValidationErrorHandler);
	app.use(errorMiddleware);

	const server = app.listen(Env.PORT, () => {
		console.log(`Server💻: running at http://localhost:${Env.PORT}`);
	});

	// Graceful shutdown
	process.on("SIGTERM", async () => {
		console.log("🛑 Shutting down gracefully...");

		// Flush Sentry events before shutdown
		await flushSentry(2000);

		server.close(() => {
			console.log("Server terminated");
			process.exit(0);
		});
	});
}

main().catch(console.error);
