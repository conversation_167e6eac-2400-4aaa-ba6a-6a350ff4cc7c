import fs from "node:fs";
import path from "node:path";
import yaml from "yaml";
import "@/shared/lib/zod-extensions"; 
import { generateOpenAPIDocument } from "@/shared/docs/openapi";
import { PATHS } from "@/shared/lib/paths";

async function main() {
	const document = await generateOpenAPIDocument();

	// Tentukan path dasar untuk output
	const outputBaseDir = path.resolve(__dirname, PATHS.PUBLIC.BASE);

	// Hasilkan openapi.yaml
	const openapiPath = path.join(outputBaseDir, "openapi.yaml");
	const openapiContent = yaml.stringify(document);
	fs.writeFileSync(openapiPath, openapiContent, {
		encoding: "utf-8",
	});
	console.log(`OpenAPI document generated at: ${openapiPath}`);

	// Hasilkan api-doc.js
	const apiDocJsPath = path.join(outputBaseDir, "api-doc.js");
	const apiDocJsContent = `
    Scalar.createApiReference('#app', {
      url: '/openapi.yaml',
      proxyUrl: 'https://proxy.scalar.com',
    });
`;
	fs.writeFileSync(apiDocJsPath, apiDocJsContent.trim(), {
		encoding: "utf-8",
	});
	console.log(`API documentation JavaScript generated at: ${apiDocJsPath}`);
}

main().catch(console.error);
