import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { AuthUser } from "@/shared/types";
import type { OfficeLeaveQueryDTO } from "../dtos";
import type {
	AdminCreateOfficeLeaveDTO,
	AdminUpdateOfficeLeaveDTO,
	MarkOfficeLeaveDTO,
} from "../dtos/request";
import * as adminOfficeLeaveService from "../services/admin-office-leave.service";

export const getAllOfficeLeaves = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedQuery = req.validatedQuery as OfficeLeaveQueryDTO;
		const result =
			await adminOfficeLeaveService.getAllOfficeLeaves(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const officeLeaveId = req.params.officeLeaveId as string;

		const result = await adminOfficeLeaveService.getOfficeLeave(officeLeaveId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateOfficeLeaveDTO;

		const result =
			await adminOfficeLeaveService.createOfficeLeave(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const officeLeaveId = req.params.officeLeaveId as string;
		const validatedBody = req.validatedBody as AdminUpdateOfficeLeaveDTO;

		const result = await adminOfficeLeaveService.updateOfficeLeave(
			officeLeaveId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: {
				...result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const markOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as MarkOfficeLeaveDTO;
		await adminOfficeLeaveService.markOfficeLeave({
			id: req.params.officeLeaveId as string,
			payload: validatedBody,
			adminUser: req.user as AuthUser,
		});

		const response = {
			status: "success",
			message: `Berhasil menandai izin keluar sebagai ${validatedBody.isOfficialBusiness ? "Keperluan Kantor" : "Bukan Keperluan Kantor"}`,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminOfficeLeaveService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=users.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
