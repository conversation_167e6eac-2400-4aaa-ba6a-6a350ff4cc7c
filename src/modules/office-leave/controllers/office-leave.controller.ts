import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import { isValidTimezone } from "@/shared/utils";
import type { CreateOfficeLeaveDTO, MarkOfficeLeaveDTO } from "../dtos/request";
import * as officeLeaveService from "../services/office-leave.service";

export const getMyOfficeLeaveHistory = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await officeLeaveService.getMyOfficeLeaveHistory(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				officeLeaves: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getOfficeLeaveListForReview = async (
	_req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await officeLeaveService.getOfficeLeaveListForReview();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				officeLeaves: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getOfficeLeaveDetail = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await officeLeaveService.getOfficeLeaveDetail(
			req.params.officeLeaveId as string,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as CreateOfficeLeaveDTO;
		const timezone =
			req.headers["x-timezone"] &&
			isValidTimezone(req.headers["x-timezone"] as string)
				? (req.headers["x-timezone"] as string)
				: "Asia/Jakarta";
		const result = await officeLeaveService.createOfficeLeave(
			validatedBody,
			req.user as AuthUser,
			timezone,
		);

		const response = {
			status: "success",
			message: "Berhasil membuat izin keluar baru",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const markOfficeLeave = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as MarkOfficeLeaveDTO;
		await officeLeaveService.markOfficeLeave({
			id: req.params.officeLeaveId as string,
			payload: validatedBody,
			user: req.user as AuthUser,
		});

		const response = {
			status: "success",
			message: `Berhasil menandai izin keluar sebagai ${validatedBody.isOfficialBusiness ? "Keperluan Kantor" : "Bukan Keperluan Kantor"}`,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
