import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { authMiddleware, validateBody } from "@/shared/middlewares";
import * as officeLeaveController from "../controllers/office-leave.controller";
import { createOfficeLeaveDTO, markOfficeLeaveDTO } from "../dtos/request";

export const officeLeaveRouter = Router();

officeLeaveRouter.get(
	"/history",
	authMiddleware(AuthStrategy.JWT_USER),
	officeLeaveController.getMyOfficeLeaveHistory,
);

officeLeaveRouter.get(
	"/for-review",
	authMiddleware(AuthStrategy.JWT_USER),
	officeLeaveController.getOfficeLeaveListForReview,
);

officeLeaveRouter.get(
	"/:officeLeaveId",
	authMiddleware(AuthStrategy.JWT_USER),
	officeLeaveController.getOfficeLeaveDetail,
);

officeLeaveRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	validateBody(createOfficeLeaveDTO),
	officeLeaveController.createOfficeLeave,
);

officeLeaveRouter.patch(
	"/:officeLeaveId/mark",
	authMiddleware(AuthStrategy.JWT_USER),
	validateBody(markOfficeLeaveDTO),
	officeLeaveController.markOfficeLeave,
);
