import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminOfficeLeaveController from "../controllers/admin-office-leave.controller";
import { officeLeaveQueryDTO } from "../dtos";
import {
	adminCreateOfficeLeaveDTO,
	adminUpdateOfficeLeaveDTO,
	markOfficeLeaveDTO,
} from "../dtos/request";

export const adminOfficeLeaveRouter = Router();

adminOfficeLeaveRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(officeLeaveQueryDTO),
	adminOfficeLeaveController.getAllOfficeLeaves,
);

adminOfficeLeaveRouter.get(
	"/:officeLeaveId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminOfficeLeaveController.getOfficeLeave,
);

adminOfficeLeaveRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateOfficeLeaveDTO),
	adminOfficeLeaveController.createOfficeLeave,
);

adminOfficeLeaveRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminOfficeLeaveController.bulkAction,
);

adminOfficeLeaveRouter.put(
	"/:officeLeaveId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateOfficeLeaveDTO),
	adminOfficeLeaveController.updateOfficeLeave,
);

adminOfficeLeaveRouter.patch(
	"/:officeLeaveId/mark",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(markOfficeLeaveDTO),
	adminOfficeLeaveController.markOfficeLeave,
);
