import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateOfficeLeaveDTO } from "../dtos/request";
import { officeLeaveResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Office Leave [Admin]",
	method: "put",
	path: "/api/v1/admin/office-leaves/{officeLeaveId}",
	tags: ["office-leave"],
	description: "UPdate officel leave for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			officeLeaveId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateOfficeLeaveDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: officeLeaveResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
