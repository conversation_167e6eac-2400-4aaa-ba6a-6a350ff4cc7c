import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { markOfficeLeaveDTO } from "../dtos/request";

registry.registerPath({
	summary: "Mark Office Leave [Admin]",
	method: "patch",
	path: "/api/v1/admin/office-leaves/{officeLeaveId}/mark",
	tags: ["office-leave"],
	description:
		"Mark office leave as official business or personal (Admin only)",
	security: [{ bearerAuth: [] }],
	parameters: [
		{
			name: "officeLeaveId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Office Leave Id",
		},
	],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: markOfficeLeaveDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
