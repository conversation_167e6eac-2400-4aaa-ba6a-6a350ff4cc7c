import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateOfficeLeaveDTO } from "../dtos/request";
import { officeLeaveResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create Office Leave [Admin]",
	method: "post",
	path: "/api/v1/admin/office-leaves",
	tags: ["office-leave"],
	description: "Create officel leave for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminCreateOfficeLeaveDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: officeLeaveResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
