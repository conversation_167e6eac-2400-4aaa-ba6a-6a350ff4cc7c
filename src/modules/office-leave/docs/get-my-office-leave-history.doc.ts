import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { officeLeaveResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Office Leave History",
	method: "get",
	path: "/api/v1/office-leaves/history",
	tags: ["office-leave"],
	description: "Get current user's office leave history",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							officeLeaves: z.array(officeLeaveResponseDTO),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
