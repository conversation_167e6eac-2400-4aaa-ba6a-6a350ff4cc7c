import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { officeLeaveDTO } from "../dtos";

registry.registerPath({
	summary: "Get Office Leave [Admin]",
	method: "get",
	path: "/api/v1/admin/office-leaves/{officeLeaveId}",
	tags: ["office-leave"],
	description: "Get single office leave for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			officeLeaveId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: officeLeaveDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
