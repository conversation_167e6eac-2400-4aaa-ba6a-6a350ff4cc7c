import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { officeLeaveResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Office Leave Detail",
	method: "get",
	path: "/api/v1/office-leaves/{officeLeaveId}",
	tags: ["office-leave"],
	description: "Get detailed information about a specific office leave",
	security: [{ bearerAuth: [] }],
	request: {},
	parameters: [
		{
			name: "officeLeaveId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Office Leave Id",
		},
	],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: officeLeaveResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
