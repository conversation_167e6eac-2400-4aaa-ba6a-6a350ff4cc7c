import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { officeLeaveResponseDTO } from "../dtos/response";

// GET /api/v1/office-leaves/for-review - Get office leaves for review
registry.registerPath({
	summary: "Get Office Leave For Review (HR)",
	method: "get",
	path: "/api/v1/office-leaves/for-review",
	tags: ["office-leave"],
	description: "Get office leaves that need review/categorization",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							officeLeaves: z.array(officeLeaveResponseDTO),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
