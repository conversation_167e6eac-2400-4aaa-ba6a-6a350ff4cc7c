import z from "zod";

export const adminCreateOfficeLeaveDTO = z.object({
	userId: z.string().meta({
		example: "user_bXn1LnLm8GNqn2OKSE4db",
		description: "ID of the user requesting office leave",
	}),
	title: z.string().nonempty().meta({
		description: "Title/name of the office leave request",
		example: "Client Meeting at Downtown Office",
	}),
	description: z.string().nonempty().meta({
		description: "Description/reason for office leave",
		example: "Meeting with ABC Corp client to discuss Q1 project requirements",
	}),
	startTime: z.iso.time().meta({
		description: "Start time of office leave in ISO format",
		example: "09:00:00",
	}),
	endTime: z.iso.time().meta({
		description: "End time of office leave in ISO format",
		example: "17:00:00",
	}),
	date: z.iso.date().meta({
		example: "2025-01-20",
		description: "Date of office leave (Date format)",
	}),
});
export type AdminCreateOfficeLeaveDTO = z.infer<
	typeof adminCreateOfficeLeaveDTO
>;
