import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import * as z from "zod";
import "@/shared/lib/zod-extensions";

dayjs.extend(customParseFormat);

const TIME_FORMATS = ["HH:mm:ss", "HH:mm"];

export const createOfficeLeaveDTO = z
	.object({
		title: z.string().nonempty().meta({
			description: "Title/name of the office leave request",
			example: "Client Meeting at Downtown Office",
		}),
		description: z.string().nonempty().meta({
			description: "Description/reason for office leave",
			example:
				"Meeting with ABC Corp client to discuss Q1 project requirements",
		}),
		startTime: z.iso.time().meta({
			description: "Start time of office leave in ISO format",
			example: "09:00:00",
		}),
		endTime: z.iso.time().meta({
			description: "End time of office leave in ISO format",
			example: "17:00:00",
		}),
	})
	.refine(
		(data) => {
			const start = dayjs(data.startTime, TIME_FORMATS, true);
			const end = dayjs(data.endTime, TIME_FORMATS, true);

			return start.isValid() && end.isValid() && end.isSameOrAfter(start);
		},
		{
			path: ["endTime"],
			message: "Waktu akhir tidak boleh sebelum waktu mulai.",
		},
	);
export type CreateOfficeLeaveDTO = z.infer<typeof createOfficeLeaveDTO>;
