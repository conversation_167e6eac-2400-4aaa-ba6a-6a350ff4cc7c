import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";
import { OfficeLeaveStatus } from "@/shared/enums";

export const officeLeaveDTO = z
	.object({
		id: z.string().meta({
			example: "ol_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the office leave",
		}),
		userId: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "ID of the user requesting office leave",
		}),
		title: z.string().meta({
			example: "Client Meeting at Downtown Office",
			description: "Title/name of the office leave request",
		}),
		description: z.string().meta({
			example:
				"Meeting with ABC Corp client to discuss Q1 project requirements",
			description: "Description/reason for office leave",
		}),
		status: z.string().meta({
			example: "NEED_REVIEW",
			enum: [OfficeLeaveStatus.NEED_REVIEW, OfficeLeaveStatus.REVIEWED],
		}),
		startTime: z.string().meta({
			example: "09:00:00",
			description: "Start time of office leave (DateTime format)",
		}),
		endTime: z.string().meta({
			example: "17:00:00",
			description: "End time of office leave (DateTime format)",
		}),
		date: z.iso.datetime().meta({
			example: "2025-01-20",
			description: "Date of office leave (Date format)",
		}),
		isOfficialBusiness: z.boolean().nullable().meta({
			example: true,
			description: "Whether this is official business (categorized by HR)",
		}),
		reviewedBy: z.string().nullable().meta({
			example: "hr_manager_123",
			description: "ID of HR who reviewed the office leave",
		}),
		reviewedAt: z.iso.datetime().nullable().meta({
			example: "2025-01-19T14:30:00Z",
			description: "When the office leave was reviewed",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("OfficeLeave");
export type OfficeLeaveDTO = z.infer<typeof officeLeaveDTO>;

export const officeLeaveQueryDTO = tableQueryDTO.extend({
	status: filterFieldDTO.optional(),
	date: filterFieldDTO.optional(),
	isOfficialBusiness: filterFieldDTO.optional(),
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	reviewerName: filterFieldDTO.optional(),
	reviewerEmail: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type OfficeLeaveQueryDTO = z.infer<typeof officeLeaveQueryDTO>;
