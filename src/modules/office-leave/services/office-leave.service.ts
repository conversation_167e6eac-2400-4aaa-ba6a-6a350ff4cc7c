import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { OfficeLeavesId } from "@/database/types/public/OfficeLeaves";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import { OfficeLeaveStatus } from "@/shared/enums";
import {
	BadRequestError,
	ForbiddenError,
	NotFoundError,
} from "@/shared/exceptions";
import type { AuthUser } from "@/shared/types";
import type { CreateOfficeLeaveDTO, MarkOfficeLeaveDTO } from "../dtos/request";
import type { OfficeLeaveResponseDTO } from "../dtos/response";

dayjs.extend(utc);
dayjs.extend(timezone);

export const getMyOfficeLeaveHistory = async (
	user: AuthUser,
): Promise<OfficeLeaveResponseDTO[]> => {
	const officeLeaves = await db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("officeLeaves")
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.where("officeLeaves.userId", "=", user.id as UsersId)
		.orderBy("officeLeaves.createdAt", "desc")
		.execute();

	return officeLeaves.map((officeLeave) => ({
		...officeLeave,
		date: dayjs(officeLeave.date).locale("id").format("D MMMM YYYY"),
	}));
};

export const getOfficeLeaveListForReview = async (): Promise<
	OfficeLeaveResponseDTO[]
> => {
	const officeLeaves = await db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("officeLeaves")
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.where("officeLeaves.reviewedBy", "is", null)
		.execute();

	return officeLeaves.map((officeLeave) => ({
		...officeLeave,
		date: dayjs(officeLeave.date).locale("id").format("D MMMM YYYY"),
	}));
};

export const getOfficeLeaveDetail = async (
	id: string,
): Promise<OfficeLeaveResponseDTO> => {
	const officeLeave = await db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("officeLeaves")
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.where("officeLeaves.id", "=", id as OfficeLeavesId)
		.executeTakeFirst();

	if (!officeLeave) {
		throw new NotFoundError("Izin keluar yang dipilih tidak ditemukan.");
	}

	return {
		...officeLeave,
		date: dayjs(officeLeave.date).locale("id").format("D MMMM YYYY"),
	};
};

export const createOfficeLeave = async (
	payload: CreateOfficeLeaveDTO,
	user: AuthUser,
	timezone = "Asia/Jakarta",
): Promise<OfficeLeaveResponseDTO> => {
	const nowInTz = dayjs().tz(timezone);
	const dateOnly = nowInTz.startOf("day").format("YYYY-MM-DD");

	const newOfficeLeave = await db
		.insertInto("officeLeaves")
		.values({
			id: nanoid() as OfficeLeavesId,
			userId: user.id as UsersId,
			title: payload.title,
			description: payload.description,
			date: dateOnly,
			startTime: payload.startTime, // "08:00"
			endTime: payload.endTime, // "12:00"
			status: OfficeLeaveStatus.NEED_REVIEW,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newOfficeLeave,
		date: dayjs(newOfficeLeave.date).locale("id").format("D MMMM YYYY"),
		userName: user.name,
		userEmail: user.email,
		reviewerName: null,
		reviewerEmail: null,
	};
};

export const markOfficeLeave = async ({
	user,
	id,
	payload,
}: {
	user: AuthUser;
	id: string;
	payload: MarkOfficeLeaveDTO;
}): Promise<void> => {
	const isHR = user.roles.find((role) => role.id === Env.HR_ROLE_ID);
	if (!isHR) {
		throw new ForbiddenError(
			"Anda tidak memiliki akses untuk menandai izin keluar.",
		);
	}

	const officeLeave = await db
		.selectFrom("officeLeaves")
		.select(["id", "reviewedBy"])
		.where("id", "=", id as OfficeLeavesId)
		.executeTakeFirst();

	if (!officeLeave) {
		throw new BadRequestError("Izin keluar tidak ditemukan.");
	}

	if (officeLeave.reviewedBy) {
		throw new BadRequestError("Izin keluar sudah direview.");
	}

	await db
		.updateTable("officeLeaves")
		.set({
			isOfficialBusiness: payload.isOfficialBusiness,
			status: OfficeLeaveStatus.REVIEWED,
			reviewedAt: new Date().toISOString(),
			reviewedBy: user.id as UsersId,
		})
		.where("id", "=", id as OfficeLeavesId)
		.executeTakeFirstOrThrow();
};
