import { parse } from "node:querystring";
import dayjs from "dayjs";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AdministratorUsersId } from "@/database/types/public/AdministratorUsers";
import type {
	OfficeLeaves,
	OfficeLeavesId,
} from "@/database/types/public/OfficeLeaves";
import type { UsersId } from "@/database/types/public/Users";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { OfficeLeaveStatus } from "@/shared/enums";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import type { AuthUser } from "@/shared/types";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type OfficeLeaveDTO,
	type OfficeLeaveQueryDTO,
	officeLeaveQueryDTO,
} from "../dtos";
import type {
	AdminCreateOfficeLeaveDTO,
	AdminUpdateOfficeLeaveDTO,
	MarkOfficeLeaveDTO,
} from "../dtos/request";
import type { OfficeLeaveResponseDTO } from "../dtos/response";

const OFFICE_LEAVE_FILTERABLE_FIELDS: FilterableFields = {
	id: "officeLeaves.id",
	status: "officeLeaves.status",
	date: "officeLeaves.date",
	isOfficialBusiness: "officeLeaves.isOfficialBusiness",
	userName: "users.name",
	userEmail: "users.email",
	reviewerName: "reviewer.name",
	reviewerEmail: "reviewer.email",
	createdAt: "officeLeaves.createdAt",
} as const;

const OFFICE_LEAVE_FIELD_TYPES: FieldTypes = {
	id: "string",
	status: "string",
	date: "date",
	isOfficialBusiness: "boolean",
	userName: "string",
	userEmail: "string",
	reviewerName: "string",
	reviewerEmail: "string",
	createdAt: "date",
} as const;

const OFFICE_LEAVE_SORTABLE_FIELDS: SortableFields = {
	id: "officeLeaves.id",
	status: "officeLeaves.status",
	date: "officeLeaves.date",
	isOfficialBusiness: "officeLeaves.isOfficialBusiness",
	userName: "users.name",
	userEmail: "users.email",
	reviewerName: "reviewer.name",
	reviewerEmail: "reviewer.email",
	createdAt: "officeLeaves.createdAt",
	updatedAt: "officeLeaves.updatedAt",
} as const;

export const getAllOfficeLeaves = async (
	queryParams: OfficeLeaveQueryDTO,
): Promise<{ data: OfficeLeaveResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		status,
		date,
		isOfficialBusiness,
		userName,
		userEmail,
		reviewerName,
		reviewerEmail,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("officeLeaves")
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		]);

	const countQuery = db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		status?: string | string[];
		date?: string | string[];
		isOfficialBusiness?: string | string[];
		userName?: string | string[];
		userEmail?: string | string[];
		reviewerName?: string | string[];
		reviewerEmail?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (status) filters.status = status;
	if (date) filters.date = date;
	if (isOfficialBusiness) filters.isOfficialBusiness = isOfficialBusiness;
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (reviewerName) filters.reviewerName = reviewerName;
	if (reviewerEmail) filters.reviewerEmail = reviewerEmail;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			filterableFields: OFFICE_LEAVE_FILTERABLE_FIELDS,
			fieldTypes: OFFICE_LEAVE_FIELD_TYPES,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: OFFICE_LEAVE_SORTABLE_FIELDS,
		defaultSort: {
			field: "officeLeaves.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [officeLeaves, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedOfficeLeaves = officeLeaves.map((officeLeave) => ({
		...officeLeave,
		date: dayjs(officeLeave.date).locale("id").format("D MMMM YYYY"),
		reviewedAt: officeLeave.reviewedAt
			? dayjs(officeLeave.reviewedAt).locale("id").format("D MMMM YYYY")
			: null,
	}));

	return {
		data: formattedOfficeLeaves,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getOfficeLeave = async (
	officeLeaveId: string,
): Promise<OfficeLeaveDTO> => {
	const existOfficeLeave = await db
		.selectFrom("officeLeaves")
		.selectAll("officeLeaves")
		.where("officeLeaves.id", "=", officeLeaveId as OfficeLeavesId)
		.executeTakeFirst();

	if (!existOfficeLeave) {
		throw new NotFoundError("Izin keluar yang dipilih tidak ditemukan.");
	}

	return existOfficeLeave;
};

export const createOfficeLeave = async (
	payload: AdminCreateOfficeLeaveDTO,
): Promise<OfficeLeaveResponseDTO> => {
	const existUser = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", payload.userId as UsersId)
		.executeTakeFirst();

	if (!existUser) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	const newOfficeLeave = await db
		.insertInto("officeLeaves")
		.values({
			id: nanoid() as OfficeLeavesId,
			...payload,
			status: OfficeLeaveStatus.NEED_REVIEW,
			userId: payload.userId as UsersId,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newOfficeLeave,
		date: dayjs(newOfficeLeave.date).locale("id").format("D MMMM YYYY"),
		userName: existUser.name,
		userEmail: existUser.email,
		reviewerName: null,
		reviewerEmail: null,
	};
};

export const updateOfficeLeave = async (
	officeLeaveId: string,
	payload: AdminUpdateOfficeLeaveDTO,
): Promise<OfficeLeaveResponseDTO> => {
	const existOfficeLeave = await db
		.selectFrom("officeLeaves")
		.selectAll()
		.where("id", "=", officeLeaveId as OfficeLeavesId)
		.executeTakeFirst();

	if (!existOfficeLeave) {
		throw new NotFoundError("Izin keluar yang dipilih tidak ditemukan.");
	}

	let existUser = null;

	if (payload.userId) {
		existUser = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existUser) {
			throw new BadRequestError("User yang dipilih tidak ditemukan");
		}
	}

	const updateOfficeLeaveData: Partial<OfficeLeaves> = {};

	if (payload.userId) updateOfficeLeaveData.userId = payload.userId as UsersId;
	if (payload.title) updateOfficeLeaveData.title = payload.title;
	if (payload.description)
		updateOfficeLeaveData.description = payload.description;
	if (payload.date) updateOfficeLeaveData.date = payload.date;
	if (payload.startTime) updateOfficeLeaveData.startTime = payload.startTime;
	if (payload.endTime) updateOfficeLeaveData.endTime = payload.endTime;

	updateOfficeLeaveData.updatedAt = new Date().toISOString();

	let updatedOfficeLeave = existOfficeLeave;
	if (Object.keys(updateOfficeLeaveData).length > 1) {
		updatedOfficeLeave = await db
			.updateTable("officeLeaves")
			.set(updateOfficeLeaveData)
			.where("id", "=", officeLeaveId as OfficeLeavesId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	const finalUser =
		existUser ||
		(await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", existOfficeLeave.userId)
			.executeTakeFirst());
	const finalReviewer = existOfficeLeave.reviewedBy
		? await db
				.selectFrom("users")
				.select(["id", "name", "email"])
				.where("id", "=", existOfficeLeave.reviewedBy)
				.executeTakeFirst()
		: null;

	return {
		...updatedOfficeLeave,
		date: dayjs(updatedOfficeLeave.date).locale("id").format("D MMMM YYYY"),
		reviewedAt: updatedOfficeLeave.reviewedAt
			? dayjs(updatedOfficeLeave.reviewedAt).locale("id").format("D MMMM YYYY")
			: null,
		userName: finalUser?.name || "",
		userEmail: finalUser?.email || "",
		reviewerName: finalReviewer?.name || null,
		reviewerEmail: finalReviewer?.email || null,
	};
};

export const markOfficeLeave = async ({
	adminUser,
	id,
	payload,
}: {
	adminUser: AuthUser;
	id: string;
	payload: MarkOfficeLeaveDTO;
}): Promise<void> => {
	const officeLeave = await db
		.selectFrom("officeLeaves")
		.select(["id", "reviewedBy"])
		.where("id", "=", id as OfficeLeavesId)
		.executeTakeFirst();

	if (!officeLeave) {
		throw new BadRequestError("Izin keluar tidak ditemukan.");
	}

	if (officeLeave.reviewedBy) {
		throw new BadRequestError("Izin keluar sudah direview.");
	}

	const user = await db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId")
		.select(["users.id"])
		.where("administratorUsers.id", "=", adminUser.id as AdministratorUsersId)
		.executeTakeFirstOrThrow();

	await db
		.updateTable("officeLeaves")
		.set({
			isOfficialBusiness: payload.isOfficialBusiness,
			status: OfficeLeaveStatus.REVIEWED,
			reviewedAt: new Date().toISOString(),
			reviewedBy: user.id as UsersId,
		})
		.where("id", "=", id as OfficeLeavesId)
		.executeTakeFirstOrThrow();
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("officeLeaves")
		.innerJoin("users", "users.id", "officeLeaves.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("officeLeaves")
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			status,
			date,
			isOfficialBusiness,
			userName,
			userEmail,
			reviewerName,
			reviewerEmail,
			createdAt,
		} = officeLeaveQueryDTO.parse(queryObj);

		// searching
		if (search) {
		}

		// filtering
		const filters: {
			status?: string | string[];
			date?: string | string[];
			isOfficialBusiness?: string | string[];
			userName?: string | string[];
			userEmail?: string | string[];
			reviewerName?: string | string[];
			reviewerEmail?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (status) filters.status = status;
		if (date) filters.date = date;
		if (isOfficialBusiness) filters.isOfficialBusiness = isOfficialBusiness;
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (reviewerName) filters.reviewerName = reviewerName;
		if (reviewerEmail) filters.reviewerEmail = reviewerEmail;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: OFFICE_LEAVE_FILTERABLE_FIELDS,
			fieldTypes: OFFICE_LEAVE_FIELD_TYPES,
		});

		// sorting
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: OFFICE_LEAVE_SORTABLE_FIELDS,
			defaultSort: {
				field: "officeLeaves.createdAt",
				direction: "desc",
			},
		});

		// apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as OfficeLeavesId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const officeLeaves = await sortedQuery.execute();

			const csv = await createCsv(officeLeaves);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"officeLeaves.id",
			"in",
			selection.selectedIds as OfficeLeavesId[],
		);
		const users = await query.execute();

		if (action === "export") {
			const csv = await createCsv(users);

			return csv;
		}
	}

	return null;
};
