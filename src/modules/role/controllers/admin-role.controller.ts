import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO, GetTreeNodeResponseDTO } from "@/shared/dtos";
import type { AdminUpdateRoleDTO, RoleQueryDTO } from "../dtos";
import type { AdminCreateRoleDTO } from "../dtos/request";
import * as adminRoleService from "../services/admin-role.service";

export const getAllRole = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedQuery = req.validatedQuery as RoleQueryDTO;

		const result = await adminRoleService.getAllRole(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getRole = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const roleId = req.params.roleId as string;

		const result = await adminRoleService.getRole(roleId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getRoleOptions = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const excludeIdQuery = req.query.excludeId;
		const excludeId =
			excludeIdQuery && typeof excludeIdQuery === "string"
				? excludeIdQuery
				: undefined;

		const result = await adminRoleService.getRoleOptions(excludeId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getSupervisorOptions = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const excludeIdQuery = req.query.excludeId;
		const excludeId =
			excludeIdQuery && typeof excludeIdQuery === "string"
				? excludeIdQuery
				: undefined;
		const roleId = req.params.roleId as string;

		const result = await adminRoleService.getSupervisorOptions(
			roleId,
			excludeId,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getRoleTree = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await adminRoleService.getRoleTree();

		const response: GetTreeNodeResponseDTO = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createRole = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateRoleDTO;
		const result = await adminRoleService.createRole(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateRole = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const roleId = req.params.roleId as string;
		const validatedBody = req.validatedBody as AdminUpdateRoleDTO;
		const result = await adminRoleService.updateRole(roleId, validatedBody);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminRoleService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=roles.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
