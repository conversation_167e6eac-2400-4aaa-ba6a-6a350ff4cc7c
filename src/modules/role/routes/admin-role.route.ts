import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminRoleController from "../controllers/admin-role.controller";
import { adminUpdateRoleDTO, roleQueryDTO } from "../dtos";
import { adminCreateRoleDTO } from "../dtos/request";

export const adminRoleRouter = Router();

adminRoleRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(roleQueryDTO),
	adminRoleController.getAllRole,
);

adminRoleRouter.get("/options", adminRoleController.getRoleOptions);

adminRoleRouter.get(
	"/tree",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminRoleController.getRoleTree,
);

adminRoleRouter.get(
	"/:roleId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminRoleController.getRole,
);

adminRoleRouter.get(
	"/:roleId/supervisors/options",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminRoleController.getSupervisorOptions,
);

adminRoleRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateRoleDTO),
	adminRoleController.createRole,
);

adminRoleRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminRoleController.bulkAction,
);

adminRoleRouter.put(
	"/:roleId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateRoleDTO),
	adminRoleController.updateRole,
);
