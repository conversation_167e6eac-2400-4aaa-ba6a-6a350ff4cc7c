import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { Roles, RolesId } from "@/database/types/public/Roles";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
	TreeNodeDTO,
} from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type AdminUpdateRoleDTO,
	type RoleDTO,
	type RoleQueryDTO,
	roleQueryDTO,
} from "../dtos";
import type { AdminCreateRoleDTO } from "../dtos/request";
import type { RoleResponseDTO } from "../dtos/response";

const ROLE_FILTERABLE_FIELDS: FilterableFields = {
	id: "roles.id",
	name: "roles.name",
	createdAt: "roles.createdAt",
} as const;

const ROLE_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	createdAt: "date",
} as const;

const ROLE_SORTABLE_FIELDS: SortableFields = {
	id: "roles.id",
	name: "roles.name",
	parentName: "parent.name",
	createdAt: "roles.createdAt",
} as const;

export const getAllRole = async (
	queryParams: RoleQueryDTO,
): Promise<{ data: RoleResponseDTO[]; meta: ResponseMetaDTO }> => {
	const { pageIndex, pageSize, search, sort, name, createdAt } = queryParams;

	let query = db
		.selectFrom("roles")
		.leftJoin("roles as parent", "parent.id", "roles.parentId")
		.selectAll("roles")
		.select("parent.name as parentName");
	let countQuery = db
		.selectFrom("roles")
		.leftJoin("roles as parent", "parent.id", "roles.parentId");

	// searching
	if (search) {
		query = query.where("name", "ilike", search);
		countQuery = countQuery.where("name", "ilike", search);
	}

	// filtering
	const filters: { name?: string | string[]; createdAt?: string | string[] } =
		{};
	if (name) filters.name = name;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: ROLE_FIELD_TYPES,
			filterableFields: ROLE_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: ROLE_SORTABLE_FIELDS,
		defaultSort: {
			field: "roles.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [roles, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: roles,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getRole = async (roleId: string): Promise<RoleDTO> => {
	const existRole = await db
		.selectFrom("roles")
		.selectAll()
		.where("id", "=", roleId as RolesId)
		.executeTakeFirst();

	if (!existRole) {
		throw new NotFoundError("Role/jabatan tidak ditemukan");
	}

	return existRole;
};

export const getRoleOptions = async (
	excludeId?: string,
): Promise<OptionItemDTO[]> => {
	const roles = await db
		.selectFrom("roles")
		.select(["id as value", "name as label"])
		.execute();

	const filteredRoles = excludeId
		? roles.filter((role) => role.value !== excludeId)
		: roles;

	return filteredRoles;
};

export const getSupervisorOptions = async (
	roleId: string,
	excludeId?: string,
): Promise<OptionItemDTO[]> => {
	const role = await db
		.selectFrom("roles")
		.where("id", "=", roleId as RolesId)
		.select(["id", "parentId"])
		.executeTakeFirst();

	if (!role) {
		throw new NotFoundError("Role tidak ditemukan");
	}

	if (!role.parentId) {
		return [] as { label: string; value: string }[];
	}

	const supervisorsInHierarchy = await db
		.selectFrom("userHierarchies")
		.where("roleId", "=", role.parentId as RolesId)
		.select(["id", "userId"])
		.execute();

	const filteredSupervisorsInHierarchy = excludeId
		? supervisorsInHierarchy.filter((item) => item.userId !== excludeId)
		: supervisorsInHierarchy;

	if (filteredSupervisorsInHierarchy.length < 1) {
		return [] as { label: string; value: string }[];
	}

	const users = await db
		.selectFrom("users")
		.where(
			"id",
			"in",
			filteredSupervisorsInHierarchy.map((item) => item.userId),
		)
		.select(["id", "name", "email"])
		.execute();

	const formattedUsers = users.map((user) => ({
		label: `${user.name} - ${user.email}`,
		value: user.id,
	}));

	return formattedUsers;
};

export const getRoleTree = async (): Promise<TreeNodeDTO[]> => {
	const roles = await db
		.selectFrom("roles")
		.select(["id", "name", "parentId"])
		.execute();

	const roleMap: Record<
		string,
		TreeNodeDTO & { id: string; parentId: string | null }
	> = {};

	for (const role of roles) {
		roleMap[role.id] = {
			id: role.id,
			name: role.name,
			children: [],
			attributes: {},
			parentId: role.parentId ?? null,
		};
	}

	const roots: (TreeNodeDTO & { id: string; parentId: string | null })[] = [];

	for (const role of Object.values(roleMap)) {
		if (role.parentId && roleMap[role.parentId]) {
			roleMap?.[role.parentId]?.children?.push(role);
		} else {
			roots.push(role);
		}
	}

	const stripParentId = (
		node: TreeNodeDTO & { id: string; parentId: string | null },
	): TreeNodeDTO =>
		({
			id: node.id,
			name: node.name,
			children: (
				node.children as (TreeNodeDTO & {
					id: string;
					parentId: string | null;
				})[]
			)?.map(stripParentId),
			attributes: node.attributes,
		}) as TreeNodeDTO;

	return roots.map(stripParentId);
};

export const createRole = async (
	payload: AdminCreateRoleDTO,
): Promise<RoleResponseDTO> => {
	let parentRole = null;
	if (payload.parentId) {
		parentRole = await db
			.selectFrom("roles")
			.select(["id", "name"])
			.where("id", "=", payload.parentId as RolesId)
			.executeTakeFirst();

		if (!parentRole) {
			throw new BadRequestError("Parent role yang dipilih tidak ditemukan");
		}
	}

	const existRole = await db
		.selectFrom("roles")
		.select("id")
		.where("name", "=", payload.name)
		.executeTakeFirst();

	if (existRole) {
		throw new BadRequestError("Jabatan dengan nama yang sama sudah ada");
	}

	const newRole = await db
		.insertInto("roles")
		.values({
			id: nanoid() as RolesId,
			...payload,
			parentId: (payload.parentId as RolesId) ?? null,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newRole,
		parentName: parentRole?.name || null,
	};
};

export const updateRole = async (
	roleId: string,
	payload: AdminUpdateRoleDTO,
): Promise<RoleResponseDTO> => {
	const existRole = await db
		.selectFrom("roles")
		.selectAll()
		.where("id", "=", roleId as RolesId)
		.executeTakeFirst();

	if (!existRole) {
		throw new NotFoundError("Jabatan yang dipilih tidak ditemukan");
	}

	// Validasi parentId baru jika diberikan
	if (payload.parentId) {
		const parentExists = await db
			.selectFrom("roles")
			.select("id")
			.where("id", "=", payload.parentId as RolesId)
			.executeTakeFirst();

		if (!parentExists) {
			throw new BadRequestError("Parent role yang dipilih tidak ditemukan");
		}
	}

	// Siapkan data untuk update
	const roleUpdateData: Partial<Roles> = {};
	if (payload.name) roleUpdateData.name = payload.name;
	if (payload.description) roleUpdateData.description = payload.description;
	if (payload.parentId !== undefined)
		roleUpdateData.parentId = payload.parentId as RolesId;
	roleUpdateData.updatedAt = new Date().toISOString();

	// Update jika ada perubahan (selain updatedAt)
	let updatedRole = existRole;
	if (Object.keys(roleUpdateData).length > 1) {
		updatedRole = await db
			.updateTable("roles")
			.set(roleUpdateData)
			.where("id", "=", roleId as RolesId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	// Ambil parentName hanya sekali dari parentId efektif
	const effectiveParentId = payload.parentId ?? existRole.parentId;
	let parentName: string | null = null;

	if (effectiveParentId) {
		const parentRole = await db
			.selectFrom("roles")
			.select(["id", "name"])
			.where("id", "=", effectiveParentId as RolesId)
			.executeTakeFirst();

		parentName = parentRole?.name ?? null;
	}

	return {
		...updatedRole,
		parentName,
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("roles")
		.leftJoin("roles as parent", "parent.id", "roles.parentId")
		.selectAll("roles")
		.select("parent.name as parentName");

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, createdAt } = roleQueryDTO.parse(queryObj);

		// search
		if (search) {
			query = query.where((eb) => eb("roles.name", "ilike", `%${search}%`));
		}

		// filters
		const filters: {
			name?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: ROLE_FILTERABLE_FIELDS,
			fieldTypes: ROLE_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: ROLE_SORTABLE_FIELDS,
			defaultSort: {
				field: "createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as RolesId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where("roles.id", "in", selection.selectedIds as RolesId[]);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
