import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const roleDTO = z
	.object({
		id: z.string().meta({
			example: "AoH_WyFs2SBqlmob081uI",
			description: "Unique identifier for the role",
		}),
		name: z.string().meta({
			example: "Director",
			description: "Name of the role",
		}),
		description: z.string().nullable().meta({
			example: "Role for directors",
			description: "Description of the role",
		}),
		parentId: z.string().nullable().meta({
			example: "6jMyaEVAmlxeWHCzNfLPi",
			description: "ID of the parent role (null for top level)",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Role");
export type RoleDTO = z.infer<typeof roleDTO>;

export const roleQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type RoleQueryDTO = z.infer<typeof roleQueryDTO>;
