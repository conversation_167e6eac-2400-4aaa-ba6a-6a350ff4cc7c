import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO, optionItemDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get Supervisor For Specific Role [Admin]",
	method: "get",
	path: "/api/v1/admin/roles/{roleId}/supervisors/options",
	tags: ["role"],
	description: "Get list of supervisor users for a specific role (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		query: z.object({
			excludeId: z.string().optional(),
		}),
	},
	parameters: [
		{
			name: "roleId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Role Id",
		},
	],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(optionItemDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
