import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { roleResponseDTO } from "../dtos/response";
import z from "zod";

registry.registerPath({
	summary: "Get All Role [Admin]",
	method: "get",
	path: "/api/v1/admin/roles",
	tags: ["role"],
	description:
		"Get all roles for tabular data, support sorting, filtering, paginating, and searching",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(roleResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
