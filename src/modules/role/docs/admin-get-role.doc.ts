import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { roleDTO } from "../dtos";

registry.registerPath({
	summary: "Get Role [Admin]",
	method: "get",
	path: "/api/v1/admin/roles/{roleId}",
	tags: ["role"],
	description: "Get role single role (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			roleId: z.string(),
		}),
	},
	parameters: [],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: roleDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
