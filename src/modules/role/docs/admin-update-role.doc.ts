import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateRoleDTO } from "../dtos";
import { roleResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Role [Admin]",
	method: "put",
	path: "/api/v1/admin/roles/{roleId}",
	tags: ["user"],
	description: "Update role (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			roleId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateRoleDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: roleResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
