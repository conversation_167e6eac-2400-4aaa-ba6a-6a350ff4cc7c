import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateRoleDTO } from "../dtos/request";
import { roleResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Role [Admin]",
	method: "post",
	path: "/api/v1/admin/roles",
	tags: ["role"],
	description: "Create a new role (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminCreateRoleDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: roleResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
