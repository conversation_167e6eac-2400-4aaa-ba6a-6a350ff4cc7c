import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO, optionItemDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get Role Options [Admin]",
	method: "get",
	path: "/api/v1/admin/roles/options",
	tags: ["role"],
	description: "Get role options for dropdown/select data (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {},
	parameters: [],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(optionItemDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
