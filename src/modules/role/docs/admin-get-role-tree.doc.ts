import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { getTreeNodeResponseDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get Role Tree [Admin]",
	method: "get",
	path: "/api/v1/admin/roles/tree",
	tags: ["role"],
	description: "Get role tree nodes (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {},
	parameters: [],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: getTreeNodeResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
