import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveQuotaResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Leave Request Quota",
	method: "get",
	path: "/api/v1/leave-requests/quota",
	tags: ["leave"],
	description: "Get current user's leave quota for all leave types",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "Leave quota retrieved successfully",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveQuotaResponseDTO,
					})
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
