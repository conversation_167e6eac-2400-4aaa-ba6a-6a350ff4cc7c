import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveRequestDTO } from "../dtos";

registry.registerPath({
	summary: "Get Leave Request [Admin]",
	method: "get",
	path: "/api/v1/admin/leave-requests/{leaveRequestId}",
	tags: ["leave"],
	description: "Get single leave request for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			leaveRequestId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveRequestDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
