import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { reviewLeaveRequestDTO } from "../dtos/request";

registry.registerPath({
	summary: "Review Leave Request",
	method: "patch",
	path: "/api/v1/admin/leave-requests/{leaveRequestId}/review",
	tags: ["leave"],
	description: "Review leave request for HR",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			leaveRequestId: z.string(),
		}),
		body: {
			content: {
				"application/json": {
					schema: reviewLeaveRequestDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
