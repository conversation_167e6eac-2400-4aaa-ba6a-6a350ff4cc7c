import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveRequestResponseDTO } from "../dtos/response";

// POST /api/v1/leave-requests - Create new leave request
registry.registerPath({
	summary: "Create Leave Request",
	method: "post",
	path: "/api/v1/leave-requests",
	tags: ["leave"],
	description: "Create a new leave request with optional document upload",
	security: [{ bearerAuth: [] }],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: ["leavePolicyId", "startDate", "endDate", "description"],
					properties: {
						leavePolicyId: {
							type: "string",
							description: "ID of the leave policy to apply for",
							example: "Qah4cccEK0MsspUJ9ywAy",
						},
						startDate: {
							type: "string",
							format: "date",
							description: "Start date of leave in YYYY-MM-DD format",
							example: "2025-01-15",
						},
						endDate: {
							type: "string",
							format: "date",
							description: "End date of leave in YYYY-MM-DD format",
							example: "2025-01-17",
						},
						description: {
							type: "string",
							description: "Reason or description for the leave request",
							example: "Family vacation",
						},
						document: {
							type: "string",
							format: "binary",
							description:
								"Optional supporting document (PDF, DOC, DOCX, max 5MB)",
						},
					},
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveRequestResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
