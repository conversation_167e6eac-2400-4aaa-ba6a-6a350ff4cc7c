import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leavePolicyResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Leave Policy [Admin]",
	method: "get",
	path: "/api/v1/admin/leave-policies/{leavePolicyId}",
	tags: ["leave"],
	description: "Get single leave policy for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			leavePolicyId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leavePolicyResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
