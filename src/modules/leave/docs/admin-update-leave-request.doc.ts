import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveRequestResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Leave Request [Admin]",
	method: "put",
	path: "/api/v1/admin/leave-requests/{leaveRequestId}",
	tags: ["leave"],
	description: "Create new leave request for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			leaveRequestId: z.string(),
		}),
	},
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: [],
					properties: {
						userId: {
							type: "string",
							description: "ID of the user requesting the leave",
							example: "Qah4cccEK0MsspUJ9ywAy",
						},
						leavePolicyId: {
							type: "string",
							description: "ID of the leave policy to apply for",
							example: "Qah4cccEK0MsspUJ9ywAy",
						},
						startDate: {
							type: "string",
							format: "date",
							description: "Start date of leave in YYYY-MM-DD format",
							example: "2025-01-15",
						},
						endDate: {
							type: "string",
							format: "date",
							description: "End date of leave in YYYY-MM-DD format",
							example: "2025-01-17",
						},
						description: {
							type: "string",
							description: "Reason or description for the leave request",
							example: "Family vacation",
						},
						document: {
							type: "string",
							format: "binary",
							description:
								"Optional supporting document (PDF, DOC, DOCX, max 5MB)",
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveRequestResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
