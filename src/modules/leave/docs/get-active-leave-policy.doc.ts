import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leavePolicyResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Active Leave Policy",
	method: "get",
	path: "/api/v1/leave-policies",
	tags: ["leave"],
	security: [{ bearerAuth: [] }],
	description: "Get list of active leave policies",
	responses: {
		200: {
			description: "Leave policies retrieved successfully",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leavePolicyResponseDTO,
					}),
				},
			},
		},
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
