import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { leaveRequestResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Leave Request [Admin]",
	method: "get",
	path: "/api/v1/admin/leave-requests",
	tags: ["leave"],
	description:
		"Get all leave request for tabular data, support sorting, filtering, paginating, and searching",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(leaveRequestResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
