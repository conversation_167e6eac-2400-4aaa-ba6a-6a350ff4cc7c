import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveRequestResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Latest Leave Request",
	method: "get",
	path: "/api/v1/leave-requests/latest",
	tags: ["leave"],
	description: "Get current user's latest leave requests",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "Leave requests retrieved successfully",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveRequestResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
