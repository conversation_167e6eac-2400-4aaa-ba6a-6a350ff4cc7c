import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { leaveRequestResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Leave Request History",
	method: "get",
	path: "/api/v1/leave-requests/history",
	tags: ["leave"],
	description: "Get current user's leave request history",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "Leave request history retrieved successfully",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leaveRequestResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
