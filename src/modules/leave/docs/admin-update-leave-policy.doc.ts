import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateLeavePolicyDTO } from "../dtos/request";
import { leavePolicyResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Leave Policy [Admin]",
	method: "put",
	path: "/api/v1/admin/leave-policies/{leavePolicyId}",
	tags: ["leave"],
	description: "Update leave policy for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateLeavePolicyDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: leavePolicyResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
