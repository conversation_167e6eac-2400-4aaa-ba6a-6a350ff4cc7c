import dayjs, { type Dayjs } from "dayjs";
import isBetweenPlugin from "dayjs/plugin/isBetween.js";
import isSameOrBeforePlugin from "dayjs/plugin/isSameOrBefore.js";
import { db } from "@/database/connection";
import { toDateOnlyString } from "@/shared/utils/date";

dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(isBetweenPlugin);

export const _findNextWorkingDay = async (
	afterDate: dayjs.Dayjs,
): Promise<dayjs.Dayjs> => {
	const holidaysAfterDate = await db
		.selectFrom("holidays")
		.select(["date"])
		.where("date", ">", toDateOnlyString(afterDate))
		.execute();

	const holidayDates = new Set(
		holidaysAfterDate.map((h) => dayjs(h.date).format("YYYY-MM-DD")),
	);

	let date = afterDate.clone().add(1, "day");
	while (true) {
		const dayOfWeek = date.day();
		const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
		const isHoliday = holidayDates.has(date.format("YYYY-MM-DD"));

		if (!isWeekend && !isHoliday) {
			break;
		}
		date = date.add(1, "day");
	}

	return date;
};

export const _checkIsWorkingDay = async (
	date: dayjs.Dayjs,
): Promise<boolean> => {
	const day = date.format("dddd").toLowerCase();

	const workingDays = ["monday", "tuesday", "wednesday", "thursday", "friday"];

	if (!workingDays.includes(day)) return false;

	const isHoliday = await db
		.selectFrom("holidays")
		.select(["id"])
		.where("date", "=", toDateOnlyString(date))
		.executeTakeFirst();

	return !isHoliday;
};

export const _calculateEffectiveLeaveDays = async (
	startDate: Dayjs,
	endDate: Dayjs,
): Promise<number> => {
	const holidaysBetweenDates = await db
		.selectFrom("holidays")
		.select(["date"])
		.where((eb) =>
			eb.between(
				"date",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const holidayDates = new Set(
		holidaysBetweenDates.map((h) => dayjs(h.date).format("YYYY-MM-DD")),
	);

	const start = dayjs(startDate);
	const end = dayjs(endDate);

	let effectiveDays = 0;
	for (
		let date = start.clone();
		date.isSameOrBefore(end, "day");
		date = date.add(1, "day")
	) {
		const dayOfWeek = date.day();
		const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
		const isHoliday = holidayDates.has(date.format("YYYY-MM-DD"));

		if (!isWeekend && !isHoliday) {
			effectiveDays++;
		}
	}

	return effectiveDays;
};
