import { parse } from "node:querystring";
import dayjs from "dayjs";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AdministratorUsersId } from "@/database/types/public/AdministratorUsers";
import type { LeavePoliciesId } from "@/database/types/public/LeavePolicies";
import type {
	LeaveRequests,
	LeaveRequestsId,
} from "@/database/types/public/LeaveRequests";
import type { LeaveUsagesId } from "@/database/types/public/LeaveUsages";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { LeaveRequestStatus } from "@/shared/enums";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import type { AuthUser } from "@/shared/types";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type LeaveRequestDTO,
	type LeaveRequestQueryDTO,
	leaveRequestQueryDTO,
} from "../dtos";
import type {
	AdminCreateLeaveRequestDTO,
	AdminUpdateLeaveRequestDTO,
	ReviewLeaveRequestDTO,
} from "../dtos/request";
import type { LeaveRequestResponseDTO } from "../dtos/response";
import {
	_calculateEffectiveLeaveDays,
	_checkIsWorkingDay,
	_findNextWorkingDay,
} from "./shared-leave-request.service";

const LEAVE_REQUEST_FILTERABLE_FIELDS: FilterableFields = {
	id: "leaveRequests.id",
	name: "leavePolicies.name",
	isCountedAsPresent: "leavePolicies.isCountedAsPresent",
	startDate: "leaveRequests.startDate",
	endDate: "leaveRequests.endDate",
	status: "leaveRequests.status",
	userName: "users.name",
	userEmail: "users.email",
	reviewerName: "reviewer.name",
	reviewerEmail: "reviewer.email",
	createdAt: "leaveRequests.createdAt",
} as const;

const LEAVE_REQUEST_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	isCountedAsPresent: "boolean",
	startDate: "date",
	endDate: "date",
	status: "string",
	userName: "string",
	userEmail: "string",
	reviewerName: "string",
	reviewerEmail: "string",
	createdAt: "date",
} as const;

const LEAVE_REQUEST_SORTABLE_FIELDS: SortableFields = {
	id: "leaveRequests.id",
	name: "leavePolicies.name",
	isCountedAsPresent: "leavePolicies.isCountedAsPresent",
	startDate: "leaveRequests.startDate",
	endDate: "leaveRequests.endDate",
	status: "leaveRequests.status",
	userName: "users.name",
	userEmail: "users.email",
	reviewerName: "reviewer.name",
	reviewerEmail: "reviewer.email",
	createdAt: "leaveRequests.createdAt",
	updatedAt: "leaveRequests.updatedAt",
} as const;

export const getAllLeaveRequest = async (
	queryParams: LeaveRequestQueryDTO,
): Promise<{ data: LeaveRequestResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		name,
		isCountedAsPresent,
		status,
		startDate,
		endDate,
		userName,
		userEmail,
		reviewerName,
		reviewerEmail,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("leaveRequests")
		.select(["leavePolicies.name", "leavePolicies.isCountedAsPresent"])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		]);

	const countQuery = db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		name?: string | string[];
		isCountedAsPresent?: string | string[];
		status?: string | string[];
		startDate?: string | string[];
		endDate?: string | string[];
		userName?: string | string[];
		userEmail?: string | string[];
		reviewerName?: string | string[];
		reviewerEmail?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (isCountedAsPresent) filters.isCountedAsPresent = isCountedAsPresent;
	if (startDate) filters.startDate = startDate;
	if (endDate) filters.endDate = endDate;
	if (status) filters.status = status;
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (reviewerName) filters.reviewerName = reviewerName;
	if (reviewerEmail) filters.reviewerEmail = reviewerEmail;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			filterableFields: LEAVE_REQUEST_FILTERABLE_FIELDS,
			fieldTypes: LEAVE_REQUEST_FIELD_TYPES,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: LEAVE_REQUEST_SORTABLE_FIELDS,
		defaultSort: {
			field: "leaveRequests.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [leaveRequests, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedLeaveRequests = leaveRequests.map((leaveRequest) => ({
		...leaveRequest,
		documentUrl: leaveRequest.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(leaveRequest.documentUrl))}`
			: null,
		startDate: dayjs(leaveRequest.startDate).locale("id").format("D MMMM YYYY"),
		endDate: dayjs(leaveRequest.endDate).locale("id").format("D MMMM YYYY"),
		reviewedAt: dayjs(leaveRequest.reviewedAt)
			.locale("id")
			.format("D MMMM YYYY"),
	}));

	return {
		data: formattedLeaveRequests,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getLeaveRequest = async (
	leaveRequestId: string,
): Promise<LeaveRequestDTO> => {
	const existLeaveRequest = await db
		.selectFrom("leaveRequests")
		.selectAll()
		.where("id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirst();

	if (!existLeaveRequest) {
		throw new NotFoundError("Cuti tidak ditemukan");
	}

	return existLeaveRequest;
};

export const createLeaveRequest = async (
	payload: AdminCreateLeaveRequestDTO,
): Promise<LeaveRequestResponseDTO> => {
	const [existUser, startDateIsWorkingDay] = await Promise.all([
		db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst(),
		_checkIsWorkingDay(dayjs(payload.startDate)),
	]);

	if (!existUser) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	if (!startDateIsWorkingDay) {
		throw new BadRequestError("Tanggal mulai cuti harus berada di hari kerja.");
	}

	const leaveUsage = await db
		.selectFrom("leaveUsages")
		.innerJoin("leavePolicies", "leavePolicies.id", "leaveUsages.leavePolicyId")
		.select([
			"leaveUsages.id as leaveUsageId",
			"leaveUsages.used",
			"leaveUsages.userId",
			"leavePolicies.id as leavePolicyId",
			"leavePolicies.name",
			"leavePolicies.quota",
			"leavePolicies.isCountedAsPresent",
		])
		.where("leavePolicyId", "=", payload.leavePolicyId as LeavePoliciesId)
		.where("userId", "=", payload.userId as UsersId)
		.executeTakeFirst();

	if (!leaveUsage) {
		throw new BadRequestError(
			"Quota cuti user yang dipilih tidak ditemukan, segera periksa data user tersebut.",
		);
	}

	const existingLeaves = await db
		.selectFrom("leaveRequests")
		.select(["id", "startDate", "endDate"])
		.where("userId", "=", payload.userId as UsersId)
		.where("status", "in", ["PENDING", "APPROVED"])
		.execute();

	const newStart = dayjs(payload.startDate);
	const newEnd = dayjs(payload.endDate);

	// Cek overlap dengan cuti yang sudah ada
	for (const leave of existingLeaves) {
		const existingStart = dayjs(leave.startDate);
		const existingEnd = dayjs(leave.endDate);

		const isOverlap =
			newStart.isBetween(existingStart, existingEnd, "day", "[]") ||
			newEnd.isBetween(existingStart, existingEnd, "day", "[]") ||
			existingStart.isBetween(newStart, newEnd, "day", "[]");

		if (isOverlap) {
			throw new BadRequestError(
				`Anda sudah memiliki cuti pada tanggal ${dayjs(leave.startDate).format("D MMM YYYY")} sampai ${dayjs(leave.endDate).format("D MMM YYYY")}. Silakan pilih tanggal lain yang tidak bertabrakan.`,
			);
		}
	}

	// Pastikan cuti baru dimulai pada hari kerja setelah cuti terakhir yang masih aktif.
	if (existingLeaves.length > 0) {
		const latestEndDate = existingLeaves.reduce((latest, leave) => {
			const leaveEnd = dayjs(leave.endDate);
			return leaveEnd.isAfter(latest) ? leaveEnd : latest;
		}, dayjs("1900-01-01"));

		if (latestEndDate.isValid()) {
			const nextWorkingDay = await _findNextWorkingDay(latestEndDate);

			if (newStart.isBefore(nextWorkingDay, "day")) {
				throw new BadRequestError(
					`Tanggal mulai cuti tidak valid. Cuti baru hanya dapat dimulai pada hari kerja setelah cuti terakhir, yaitu ${nextWorkingDay.locale("id").format("D MMMM YYYY")}.`,
				);
			}
		}
	}

	// Hitung jumlah hari cuti efektif
	const effectiveLeaveDays = await _calculateEffectiveLeaveDays(
		dayjs(payload.startDate),
		dayjs(payload.endDate),
	);

	const remaining = leaveUsage.quota - leaveUsage.used;
	if (effectiveLeaveDays > remaining) {
		throw new BadRequestError(
			`Quota cuti tidak mencukupi. Diperlukan ${effectiveLeaveDays} hari, tetapi sisa quota anda hanya ${remaining} hari.`,
		);
	}

	const result = await db.transaction().execute(async (trx) => {
		const newLeaveRequest = await trx
			.insertInto("leaveRequests")
			.values({
				id: nanoid() as LeaveRequestsId,
				userId: payload.userId as UsersId,
				leaveUsageId: leaveUsage.leaveUsageId,
				leavePolicyId: leaveUsage.leavePolicyId,
				startDate: payload.startDate,
				endDate: payload.endDate,
				reason: payload.description,
				status: "PENDING",
				effectiveLeaveDays,
				documentUrl: payload.document || null,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.returningAll()
			.executeTakeFirstOrThrow();

		await trx
			.updateTable("leaveUsages")
			.set({
				used: leaveUsage.used + effectiveLeaveDays,
			})
			.where("id", "=", leaveUsage.leaveUsageId)
			.execute();

		return newLeaveRequest;
	});

	return {
		...result,
		documentUrl: result.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(result.documentUrl))}`
			: null,
		name: leaveUsage.name,
		isCountedAsPresent: leaveUsage.isCountedAsPresent,
		userName: existUser.name,
		userEmail: existUser.email,
		reviewerName: null,
		reviewerEmail: null,
	};
};

export const updateLeaveRequest = async (
	leaveRequestId: string,
	payload: AdminUpdateLeaveRequestDTO,
): Promise<LeaveRequestResponseDTO> => {
	const existLeaveRequest = await db
		.selectFrom("leaveRequests")
		.selectAll()
		.where("id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirst();

	if (!existLeaveRequest) {
		throw new NotFoundError("Cuti tidak ditemukan");
	}

	let existUser = null;
	if (payload.userId) {
		existUser = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existUser) {
			throw new BadRequestError("User yang dipilih tidak ditemukan");
		}
	}

	let existLeavePolicy = null;
	let existLeaveUsage = null;
	if (payload.leavePolicyId) {
		existLeavePolicy = await db
			.selectFrom("leavePolicies")
			.select(["id", "name", "isCountedAsPresent", "quota"])
			.where("id", "=", payload.leavePolicyId as LeavePoliciesId)
			.executeTakeFirst();

		if (!existLeavePolicy) {
			throw new BadRequestError("Jenis cuti yang dipilih tidak ditemukan");
		}

		existLeaveUsage = await db
			.selectFrom("leaveUsages")
			.select(["id", "used"])
			.where("leavePolicyId", "=", payload.leavePolicyId as LeavePoliciesId)
			.where("userId", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existLeaveUsage) {
			throw new BadRequestError(
				"Quota cuti untuk user yang dipilih tidak ditemukan",
			);
		}
	}

	if (payload.startDate) {
		const startDateIsWorkingDay = await _checkIsWorkingDay(
			dayjs(payload.startDate),
		);

		if (!startDateIsWorkingDay) {
			throw new BadRequestError(
				"Tanggal mulai cuti harus berada di hari kerja.",
			);
		}
	}

	const updateLeaveRequestData: Partial<LeaveRequests> = {};

	if (payload.userId) updateLeaveRequestData.userId = payload.userId as UsersId;
	if (payload.leavePolicyId) {
		updateLeaveRequestData.leavePolicyId =
			payload.leavePolicyId as LeavePoliciesId;
		updateLeaveRequestData.leaveUsageId = existLeaveUsage?.id as LeaveUsagesId;
	}
	if (payload.startDate) updateLeaveRequestData.startDate = payload.startDate;
	if (payload.endDate) updateLeaveRequestData.endDate = payload.endDate;
	if (payload.description) updateLeaveRequestData.reason = payload.description;
	if (payload.document) updateLeaveRequestData.documentUrl = payload.document;

	updateLeaveRequestData.updatedAt = new Date().toISOString();

	let updatedLeaveRequest = existLeaveRequest;
	if (Object.keys(updateLeaveRequestData).length > 0) {
		updatedLeaveRequest = await db
			.updateTable("leaveRequests")
			.set(updateLeaveRequestData)
			.where("id", "=", leaveRequestId as LeaveRequestsId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	const finalLeavePolicy =
		existLeavePolicy ||
		(await db
			.selectFrom("leavePolicies")
			.select(["id", "name", "isCountedAsPresent"])
			.where("id", "=", updatedLeaveRequest.leavePolicyId as LeavePoliciesId)
			.executeTakeFirstOrThrow());

	const finalUser =
		existUser ||
		(await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", updatedLeaveRequest.userId as UsersId)
			.executeTakeFirstOrThrow());

	const reviewer = updatedLeaveRequest.reviewedBy
		? await db
				.selectFrom("users")
				.select(["id", "name", "email"])
				.where("id", "=", updatedLeaveRequest.reviewedBy as UsersId)
				.executeTakeFirst()
		: null;

	return {
		...updatedLeaveRequest,
		documentUrl: updatedLeaveRequest.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(updatedLeaveRequest.documentUrl))}`
			: null,
		name: finalLeavePolicy.name,
		isCountedAsPresent: finalLeavePolicy.isCountedAsPresent,
		userName: finalUser.name,
		userEmail: finalUser.email,
		reviewerName: reviewer?.name || null,
		reviewerEmail: reviewer?.email || null,
	};
};

export const reviewLeaveRequest = async (
	leaveRequestId: string,
	adminUser: AuthUser,
	payload: ReviewLeaveRequestDTO,
): Promise<void> => {
	const leaveRequest = await db
		.selectFrom("leaveRequests")
		.select(["id", "status"])
		.where("leaveRequests.id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirstOrThrow();

	if (leaveRequest.status !== LeaveRequestStatus.PENDING) {
		throw new BadRequestError("Cuti sudah tidak dapat direview.");
	}

	const user = await db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId")
		.select(["users.id"])
		.where("administratorUsers.id", "=", adminUser.id as AdministratorUsersId)
		.executeTakeFirstOrThrow();

	await db
		.updateTable("leaveRequests")
		.set({
			status: payload.action,
			reviewedAt: new Date().toISOString(),
			reviewedBy: user.id as UsersId,
		})
		.where("id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirstOrThrow();
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("leaveRequests")
		.select(["leavePolicies.name", "leavePolicies.isCountedAsPresent"])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			name,
			isCountedAsPresent,
			status,
			startDate,
			endDate,
			userName,
			userEmail,
			reviewerName,
			reviewerEmail,
			createdAt,
		} = leaveRequestQueryDTO.parse(queryObj);

		// searching
		if (search) {
		}

		// filtering
		const filters: {
			name?: string | string[];
			isCountedAsPresent?: string | string[];
			status?: string | string[];
			startDate?: string | string[];
			endDate?: string | string[];
			userName?: string | string[];
			userEmail?: string | string[];
			reviewerName?: string | string[];
			reviewerEmail?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (isCountedAsPresent) filters.isCountedAsPresent = isCountedAsPresent;
		if (startDate) filters.startDate = startDate;
		if (endDate) filters.endDate = endDate;
		if (status) filters.status = status;
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (reviewerName) filters.reviewerName = reviewerName;
		if (reviewerEmail) filters.reviewerEmail = reviewerEmail;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: LEAVE_REQUEST_FILTERABLE_FIELDS,
			fieldTypes: LEAVE_REQUEST_FIELD_TYPES,
		});

		// sorting
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: LEAVE_REQUEST_SORTABLE_FIELDS,
			defaultSort: {
				field: "leaveRequests.createdAt",
				direction: "desc",
			},
		});

		// apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as LeaveRequestsId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"leaveRequests.id",
			"in",
			selection.selectedIds as LeaveRequestsId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
