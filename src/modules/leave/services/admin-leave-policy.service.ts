import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type {
	LeavePolicies,
	LeavePoliciesId,
} from "@/database/types/public/LeavePolicies";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
} from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import { type LeavePolicyQueryDTO, leavePolicyQueryDTO } from "../dtos";
import type {
	AdminCreateLeavePolicyDTO,
	AdminUpdateLeavePolicyDTO,
} from "../dtos/request";
import type { LeavePolicyResponseDTO } from "../dtos/response";
import { bulkCreateLeaveUsageProducer } from "../jobs/producers/bulk-create-leave-balance.producer";

const LEAVE_POLICY_FILTERABLE_FIELDS: FilterableFields = {
	id: "leavePolicies.id",
	name: "leavePolicies.name",
	quota: "leavePolicies.quota",
	isCountedAsPresent: "leavePolicies.isCountedAsPresent",
	createdAt: "leavePolicies.createdAt",
} as const;

const LEAVE_POLICY_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	quota: "number",
	isCountedAsPresent: "boolean",
	createdAt: "date",
} as const;

const LEAVE_POLICY_SORTABLE_FIELDS: SortableFields = {
	id: "leavePolicies.id",
	name: "leavePolicies.name",
	quota: "leavePolicies.quota",
	isCountedAsPresent: "leavePolicies.isCountedAsPresent",
	createdAt: "leavePolicies.createdAt",
} as const;

export const getAllLeavePolicy = async (
	queryParams: LeavePolicyQueryDTO,
): Promise<{ data: LeavePolicyResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		name,
		quota,
		isCountedAsPresent,
		createdAt,
	} = queryParams;

	const query = db.selectFrom("leavePolicies").selectAll();

	const countQuery = db.selectFrom("leavePolicies");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		name?: string | string[];
		quota?: string | string[];
		isCountedAsPresent?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (quota) filters.quota = quota;
	if (isCountedAsPresent) filters.isCountedAsPresent = isCountedAsPresent;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: LEAVE_POLICY_FIELD_TYPES,
			filterableFields: LEAVE_POLICY_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: LEAVE_POLICY_SORTABLE_FIELDS,
		defaultSort: {
			field: "leavePolicies.createdAt",
			direction: "desc",
		}
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [leavePolicies, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: leavePolicies,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getLeavePolicy = async (
	leavePolicyId: string,
): Promise<LeavePolicyResponseDTO> => {
	const leavePolicy = await db
		.selectFrom("leavePolicies")
		.selectAll()
		.where("id", "=", leavePolicyId as LeavePoliciesId)
		.executeTakeFirst();

	if (!leavePolicy) {
		throw new NotFoundError("Konfigurasi cuti tidak ditemukan");
	}

	return leavePolicy;
};

export const getLeavePolicyOptions = async (): Promise<OptionItemDTO[]> => {
	const leavePolicies = await db
		.selectFrom("leavePolicies")
		.select(["id as value", "name as label"])
		.execute();

	return leavePolicies;
};

export const createLeavePolicy = async (
	payload: AdminCreateLeavePolicyDTO,
): Promise<LeavePolicyResponseDTO> => {
	const existLeavePolicy = await db
		.selectFrom("leavePolicies")
		.select(["id"])
		.where("name", "=", payload.name)
		.executeTakeFirst();

	if (existLeavePolicy) {
		throw new BadRequestError(
			`Policy dengan nama '${payload.name}' sudah tersedia, tidak bisa membuat policy dengan nama yang sama`,
		);
	}

	const newLeavePolicy = await db
		.insertInto("leavePolicies")
		.values({
			id: nanoid() as LeavePoliciesId,
			...payload,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	await bulkCreateLeaveUsageProducer(newLeavePolicy);

	return newLeavePolicy;
};

export const updateLeavePolicy = async (
	leavePolicyId: string,
	payload: AdminUpdateLeavePolicyDTO,
): Promise<LeavePolicyResponseDTO> => {
	const existLeavePolicy = await db
		.selectFrom("leavePolicies")
		.selectAll()
		.where("id", "=", leavePolicyId as LeavePoliciesId)
		.executeTakeFirst();

	if (!existLeavePolicy) {
		throw new NotFoundError("Konfigurasi cuti tidak ditemukan");
	}

	if (payload.name) {
		const duplicatePolicy = await db
			.selectFrom("leavePolicies")
			.select(["id"])
			.where("name", "=", payload.name)
			.where("id", "!=", leavePolicyId as LeavePoliciesId)
			.executeTakeFirst();

		if (duplicatePolicy) {
			throw new BadRequestError(
				`Nama policy '${payload.name}' sudah digunakan. Silakan pilih nama lain.`,
			);
		}
	}

	if (payload.quota) {
		const maxLeaveUsage = await db
			.selectFrom("leaveUsages")
			.innerJoin("users", "users.id", "leaveUsages.userId")
			.select(["leaveUsages.userId", "users.name", "leaveUsages.used"])
			.where("leaveUsages.leavePolicyId", "=", leavePolicyId as LeavePoliciesId)
			.orderBy("leaveUsages.used", "desc")
			.limit(1)
			.executeTakeFirst();

		if (maxLeaveUsage && payload.quota < maxLeaveUsage.used) {
			throw new BadRequestError(
				`Kuota tidak dapat diubah menjadi ${payload.quota} hari karena pengguna '${maxLeaveUsage.name}' (ID: ${maxLeaveUsage.userId}) telah menggunakan ${maxLeaveUsage.used} hari cuti.`,
			);
		}
	}

	const updateLeavePolicyData: Partial<LeavePolicies> = {};

	if (payload.name) updateLeavePolicyData.name = payload.name;
	if (payload.description)
		updateLeavePolicyData.description = payload.description;
	if (payload.quota) updateLeavePolicyData.quota = payload.quota;
	if (payload.isCountedAsPresent !== undefined) {
		updateLeavePolicyData.isCountedAsPresent = payload.isCountedAsPresent;
	}

	updateLeavePolicyData.updatedAt = new Date().toISOString();

	let updatedLeavePolicy = existLeavePolicy;
	if (Object.keys(updateLeavePolicyData).length > 1) {
		updatedLeavePolicy = await db
			.updateTable("leavePolicies")
			.set(updateLeavePolicyData)
			.where("id", "=", leavePolicyId as LeavePoliciesId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	return updatedLeavePolicy;
};

export const bulkAction = async (
	payload: BulkActionDTO,
): Promise<string | null> => {
	const { action, queryString, selection } = payload;
	let query = db.selectFrom("leavePolicies").selectAll();

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, quota, isCountedAsPresent, createdAt } =
			leavePolicyQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filtering
		const filters: {
			name?: string | string[];
			quota?: string | string[];
			isCountedAsPresent?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (quota) filters.quota = quota;
		if (isCountedAsPresent) filters.isCountedAsPresent = isCountedAsPresent;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: LEAVE_POLICY_FILTERABLE_FIELDS,
			fieldTypes: LEAVE_POLICY_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: LEAVE_POLICY_SORTABLE_FIELDS,
			defaultSort: {
				field: "leavePolicies.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as LeavePoliciesId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"leavePolicies.id",
			"in",
			selection.selectedIds as LeavePoliciesId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
