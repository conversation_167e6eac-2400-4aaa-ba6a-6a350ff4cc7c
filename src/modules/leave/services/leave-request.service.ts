import dayjs from "dayjs";
import isBetweenPlugin from "dayjs/plugin/isBetween.js";
import isSameOrBeforePlugin from "dayjs/plugin/isSameOrBefore.js";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { LeavePoliciesId } from "@/database/types/public/LeavePolicies";
import type { LeaveRequestsId } from "@/database/types/public/LeaveRequests";
import type { LeaveUsagesId } from "@/database/types/public/LeaveUsages";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import { LeaveRequestStatus } from "@/shared/enums";
import { BadRequestError, ForbiddenError } from "@/shared/exceptions";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import type { AuthUser } from "@/shared/types";
import type {
	CreateLeaveRequestDTO,
	ReviewLeaveRequestDTO,
} from "../dtos/request";
import type {
	LeaveQuotaResponseDTO,
	LeaveRequestResponseDTO,
} from "../dtos/response";
import {
	_calculateEffectiveLeaveDays,
	_checkIsWorkingDay,
	_findNextWorkingDay,
} from "./shared-leave-request.service";

dayjs.extend(isSameOrBeforePlugin);
dayjs.extend(isBetweenPlugin);

export const getMyLeaveQuota = async (
	user: AuthUser,
): Promise<LeaveQuotaResponseDTO> => {
	const leaveUsages = await db
		.selectFrom("leaveUsages")
		.innerJoin("leavePolicies", "leavePolicies.id", "leaveUsages.leavePolicyId")
		.select([
			"leaveUsages.id",
			"leaveUsages.used",
			"leavePolicies.name",
			"leavePolicies.quota",
		])
		.where("leaveUsages.userId", "=", user.id as UsersId)
		.execute();

	const formattedLeaveQuota = leaveUsages.map((leaveUsage) => ({
		...leaveUsage,
		remaining: leaveUsage.quota - leaveUsage.used,
		isActive: true,
	}));

	return formattedLeaveQuota;
};

export const getMyLatestLeaveRequest = async (
	user: AuthUser,
): Promise<LeaveRequestResponseDTO[]> => {
	const leaveRequests = await db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("leaveRequests")
		.select(["leavePolicies.name", "leavePolicies.isCountedAsPresent"])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.where("leaveRequests.userId", "=", user.id as UsersId)
		.orderBy("createdAt", "desc")
		.limit(2)
		.execute();

	return leaveRequests.map((leave) => ({
		...leave,
		reviewerName: leave.reviewerName || null,
		reviewerEmail: leave.reviewerEmail || null,
		startDate: dayjs(leave.startDate).locale("id").format("D MMMM YYYY"),
		endDate: dayjs(leave.endDate).locale("id").format("D MMMM YYYY"),
		documentUrl: leave.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(leave.documentUrl))}`
			: null,
	}));
};

export const getMyLeaveRequestHistory = async (
	user: AuthUser,
): Promise<LeaveRequestResponseDTO[]> => {
	const leaveRequests = await db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leavePolicies.id",
			"leaveRequests.leavePolicyId",
		)
		.innerJoin("users", "users.id", "leaveRequests.userId")
		.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
		.selectAll("leaveRequests")
		.select(["leavePolicies.name", "leavePolicies.isCountedAsPresent"])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"reviewer.name as reviewerName",
			"reviewer.email as reviewerEmail",
		])
		.where("leaveRequests.userId", "=", user.id as UsersId)
		.orderBy("leaveRequests.createdAt", "desc")
		.execute();

	return leaveRequests.map((leave) => ({
		...leave,
		reviewerName: leave.reviewerName || null,
		reviewerEmail: leave.reviewerEmail || null,
		startDate: dayjs(leave.startDate).locale("id").format("D MMMM YYYY"),
		endDate: dayjs(leave.endDate).locale("id").format("D MMMM YYYY"),
		documentUrl: leave.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(leave.documentUrl))}`
			: null,
	}));
};

export const createLeaveRequest = async (
	user: AuthUser,
	payload: CreateLeaveRequestDTO,
): Promise<LeaveRequestResponseDTO> => {
	const startDateIsWorkingDay = await _checkIsWorkingDay(
		dayjs(payload.startDate),
	);

	if (!startDateIsWorkingDay) {
		throw new BadRequestError("Tanggal mulai cuti harus berada di hari kerja.");
	}

	const leaveUsage = await db
		.selectFrom("leaveUsages")
		.innerJoin("leavePolicies", "leavePolicies.id", "leaveUsages.leavePolicyId")
		.select([
			"leaveUsages.id as leaveUsageId",
			"leaveUsages.used",
			"leaveUsages.userId",
			"leavePolicies.id as leavePolicyId",
			"leavePolicies.name",
			"leavePolicies.quota",
			"leavePolicies.isCountedAsPresent",
		])
		.where("leavePolicyId", "=", payload.leavePolicyId as LeavePoliciesId)
		.where("userId", "=", user.id as UsersId)
		.executeTakeFirst();

	if (!leaveUsage) {
		throw new BadRequestError(
			"Quota cuti anda untuk cuti tidak ditemukan, segera hubungi admin.",
		);
	}

	const existingLeaves = await db
		.selectFrom("leaveRequests")
		.select(["id", "startDate", "endDate"])
		.where("userId", "=", user.id as UsersId)
		.where("status", "in", ["PENDING", "APPROVED"])
		.execute();

	const newStart = dayjs(payload.startDate);
	const newEnd = dayjs(payload.endDate);

	// Cek overlap dengan cuti yang sudah ada
	for (const leave of existingLeaves) {
		const existingStart = dayjs(leave.startDate);
		const existingEnd = dayjs(leave.endDate);

		const isOverlap =
			newStart.isBetween(existingStart, existingEnd, "day", "[]") ||
			newEnd.isBetween(existingStart, existingEnd, "day", "[]") ||
			existingStart.isBetween(newStart, newEnd, "day", "[]");

		if (isOverlap) {
			throw new BadRequestError(
				`Anda sudah memiliki cuti pada tanggal ${dayjs(leave.startDate).format("D MMM YYYY")} sampai ${dayjs(leave.endDate).format("D MMM YYYY")}. Silakan pilih tanggal lain yang tidak bertabrakan.`,
			);
		}
	}

	// Pastikan cuti baru dimulai pada hari kerja setelah cuti terakhir yang masih aktif.
	if (existingLeaves.length > 0) {
		const latestEndDate = existingLeaves.reduce((latest, leave) => {
			const leaveEnd = dayjs(leave.endDate);
			return leaveEnd.isAfter(latest) ? leaveEnd : latest;
		}, dayjs("1900-01-01"));

		if (latestEndDate.isValid()) {
			const nextWorkingDay = await _findNextWorkingDay(latestEndDate);

			if (newStart.isBefore(nextWorkingDay, "day")) {
				throw new BadRequestError(
					`Tanggal mulai cuti tidak valid. Cuti baru hanya dapat dimulai pada hari kerja setelah cuti terakhir, yaitu ${nextWorkingDay.locale("id").format("D MMMM YYYY")}.`,
				);
			}
		}
	}

	// Hitung jumlah hari cuti efektif
	const effectiveLeaveDays = await _calculateEffectiveLeaveDays(
		dayjs(payload.startDate),
		dayjs(payload.endDate),
	);

	const remaining = leaveUsage.quota - leaveUsage.used;
	if (effectiveLeaveDays > remaining) {
		throw new BadRequestError(
			`Quota cuti anda tidak mencukupi. Diperlukan ${effectiveLeaveDays} hari, tetapi sisa quota anda hanya ${remaining} hari.`,
		);
	}

	const result = await db.transaction().execute(async (trx) => {
		const newLeaveRequest = await trx
			.insertInto("leaveRequests")
			.values({
				id: nanoid() as LeaveRequestsId,
				userId: user.id as UsersId,
				leaveUsageId: leaveUsage.leaveUsageId as LeaveUsagesId,
				leavePolicyId: leaveUsage.leavePolicyId as LeavePoliciesId,
				startDate: payload.startDate,
				endDate: payload.endDate,
				reason: payload.description,
				status: LeaveRequestStatus.PENDING,
				effectiveLeaveDays,
				documentUrl: payload.document || null,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.returningAll()
			.executeTakeFirstOrThrow();

		await trx
			.updateTable("leaveUsages")
			.set({
				used: leaveUsage.used + effectiveLeaveDays,
			})
			.where("id", "=", leaveUsage.leaveUsageId as LeaveUsagesId)
			.execute();

		return newLeaveRequest;
	});

	return {
		...result,
		documentUrl: result.documentUrl
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(result.documentUrl))}`
			: null,
		name: leaveUsage.name,
		isCountedAsPresent: leaveUsage.isCountedAsPresent,
		userName: user.name,
		userEmail: user.email,
		reviewerName: null,
		reviewerEmail: null,
	};
};

export const reviewLeaveRequest = async (
	leaveRequestId: string,
	user: AuthUser,
	payload: ReviewLeaveRequestDTO,
): Promise<void> => {
	const isHR = user.roles.find((role) => role.id === Env.HR_ROLE_ID);
	if (!isHR) {
		throw new ForbiddenError(
			"Anda tidak memiliki akses untuk menyetujui cuti.",
		);
	}

	const leaveRequest = await db
		.selectFrom("leaveRequests")
		.select(["id", "status"])
		.where("leaveRequests.id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirstOrThrow();

	if (leaveRequest.status !== LeaveRequestStatus.PENDING) {
		throw new BadRequestError("Cuti sudah tidak dapat direview.");
	}

	await db
		.updateTable("leaveRequests")
		.set({
			status: payload.action,
			reviewedAt: new Date().toISOString(),
			reviewedBy: user.id as UsersId,
		})
		.where("id", "=", leaveRequestId as LeaveRequestsId)
		.executeTakeFirstOrThrow();
};
