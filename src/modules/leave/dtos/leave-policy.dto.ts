import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";
import { registry } from "@/shared/docs/openapi-registry";

export const leavePolicyDTO = z
	.object({
		id: z.string().meta({
			example: "lp_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the leave policy",
		}),
		name: z.string().meta({
			example: "Annual Leave",
			description: "Name of the leave policy",
		}),
		description: z.string().nullable().meta({
			example: "Annual leave policy for all employees with 12 days quota",
			description: "Description of the leave policy",
		}),
		quota: z.number().meta({
			example: 12,
			description: "Number of leave days allocated per year",
		}),
		isCountedAsPresent: z.boolean().meta({
			example: true,
			description: "Whether this leave policy is counted as present",
		}),
	})
	.extend({ ...timestampDTO.shape });
export type LeavePolicyDTO = z.infer<typeof leavePolicyDTO>;

registry.register("LeavePolicy", leavePolicyDTO);

export const leavePolicyQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	quota: filterFieldDTO.optional(),
	isCountedAsPresent: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type LeavePolicyQueryDTO = z.infer<typeof leavePolicyQueryDTO>;
