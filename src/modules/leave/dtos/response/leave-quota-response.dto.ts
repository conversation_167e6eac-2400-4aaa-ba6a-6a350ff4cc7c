import * as z from "zod";

export const leaveQuotaResponseDTO = z.array(
	z.object({
		id: z.string().meta({
			example: "zNKqWgGbr2bqhocdueXSk",
			description: "unique identity",
		}),
		remaining: z.number().meta({
			example: 15,
			description: "Total remaining for quota",
		}),
		name: z.string().meta({
			example: "<PERSON><PERSON>",
			description: "Name of leave quota",
		}),
		quota: z.number().meta({
			example: 20,
			description: "Total Quota",
		}),
		used: z.number().meta({
			example: 5,
			description: "Used",
		}),
		isActive: z.boolean().meta({
			example: true,
		}),
	}),
);
export type LeaveQuotaResponseDTO = z.infer<typeof leaveQuotaResponseDTO>;
