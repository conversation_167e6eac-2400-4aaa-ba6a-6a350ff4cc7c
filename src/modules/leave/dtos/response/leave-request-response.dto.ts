import * as z from "zod";
import { leaveRequestDTO } from "../leave-request.dto";
import "@/shared/lib/zod-extensions";

export const leaveRequestResponseDTO = leaveRequestDTO.extend({
	name: z.string(),
	isCountedAsPresent: z.boolean(),
	userName: z.string(),
	userEmail: z.string(),
	reviewerName: z.string().nullable(),
	reviewerEmail: z.string().nullable(),
}).openapi("LeaveRequestResponseDTO");
export type LeaveRequestResponseDTO = z.infer<typeof leaveRequestResponseDTO>;
