import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";
import { registry } from "@/shared/docs/openapi-registry";

export const leaveRequestDTO = z
	.object({
		id: z.string().meta({
			example: "lr_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the leave request",
		}),
		userId: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "ID of the user requesting leave",
		}),
		leaveUsageId: z.string().meta({
			example: "lb_Qah4cccEK0MsspUJ9ywAy",
			description: "ID of the leave balance record",
		}),
		leavePolicyId: z.string().meta({
			example: "lp_rJGI0UrX29J2pJN5lj9vN",
			description: "ID of the leave policy being applied",
		}),
		startDate: z.iso.datetime().meta({
			example: "2025-01-15",
			description: "Start date of leave (DATEONLY format)",
		}),
		endDate: z.iso.datetime().meta({
			example: "2025-01-17",
			description: "End date of leave (DATEONLY format)",
		}),
		reason: z.string().meta({
			example: "Family vacation to Bali",
			description: "Reason for the leave request",
		}),
		status: z.string().meta({
			example: "PENDING",
			description: "Status of leave request (PENDING, APPROVED, REJECTED)",
		}),
		effectiveLeaveDays: z.number().meta({
			example: 3,
			description:
				"Number of effective leave days (excluding weekends/holidays)",
		}),
		documentUrl: z.string().nullable().meta({
			example: "/uploads/leave-docs/leave-doc-123.pdf",
			description: "URL to supporting document",
		}),
		reviewedBy: z.string().nullable().meta({
			example: "hr_manager_456",
			description: "ID of HR who reviewed the request",
		}),
		reviewedAt: z.iso.datetime().nullable().meta({
			example: "2025-01-14T14:30:00Z",
			description: "When the request was reviewed",
		}),
	})
	.extend({ ...timestampDTO.shape })
export type LeaveRequestDTO = z.infer<typeof leaveRequestDTO>;

registry.register("LeaveRequest", leaveRequestDTO);

export const leaveRequestQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	isCountedAsPresent: filterFieldDTO.optional(),
	startDate: filterFieldDTO.optional(),
	endDate: filterFieldDTO.optional(),
	status: filterFieldDTO.optional(),
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	reviewerName: filterFieldDTO.optional(),
	reviewerEmail: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type LeaveRequestQueryDTO = z.infer<typeof leaveRequestQueryDTO>;
