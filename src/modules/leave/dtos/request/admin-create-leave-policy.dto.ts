import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const adminCreateLeavePolicyDTO = z.object({
	name: z.string().nonempty().meta({
		description: "Leave policy name",
		example: "Annual Leave",
	}),
	description: z.string().nonempty().optional().meta({
		description: "Optional description for the leave policy",
		example: "Annual leave policy for all employees",
	}),
	quota: z.coerce.number().min(0).meta({
		description: "quota of leave policy in number of days",
		example: 12,
	}),
	isCountedAsPresent: z.boolean().default(false).meta({
		description: "Whether this leave policy is counted as present",
		example: true,
	}),
});
export type AdminCreateLeavePolicyDTO = z.infer<
	typeof adminCreateLeavePolicyDTO
>;
