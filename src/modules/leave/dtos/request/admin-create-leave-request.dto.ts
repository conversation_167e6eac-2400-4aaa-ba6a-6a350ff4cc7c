import z from "zod";

export const adminCreateLeaveRequestDTO = z.object({
	userId: z.string().nonempty().meta({
		example: "user_bXn1LnLm8GNqn2OKSE4db",
		description: "ID of the user requesting leave",
	}),
	leavePolicyId: z.string().nonempty().meta({
		description: "ID of the leave policy to apply for",
		example: "Qah4cccEK0MsspUJ9ywAy",
	}),
	startDate: z.iso.date().meta({
		description: "Start date of leave in YYYY-MM-DD format",
		example: "2025-01-15",
	}),
	endDate: z.iso.date().meta({
		description: "End date of leave in YYYY-MM-DD format",
		example: "2025-01-17",
	}),
	description: z.string().nonempty().meta({
		description: "Reason or description for the leave request",
		example: "Family vacation",
	}),
	document: z.string().optional().meta({
		description: "Optional supporting document URL",
		example: "/uploads/documents/leave-doc-123.pdf",
	}),
});
export type AdminCreateLeaveRequestDTO = z.infer<
	typeof adminCreateLeaveRequestDTO
>;
