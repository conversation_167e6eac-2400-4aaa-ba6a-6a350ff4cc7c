import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import * as z from "zod";

dayjs.extend(customParseFormat);

export const createLeaveRequestDTO = z
	.object({
		leavePolicyId: z.string().meta({
			description: "ID of the leave policy to apply for",
			example: "Qah4cccEK0MsspUJ9ywAy",
		}),
		startDate: z.iso.date().meta({
			description: "Start date of leave in YYYY-MM-DD format",
			example: "2025-01-15",
		}),
		endDate: z.iso.date().meta({
			description: "End date of leave in YYYY-MM-DD format",
			example: "2025-01-17",
		}),
		description: z.string().nonempty().meta({
			description: "Reason or description for the leave request",
			example: "Family vacation",
		}),
		document: z.string().optional().meta({
			description: "Optional supporting document URL",
			example: "/uploads/documents/leave-doc-123.pdf",
		}),
	})
	.refine((data) => dayjs(data.endDate).isSameOrAfter(dayjs(data.startDate)), {
		path: ["endDate"],
		message: "<PERSON>gal akhir tidak boleh sebelum tanggal mulai.",
	});
export type CreateLeaveRequestDTO = z.infer<typeof createLeaveRequestDTO>;
