import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";
import { registry } from "@/shared/docs/openapi-registry";

export const leaveUsageDTO = z
	.object({
		id: z.string().meta({
			example: "lb_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the leave balance",
		}),
		userId: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "ID of the user who owns this leave balance",
		}),
		leavePolicyId: z.string().meta({
			example: "lp_rJGI0UrX29J2pJN5lj9vN",
			description: "ID of the leave policy this balance is based on",
		}),
		quota: z.number().meta({
			example: 12,
			description: "Total leave quota allocated for the year",
		}),
		used: z.number().meta({
			example: 3,
			description: "Number of leave days already used",
		}),
		isActive: z.boolean().meta({
			example: true,
			description: "Whether this leave balance is active",
		}),
	})
	.extend({ ...timestampDTO.shape });
export type LeaveUsageDTO = z.infer<typeof leaveUsageDTO>;

registry.register("LeaveUsage", leaveUsageDTO);
