import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
} from "@/shared/middlewares";
import * as leaveRequestController from "../controllers/leave-request.controller";
import { createLeaveRequestDTO, reviewLeaveRequestDTO } from "../dtos/request";

export const leaveRequestRouter = Router();

leaveRequestRouter.get(
	"/quota",
	authMiddleware(AuthStrategy.JWT_USER),
	leaveRequestController.getMyLeaveQuota,
);

leaveRequestRouter.get(
	"/latest",
	authMiddleware(AuthStrategy.JWT_USER),
	leaveRequestController.getMyLatestLeaveRequest,
);

leaveRequestRouter.get(
	"/history",
	authMiddleware(AuthStrategy.JWT_USER),
	leaveRequestController.getMyLeaveRequestHistory,
);

leaveRequestRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.single("document", {
		allowedMimeTypes: ["*"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users/leave-requests`,
		userFolder: true,
		fileNamePrefix: "leave-request-doc",
		maxFileSize: 5 * 1024 * 1024, // 5MB
		required: false,
	}),
	preprocessors.addFilePaths([
		{
			field: "document",
			bodyKey: "document",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	validateBody(createLeaveRequestDTO),
	leaveRequestController.createLeaveRequest,
);

leaveRequestRouter.patch(
	"/:leaveRequestId/review",
	authMiddleware(AuthStrategy.JWT_USER),
	validateBody(reviewLeaveRequestDTO),
	leaveRequestController.reviewLeaveRequest,
);
