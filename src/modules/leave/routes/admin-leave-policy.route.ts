import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminLeavePolicyController from "../controllers/admin-leave-policy.controller";
import { leavePolicyQueryDTO } from "../dtos";
import {
	adminCreateLeavePolicyDTO,
	adminUpdateLeavePolicyDTO,
} from "../dtos/request";

export const adminLeavePolicyRouter = Router();

adminLeavePolicyRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(leavePolicyQueryDTO),
	adminLeavePolicyController.getAllLeavePolicy,
);

adminLeavePolicyRouter.get(
	"/options",
	adminLeavePolicyController.getLeavePolicyOptions,
);

adminLeavePolicyRouter.get(
	"/:leavePolicyId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminLeavePolicyController.getLeavePolicy,
);

adminLeavePolicyRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateLeavePolicyDTO),
	adminLeavePolicyController.createLeavePolicy,
);

adminLeavePolicyRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminLeavePolicyController.bulkAction,
);

adminLeavePolicyRouter.put(
	"/:leavePolicyId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateLeavePolicyDTO),
	adminLeavePolicyController.updateLeavePolicy,
);
