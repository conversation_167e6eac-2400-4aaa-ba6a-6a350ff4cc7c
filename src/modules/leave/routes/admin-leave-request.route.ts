import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminLeaveRequestController from "../controllers/admin-leave-request.controller";
import { leaveRequestQueryDTO } from "../dtos";
import {
	adminCreateLeaveRequestDTO,
	adminUpdateLeaveRequestDTO,
	reviewLeaveRequestDTO,
} from "../dtos/request";

export const adminLeaveRequestRouter = Router();

adminLeaveRequestRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(leaveRequestQueryDTO),
	adminLeaveRequestController.getAllLeaveRequest,
);

adminLeaveRequestRouter.get(
	"/:leaveRequestId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminLeaveRequestController.getLeaveRequest,
);

adminLeaveRequestRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	localUploadMiddleware.single("document", {
		allowedMimeTypes: ["*"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users/leave-requests`,
		userFolder: true,
		fileNamePrefix: "leave-request-doc",
		maxFileSize: 5 * 1024 * 1024, // 5MB
		required: false,
	}),
	preprocessors.addFilePaths([
		{
			field: "document",
			bodyKey: "document",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	validateBody(adminCreateLeaveRequestDTO),
	adminLeaveRequestController.createLeaveRequest,
);

adminLeaveRequestRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminLeaveRequestController.bulkAction,
);

adminLeaveRequestRouter.put(
	"/:leaveRequestId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	localUploadMiddleware.single("document", {
		allowedMimeTypes: ["*"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users/leave-requests`,
		userFolder: true,
		fileNamePrefix: "leave-request-doc",
		maxFileSize: 5 * 1024 * 1024, // 5MB
		required: false,
	}),
	preprocessors.addFilePaths([
		{
			field: "document",
			bodyKey: "document",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	validateBody(adminUpdateLeaveRequestDTO),
	adminLeaveRequestController.updateLeaveRequest,
);

adminLeaveRequestRouter.patch(
	"/:leaveRequestId/review",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(reviewLeaveRequestDTO),
	adminLeaveRequestController.reviewLeaveRequest,
);
