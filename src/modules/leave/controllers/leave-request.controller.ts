import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import type {
	CreateLeaveRequestDTO,
	ReviewLeaveRequestDTO,
} from "../dtos/request";
import * as leaveRequestService from "../services/leave-request.service";

export const getMyLeaveQuota = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await leaveRequestService.getMyLeaveQuota(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				quotas: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMyLatestLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await leaveRequestService.getMyLatestLeaveRequest(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				leaveRequests: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMyLeaveRequestHistory = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await leaveRequestService.getMyLeaveRequestHistory(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				leaveRequests: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as CreateLeaveRequestDTO;
		const result = await leaveRequestService.createLeaveRequest(
			req.user as AuthUser,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil request cuti baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const reviewLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const leaveRequestId = req.params.leaveRequestId as string;
		const validatedBody = req.validatedBody as ReviewLeaveRequestDTO;
		const result = await leaveRequestService.reviewLeaveRequest(
			leaveRequestId,
			req.user as AuthUser,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil meninjau permintaan cuti",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
