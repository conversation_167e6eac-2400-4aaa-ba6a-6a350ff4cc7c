import type { NextFunction, Request, Response } from "express";
import * as leavePolicyService from "../services/leave-policy.service";

export const getActiveLeavePolicies = async (
	_req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await leavePolicyService.getActiveLeavePolicies();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				leavePolicies: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
