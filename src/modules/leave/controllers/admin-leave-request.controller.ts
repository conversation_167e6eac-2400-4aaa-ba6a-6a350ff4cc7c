import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { AuthUser } from "@/shared/types";
import type { LeaveRequestQueryDTO } from "../dtos";
import type {
	AdminCreateLeaveRequestDTO,
	AdminUpdateLeaveRequestDTO,
	ReviewLeaveRequestDTO,
} from "../dtos/request";
import * as adminLeaveRequestService from "../services/admin-leave-request.service";

export const getAllLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as LeaveRequestQueryDTO;

		const { data, meta } =
			await adminLeaveRequestService.getAllLeaveRequest(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data,
			meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const leaveRequestId = req.params.leaveRequestId as string;

		const result =
			await adminLeaveRequestService.getLeaveRequest(leaveRequestId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateLeaveRequestDTO;

		const result =
			await adminLeaveRequestService.createLeaveRequest(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const leaveRequestId = req.params.leaveRequestId as string;
		const validatedBody = req.validatedBody as AdminUpdateLeaveRequestDTO;

		const result = await adminLeaveRequestService.updateLeaveRequest(
			leaveRequestId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const reviewLeaveRequest = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const leaveRequestId = req.params.leaveRequestId as string;
		const validatedBody = req.validatedBody as ReviewLeaveRequestDTO;
		const result = await adminLeaveRequestService.reviewLeaveRequest(
			leaveRequestId,
			req.user as AuthUser,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil meninjau permintaan cuti",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminLeaveRequestService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=leaveRequests.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
