import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { LeavePolicyQueryDTO } from "../dtos";
import type {
	AdminCreateLeavePolicyDTO,
	AdminUpdateLeavePolicyDTO,
} from "../dtos/request";
import * as adminLeavePolicyService from "../services/admin-leave-policy.service";

export const getAllLeavePolicy = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedQuery = req.validatedQuery as LeavePolicyQueryDTO;
		const result =
			await adminLeavePolicyService.getAllLeavePolicy(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getLeavePolicy = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const leavePolicyId = req.params.leavePolicyId as string;
		const result = await adminLeavePolicyService.getLeavePolicy(leavePolicyId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getLeavePolicyOptions = async (
	_req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await adminLeavePolicyService.getLeavePolicyOptions();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createLeavePolicy = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as AdminCreateLeavePolicyDTO;
		const result =
			await adminLeavePolicyService.createLeavePolicy(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateLeavePolicy = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const leavePolicyId = req.params.leavePolicyId as string;
		const validatedBody = req.validatedBody as AdminUpdateLeavePolicyDTO;
		const result = await adminLeavePolicyService.updateLeavePolicy(
			leavePolicyId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminLeavePolicyService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=roles.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
