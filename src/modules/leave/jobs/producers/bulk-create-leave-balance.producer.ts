import { db } from "@/database/connection";
import type { LeavePolicies } from "@/database/types/public/LeavePolicies";
import { leaveUsageQueue } from "../queues/leave-balance.queue";

export async function bulkCreateLeaveUsageProducer(leavePolicy: LeavePolicies) {
	const users = await db.selectFrom("users").select(["id"]).execute();

	await leaveUsageQueue.addBulk(
		users.map((user) => ({
			name: "create-leave-usage",
			data: {
				userId: user.id,
				leavePolicyId: leavePolicy.id,
			},
			opts: {
				attempts: 5, // coba maksimal 5 kali
				backoff: {
					type: "exponential", // atau "fixed"
					delay: 1000, // delay 1 detik awal
				},
			},
		})),
	);
}
