import { Worker } from "bullmq";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { LeavePoliciesId } from "@/database/types/public/LeavePolicies";
import type { LeaveUsagesId } from "@/database/types/public/LeaveUsages";
import type { UsersId } from "@/database/types/public/Users";
import { QUEUE_LIST } from "@/shared/constants";
import { cacheClient } from "@/shared/lib/redis";

new Worker(
	QUEUE_LIST.LEAVE_USAGE,
	async (job) => {
		try {
			const { userId, leavePolicyId } = job.data;
			await db
				.insertInto("leaveUsages")
				.values({
					id: nanoid() as LeaveUsagesId,
					userId: userId as UsersId,
					leavePolicyId: leavePolicyId as LeavePoliciesId,
					used: 0,
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
				})
				.executeTakeFirstOrThrow();
			console.log(`✅ Created leave balance for ${userId}`);
		} catch (error) {
			console.error(
				`❌ Failed to create leave balance for ${job.data.userId}:`,
				error,
			);
			throw error; // Rethrow the error to let BullMQ handle it
		}
	},
	{ connection: cacheClient },
);
