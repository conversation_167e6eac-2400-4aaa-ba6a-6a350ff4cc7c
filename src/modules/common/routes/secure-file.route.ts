import fs from "node:fs";
import { type Request, type Response, Router } from "express";
import { verifySignature } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";

export const secureFileRouter = Router();

secureFileRouter.get("/*name", (req: Request, res: Response): void => {
	const fullPath = req.originalUrl.split("?")[0] as string; // includes /secure-files/...
	const { expires, signature } = req.query;

	if (
		typeof expires !== "string" ||
		typeof signature !== "string" ||
		!verifySignature(fullPath, expires, signature)
	) {
		res.status(403).send("Forbidden");
		return;
	}

	const diskPath = PATHS.toDiskPath(`private${req.path}`); // this strips only /uploads/...

	if (!fs.existsSync(diskPath)) {
		res.status(404).send("Not found");
		return;
	}

	res.sendFile(diskPath);
});
