import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { attendanceStatResponseDTO } from "../dtos";

registry.registerPath({
	summary: "Get Dashboard Data [Admin]",
	method: "get",
	path: "/api/v1/admin/dashboard/attendance-stats",
	tags: ["dashboard"],
	description: "Get attendance stats data for admin",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(attendanceStatResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
