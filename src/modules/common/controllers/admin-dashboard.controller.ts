import type { NextFunction, Request, Response } from "express";
import * as adminDashboardService from "../services/admin-dashboard.service";

export const getDashboardData = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await adminDashboardService.getDashboardData();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAttendanceStat = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const startDate = req.query.startDate as string | undefined;
		const endDate = req.query.endDate as string | undefined;
		const result = await adminDashboardService.getAttendanceStat(
			startDate,
			endDate,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
