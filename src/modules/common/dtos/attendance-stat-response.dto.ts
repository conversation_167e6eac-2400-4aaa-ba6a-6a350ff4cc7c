import z from "zod";

export const attendanceStatResponseDTO = z
	.object({
		date: z.string().meta({
			example: "2025-01-15",
			description: "Date of attendance (DATEONLY format)",
		}),
		total: z.number().meta({
			example: 100,
			description: "Total number of employees",
		}),
		present: z.number().meta({
			example: 80,
			description: "Number of employees present",
		}),
		absent: z.number().meta({
			example: 10,
			description: "Number of employees absent",
		}),
		leave: z.number().meta({
			example: 10,
			description: "Number of employees on leave",
		}),
	})
	.openapi("AttendanceStatResponseDTO");
export type AttendanceStatResponseDTO = z.infer<
	typeof attendanceStatResponseDTO
>;
