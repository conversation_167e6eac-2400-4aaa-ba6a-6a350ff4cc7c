import z from "zod";

export const dashboardResponseDTO = z
	.object({
		usersData: z.array(
			z.object({
				id: z.string(),
				title: z.string(),
				total: z.number(),
				trend: z.object({
					value: z.number(),
					description: z.string(),
					trendType: z.enum(["neutral", "increase", "decrease"]),
				}),
				iconKey: z.string(),
			}),
		),
		attendanceData: z.array(
			z.object({
				id: z.string(),
				title: z.string(),
				total: z.number(),
				trend: z.object({
					value: z.number(),
					description: z.string(),
					trendType: z.enum(["neutral", "increase", "decrease"]),
				}),
				iconKey: z.string(),
			}),
		),
		latestLeaveRequests: z.array(
			z.object({
				id: z.string(),
				name: z.string(),
				status: z.string(),
				statusColor: z.string(),
				avatar: z.string(),
				captionLabel: z.string(),
				caption: z.string(),
			}),
		),
		latestOfficeLeaves: z.array(
			z.object({
				id: z.string(),
				name: z.string(),
				status: z.string(),
				statusColor: z.string(),
				avatar: z.string(),
				captionLabel: z.string(),
				caption: z.string(),
			}),
		),
	})
	.openapi("DashboardResponseDTO");
export type DashboardResponseDTO = z.infer<typeof dashboardResponseDTO>;
