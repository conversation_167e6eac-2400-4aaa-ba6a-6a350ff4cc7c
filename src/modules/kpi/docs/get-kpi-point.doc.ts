import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { KPIPointResponseDTO } from "../dtos";

registry.registerPath({
	summary: "Get KPI Points",
	method: "get",
	path: "/api/v1/kpi/points",
	tags: ["KPI"],
	description:
		"Get KPI points calculation for the authenticated user including task scores, attendance percentage, violation penalties, and final KPI score",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: KPIPointResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
