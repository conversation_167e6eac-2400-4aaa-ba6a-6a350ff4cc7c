import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import * as kpiService from "../services/kpi.service";

export const getKpiPoint = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const result = await kpiService.getKPIPoint({
			user: req.user as AuthUser,
		});

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
