import { type Job, Worker } from "bullmq";
import Redis from "ioredis";
import { db } from "@/database/connection";
import { recalculateAndUpsertKpiPoints } from "../../services/kpi.service";

const connection = new Redis({
	host: process.env.REDIS_HOST as string,
	port: Number(process.env.REDIS_PORT),
	password: process.env.REDIS_PASSWORD as string,
});

export const kpiWorker = new Worker(
	"kpi-queue",
	async (job: Job) => {
		if (job.name === "recalculate-kpi-all-users") {
			console.log("🚀 Running daily KPI recalculation...");

			const users = await db.selectFrom("users").select(["id"]).execute();

			for (const user of users) {
				await recalculateAndUpsertKpiPoints(user.id);
			}

			console.log("✅ KPI recalculation done!");
		}
	},
	{ connection },
);

kpiWorker.on("completed", (job) => {
	console.log(`✅ Job ${job.id} (${job.name}) selesai`);
});

kpiWorker.on("failed", (job, err) => {
	console.error(`❌ Job ${job?.id} gagal:`, err);
});
