import { kpiQueue } from "../queues/kpi.queue";

export async function scheduleDailyKpiJob() {
	const firstJob = await kpiQueue.upsertJobScheduler(
		"kpi-daily-scheduler",
		{
			pattern: "0 0 23 * * *", // jam 23:00 WIB setiap hari
			tz: "Asia/Jakarta", // timezone biar tepat
		},
		{
			name: "recalculate-kpi-all-users",
			data: {}, // worker nanti yang ambil data user
			opts: {
				attempts: 3,
				backoff: {
					type: "exponential",
					delay: 5000, // retry delay 5 detik
				},
				removeOnComplete: true,
				removeOnFail: 50,
			},
		},
	);

	console.log("📅 KPI job scheduler registered", firstJob?.id);
}
