import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { KpiRecordsId } from "@/database/types/public/KpiRecords";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import { AttendanceStatus, AttendanceType, TaskStatus } from "@/shared/enums";
import { CACHE_KEYS, CACHE_TTL, cacheHelpers } from "@/shared/lib/redis";
import type { AuthUser } from "@/shared/types";
import { toDateOnlyString } from "@/shared/utils/date";
import type { KPIPointResponseDTO } from "../dtos";

const _getPeriodDateRange = (
	period: string,
): { startDate: Date; endDate: Date } => {
	const year = Number.parseInt(period);
	if (Number.isNaN(year)) {
		const now = new Date();
		return {
			startDate: new Date(now.getFullYear(), 0, 1),
			endDate: new Date(now.getFullYear(), 11, 31, 23, 59, 59),
		};
	}

	return {
		startDate: new Date(year, 0, 1),
		endDate: new Date(year, 11, 31, 23, 59, 59),
	};
};

const _calculateTaskAverageScore = async (
	userId: string,
	startDate: Date,
	endDate: Date,
) => {
	const tasks = await db
		.selectFrom("tasks")
		.select(["ratingPoint"])
		.where("assigneeId", "=", userId as UsersId)
		.where("status", "=", TaskStatus.COMPLETED)
		.where((eb) =>
			eb.between("createdAt", startDate.toISOString(), endDate.toISOString()),
		)
		.execute();

	if (tasks.length === 0) {
		return { taskPoint: 0, totalTasks: 0 };
	}

	const totalScore = tasks.reduce(
		(sum, task) => sum + (task.ratingPoint || 0),
		0,
	);

	return { taskPoint: totalScore / tasks.length, totalTasks: tasks.length };
};

const _calculateAttendancePercentage = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<{
	attendancePoint: number;
	totalWorkDays: number;
	totalAttendanceDays: number;
}> => {
	// 1. Hari libur nasional
	const holidays = await db
		.selectFrom("holidays")
		.select(["date"])
		.where((eb) =>
			eb.between(
				"date",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const holidayDates = new Set(holidays.map((h) => h.date.split("T")[0]));

	// 2. Hitung total hari kerja
	let totalWorkDays = 0;
	const currentDate = new Date(startDate);
	while (currentDate <= endDate) {
		const day = currentDate.getDay();
		const dateStr = currentDate.toISOString().split("T")[0];

		if (day !== 0 && day !== 6 && !holidayDates.has(dateStr)) {
			totalWorkDays++;
		}

		currentDate.setDate(currentDate.getDate() + 1);
	}

	// 3. Ambil log absensi user
	const attendanceLogs = await db
		.selectFrom("attendanceLogs")
		.select(["logDate", "type", "status"])
		.where("userId", "=", userId as UsersId)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const logsByDate = new Map<
		string,
		{ checkIn?: boolean; checkOut?: boolean }
	>();
	for (const log of attendanceLogs) {
		const date = log.logDate.split("T")[0] as string;
		const entry = logsByDate.get(date) ?? { checkIn: false, checkOut: false };

		if (
			log.type === AttendanceType.CHECK_IN &&
			[AttendanceStatus.ON_TIME, AttendanceStatus.LATE].includes(
				log.status as AttendanceStatus,
			)
		) {
			entry.checkIn = true;
		}

		if (
			log.type === AttendanceType.CHECK_OUT &&
			[AttendanceStatus.ON_TIME, AttendanceStatus.EARLY].includes(
				log.status as AttendanceStatus,
			)
		) {
			entry.checkOut = true;
		}

		logsByDate.set(date, entry);
	}

	// 4. Ambil tanggal cuti yang isCountedAsPresent = true
	const leaveRequests = await db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leaveRequests.leavePolicyId",
			"leavePolicies.id",
		)
		.select([
			"leaveRequests.startDate",
			"leaveRequests.endDate",
			"leavePolicies.isCountedAsPresent",
		])
		.where("leaveRequests.userId", "=", userId as UsersId)
		.where("leaveRequests.status", "=", "APPROVED")
		.where("leavePolicies.isCountedAsPresent", "=", true)
		.where((eb) =>
			eb.or([
				eb.between(
					"leaveRequests.startDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.between(
					"leaveRequests.endDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.and([
					eb("leaveRequests.startDate", "<=", toDateOnlyString(startDate)),
					eb("leaveRequests.endDate", ">=", toDateOnlyString(endDate)),
				]),
			]),
		)
		.execute();

	const leaveDates = new Set<string>();
	for (const leave of leaveRequests) {
		const d = new Date(leave.startDate);
		const until = new Date(leave.endDate);

		while (d <= until) {
			const day = d.getDay();
			const dateStr = d.toISOString().split("T")[0] as string;

			if (day !== 0 && day !== 6 && !holidayDates.has(dateStr)) {
				leaveDates.add(dateStr);
			}

			d.setDate(d.getDate() + 1);
		}
	}

	// 5. Hitung total hari hadir
	let totalAttendanceDays = 0;
	const iterDate = new Date(startDate);

	while (iterDate <= endDate) {
		const dateStr = iterDate.toISOString().split("T")[0] as string;
		const isHoliday = holidayDates.has(dateStr);
		const isWeekend = iterDate.getDay() === 0 || iterDate.getDay() === 6;

		if (isHoliday || isWeekend) {
			iterDate.setDate(iterDate.getDate() + 1);
			continue;
		}

		const isPresent =
			logsByDate.get(dateStr)?.checkIn && logsByDate.get(dateStr)?.checkOut;
		const isLeaveCounted = leaveDates.has(dateStr);

		if (isPresent || isLeaveCounted) {
			totalAttendanceDays++;
		}

		iterDate.setDate(iterDate.getDate() + 1);
	}

	// 6. Hitung persentase
	const attendancePoint =
		totalWorkDays > 0 ? (totalAttendanceDays / totalWorkDays) * 100 : 0;

	return {
		attendancePoint,
		totalWorkDays,
		totalAttendanceDays,
	};
};

const _calculateViolationPenalty = async (
	userId: string,
	startDate: Date,
	endDate: Date,
	taskPoint: number,
	attendancePoint: number,
): Promise<{ violationAverageScore: number; totalViolations: number }> => {
	const violations = await db
		.selectFrom("violations")
		.innerJoin(
			"violationTypes",
			"violationTypes.id",
			"violations.violationTypeId",
		)
		.select(["violationTypes.penaltyPoints"])
		.where("userId", "=", userId as UsersId)
		.where((eb) =>
			eb.between(
				"violationDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	if (violations.length === 0) {
		return { violationAverageScore: 0, totalViolations: 0 };
	}

	const totalPenaltyPoints = violations.reduce((sum, violation) => {
		return sum + (violation.penaltyPoints || 0);
	}, 0);

	const averagePenaltyPoints = totalPenaltyPoints / violations.length;
	const averageInitialScore = (taskPoint + attendancePoint) / 2;
	const violationAverageScore =
		(averagePenaltyPoints / 100) * averageInitialScore;

	return { violationAverageScore, totalViolations: violations.length };
};

export const getKPIPoint = async ({
	user,
	period,
}: {
	user: AuthUser;
	period?: string;
}): Promise<KPIPointResponseDTO> => {
	let currentPeriod = period;
	if (!currentPeriod) {
		currentPeriod = new Date().getFullYear().toString();
	} else {
		const yearNum = Number.parseInt(currentPeriod);
		if (Number.isNaN(yearNum) || yearNum < 1900 || yearNum > 9999) {
			currentPeriod = new Date().getFullYear().toString();
		}
	}

	const cacheKey = CACHE_KEYS.kpiPoints(user.id, currentPeriod);
	const cached = await cacheHelpers.get<KPIPointResponseDTO>(cacheKey);

	if (cached) {
		return cached;
	}

	const kpiRecord = await db
		.selectFrom("kpiRecords")
		.select([
			"period",
			"finalKpiScore",
			"taskAverageScore",
			"violationAverageScore",
		])
		.where("userId", "=", user.id as UsersId)
		.where("period", "=", currentPeriod)
		.executeTakeFirst();

	if (kpiRecord) {
		await cacheHelpers.set<KPIPointResponseDTO>(
			cacheKey,
			{
				period: kpiRecord.period,
				finalPoint: kpiRecord.finalKpiScore,
				taskPoint: kpiRecord.taskAverageScore,
				violationPenaltyPoint: kpiRecord.violationAverageScore,
				canAddViolation: !!user.roles.find(
					(role) => role.id === Env.HR_ROLE_ID,
				),
			},
			CACHE_TTL.KPI_POINTS,
		);

		return {
			period: kpiRecord.period,
			finalPoint: kpiRecord.finalKpiScore,
			taskPoint: kpiRecord.taskAverageScore,
			violationPenaltyPoint: kpiRecord.violationAverageScore,
			canAddViolation: !!user.roles.find((role) => role.id === Env.HR_ROLE_ID),
		};
	}

	// Kembalikan nilai default + cache
	const defaultData: KPIPointResponseDTO = {
		period: currentPeriod,
		finalPoint: 0,
		taskPoint: 0,
		violationPenaltyPoint: 0,
		canAddViolation: !!user.roles.find((role) => role.id === Env.HR_ROLE_ID),
	};

	await cacheHelpers.set(cacheKey, defaultData, CACHE_TTL.KPI_POINTS);

	return defaultData;
};

// TODO: Jalankan secara schedule lewat queue
export const recalculateAndUpsertKpiPoints = async (
	userId: string,
	period?: string,
): Promise<void> => {
	let currentPeriod = period;
	if (!currentPeriod) {
		currentPeriod = new Date().getFullYear().toString();
	} else {
		const yearNum = Number.parseInt(currentPeriod);
		if (Number.isNaN(yearNum) || yearNum < 1900 || yearNum > 9999) {
			currentPeriod = new Date().getFullYear().toString();
		}
	}
	const { startDate, endDate } = _getPeriodDateRange(currentPeriod);

	const { taskPoint, totalTasks } = await _calculateTaskAverageScore(
		userId,
		startDate,
		endDate,
	);
	const { attendancePoint, totalWorkDays, totalAttendanceDays } =
		await _calculateAttendancePercentage(userId, startDate, endDate);
	const { violationAverageScore, totalViolations } =
		await _calculateViolationPenalty(
			userId,
			startDate,
			endDate,
			taskPoint,
			attendancePoint,
		);

	const averageInitialScore = (taskPoint + attendancePoint) / 2;
	const finalKpiScore = Math.max(
		0,
		averageInitialScore - violationAverageScore,
	);

	const existing = await db
		.selectFrom("kpiRecords")
		.select(["id"])
		.where("userId", "=", userId as UsersId)
		.where("period", "=", currentPeriod)
		.executeTakeFirst();

	if (existing) {
		await db
			.updateTable("kpiRecords")
			.set({
				taskAverageScore: taskPoint,
				attendancePercentage: attendancePoint,
				violationAverageScore,
				finalKpiScore,
				totalTasks,
				totalWorkDays,
				totalAttendanceDays,
				totalViolations,
				updatedAt: new Date().toISOString(),
			})
			.where("id", "=", existing.id)
			.execute();
	} else {
		await db
			.insertInto("kpiRecords")
			.values({
				id: nanoid() as KpiRecordsId,
				userId: userId as UsersId,
				period: currentPeriod,
				taskAverageScore: taskPoint,
				attendancePercentage: attendancePoint,
				violationAverageScore,
				finalKpiScore,
				totalTasks,
				totalWorkDays,
				totalAttendanceDays,
				totalViolations,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();
	}

	// ⏹️ Cache hasil final ke Redis (replace cache lama)
	const newCache: KPIPointResponseDTO = {
		period: currentPeriod,
		finalPoint: finalKpiScore,
		taskPoint,
		violationPenaltyPoint: violationAverageScore,
		canAddViolation: true,
	};

	const cacheKey = CACHE_KEYS.kpiPoints(userId, currentPeriod);
	await cacheHelpers.set(cacheKey, newCache, CACHE_TTL.KPI_POINTS);
};
