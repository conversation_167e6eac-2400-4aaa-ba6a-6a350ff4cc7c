import { parse } from "node:querystring";
import { db } from "@/database/connection";
import type { KpiRecordsId } from "@/database/types/public/KpiRecords";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type KPIRecordQueryDTO,
	type KPIRecordResponseDTO,
	kpiRecordQueryDTO,
} from "../dtos";

const KPI_FILTERABLE_FIELDS: FilterableFields = {
	id: "kpiRecords.id",
	userName: "users.name",
	userEmail: "users.email",
	period: "kpiRecords.period",
	taskAverageScore: "kpiRecords.taskAverageScore",
	attendancePercentage: "kpiRecords.attendancePercentage",
	violationAverageScore: "kpiRecords.violationAverageScore",
	finalKpiScore: "kpiRecords.finalKpiScore",
	createdAt: "kpiRecords.createdAt",
} as const;

const KPI_FIELD_TYPES: FieldTypes = {
	id: "string",
	userName: "string",
	userEmail: "string",
	period: "string",
	taskAverageScore: "number",
	attendancePercentage: "number",
	violationAverageScore: "number",
	finalKpiScore: "number",
	createdAt: "date",
} as const;

const KPI_SORTABLE_FIELDS: SortableFields = {
	id: "kpiRecords.id",
	userName: "users.name",
	userEmail: "users.email",
	period: "kpiRecords.period",
	taskAverageScore: "kpiRecords.taskAverageScore",
	attendancePercentage: "kpiRecords.attendancePercentage",
	violationAverageScore: "kpiRecords.violationAverageScore",
	finalzKpiScore: "kpiRecords.finalKpiScore",
	createdAt: "kpiRecords.createdAt",
} as const;

export const getAllKPIRecord = async (
	queryParams: KPIRecordQueryDTO,
): Promise<{ data: KPIRecordResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		userName,
		userEmail,
		period,
		taskAverageScore,
		attendancePercentage,
		violationAverageScore,
		finalKpiScore,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("kpiRecords")
		.innerJoin("users", "users.id", "kpiRecords.userId")
		.selectAll("kpiRecords")
		.select(["users.name as userName", "users.email as userEmail"]);

	const countQuery = db
		.selectFrom("kpiRecords")
		.innerJoin("users", "users.id", "kpiRecords.userId");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		userName?: string | string[];
		userEmail?: string | string[];
		period?: string | string[];
		taskAverageScore?: string | string[];
		attendancePercentage?: string | string[];
		violationAverageScore?: string | string[];
		finalKpiScore?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (period) filters.period = period;
	if (taskAverageScore) filters.taskAverageScore = taskAverageScore;
	if (attendancePercentage) filters.attendancePercentage = attendancePercentage;
	if (violationAverageScore)
		filters.violationAverageScore = violationAverageScore;
	if (finalKpiScore) filters.finalKpiScore = finalKpiScore;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: KPI_FIELD_TYPES,
			filterableFields: KPI_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: KPI_SORTABLE_FIELDS,
		defaultSort: {
			field: "kpiRecords.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [kpiRecords, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: kpiRecords,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("kpiRecords")
		.innerJoin("users", "users.id", "kpiRecords.userId")
		.selectAll("kpiRecords")
		.select(["users.name as userName", "users.email as userEmail"]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			userName,
			userEmail,
			period,
			taskAverageScore,
			attendancePercentage,
			violationAverageScore,
			finalKpiScore,
			createdAt,
		} = kpiRecordQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filtering
		const filters: {
			userName?: string | string[];
			userEmail?: string | string[];
			period?: string | string[];
			taskAverageScore?: string | string[];
			attendancePercentage?: string | string[];
			violationAverageScore?: string | string[];
			finalKpiScore?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (period) filters.period = period;
		if (taskAverageScore) filters.taskAverageScore = taskAverageScore;
		if (attendancePercentage)
			filters.attendancePercentage = attendancePercentage;
		if (violationAverageScore)
			filters.violationAverageScore = violationAverageScore;
		if (finalKpiScore) filters.finalKpiScore = finalKpiScore;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: KPI_FILTERABLE_FIELDS,
			fieldTypes: KPI_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: KPI_SORTABLE_FIELDS,
			defaultSort: {
				field: "kpiRecords.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as KpiRecordsId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"kpiRecords.id",
			"in",
			selection.selectedIds as KpiRecordsId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
