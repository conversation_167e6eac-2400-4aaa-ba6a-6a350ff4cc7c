import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminKpiController from "../controllers/admin-kpi.controller";
import { kpiRecordQueryDTO } from "../dtos";

export const adminKPIRouter = Router();

adminKPIRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(kpiRecordQueryDTO),
	adminKpiController.getAllKPIRecord,
);

adminKPIRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminKpiController.bulkAction,
);
