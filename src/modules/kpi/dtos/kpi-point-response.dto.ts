import * as z from "zod";

export const KPIPointResponseDTO = z.object({
	period: z.string().meta({
		example: "2025-01",
		description:
			"Period of KPI calculation (YYYY-MM for monthly, YYYY for yearly)",
	}),
	taskPoint: z.number().meta({
		example: 79,
		description: "Task average point",
	}),
	violationPenaltyPoint: z.number().meta({
		example: 8,
		description: "Violation penalty point",
	}),
	finalPoint: z.number().meta({
		example: 78,
		description: "Final KPI point",
	}),
	canAddViolation: z.boolean().meta({
		example: true,
	}),
});
export type KPIPointResponseDTO = z.infer<typeof KPIPointResponseDTO>;
