import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const kpiRecord = z
	.object({
		id: z
			.string()
			.meta({ description: "Unique identifier for the KPI record" }),
		userId: z.string().meta({ description: "ID of the user" }),
		period: z.string().meta({
			description: "Period of KPI calculation (year)",
			example: "2023",
		}),
		taskAverageScore: z
			.number()
			.meta({ description: "Average score from all tasks" }),
		attendancePercentage: z
			.number()
			.meta({ description: "Attendance percentage" }),
		violationAverageScore: z
			.number()
			.meta({ description: "Penalty points from violations" }),
		finalKpiScore: z
			.number()
			.meta({ description: "Final calculated KPI score" }),
		totalTasks: z.number().meta({ description: "Total number of tasks" }),
		totalWorkDays: z
			.number()
			.meta({ description: "Total work days in period" }),
		totalAttendanceDays: z
			.number()
			.meta({ description: "Total attendance days" }),
		totalViolations: z
			.number()
			.meta({ description: "Total violations in period" }),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("KpiRecord");
export type KPIRecord = z.infer<typeof kpiRecord>;

export const kpiRecordQueryDTO = tableQueryDTO.extend({
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	period: filterFieldDTO.optional(),
	taskAverageScore: filterFieldDTO.optional(),
	attendancePercentage: filterFieldDTO.optional(),
	violationAverageScore: filterFieldDTO.optional(),
	finalKpiScore: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type KPIRecordQueryDTO = z.infer<typeof kpiRecordQueryDTO>;
