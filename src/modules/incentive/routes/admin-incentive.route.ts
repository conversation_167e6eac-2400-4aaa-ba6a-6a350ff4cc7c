import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminIncentiveController from "../controllers/admin-incentive.controller";
import { incentiveRecordQueryDTO } from "../dtos";

export const adminIncentiveRouter = Router();

adminIncentiveRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(incentiveRecordQueryDTO),
	adminIncentiveController.getAllIncentiveRecord,
);

adminIncentiveRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateB<PERSON>(bulkActionDTO),
	adminIncentiveController.bulkAction,
);
