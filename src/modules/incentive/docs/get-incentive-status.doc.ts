import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { incentiveStatusResponseDTO } from "../dtos";

registry.registerPath({
	summary: "Get Incentive Status",
	method: "get",
	path: "/api/v1/incentives/status",
	tags: ["incentive"],
	description:
		"Get incentive eligibility status for all three incentive types (Attendance, Presence, Motor) for the authenticated user",
	security: [{ bearerAuth: [] }],
	request: {
		query: z.object({
			month: z.number().optional(),
			year: z.number().optional(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							incentives: incentiveStatusResponseDTO,
						})
					}),
				},
			},
		},
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
