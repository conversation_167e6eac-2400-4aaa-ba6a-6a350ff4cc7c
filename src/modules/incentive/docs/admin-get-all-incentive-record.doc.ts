import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { incentiveRecordResponseDTO } from "../dtos";

registry.registerPath({
	summary: "Get All Incentive Record [Admin]",
	method: "get",
	path: "/api/v1/admin/incentives",
	tags: ["incentive"],
	description:
		"Get all kpi record for tabular data, support sorting, filtering, paginating, and searching",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(incentiveRecordResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
