import * as z from "zod";

export const incentiveStatusResponseDTO = z
	.object({
		incentiveType: z.string().meta({
			example: "ATTENDANCE",
			description: "Type of incentive (ATTENDANCE, PRESENCE, MOTOR)",
		}),
		isEligible: z.boolean().meta({
			example: true,
			description: "Whether user is eligible for this incentive",
		}),
		period: z.string().meta({
			example: "2024-12-26_2025-01-25",
			description: "Period for incentive calculation based on cut-off rules",
		}),
		notes: z.string().nullable().meta({
			example: "Memenuhi semua syarat insentif absensi",
			description: "Additional notes about incentive eligibility",
		}),
		requirements: z.object({
			noLateCheckIn: z.boolean(),
			noEarlyCheckOut: z.boolean(),
			noMissedAttendance: z.boolean(),
			noLeaveNotCountedAsPresent: z.boolean(),
			fullMonthPresence: z.boolean(),
			allOfficeLeaveOfficial: z.boolean(),
		}),
		amount: z.number().nullable().meta({
			example: 100000,
			description: "Amount of incentive if eligible",
		}),
		calculatedAt: z.string().nullable().meta({
			example: "2025-01-25T00:00:00.000Z",
			description: "Timestamp when the incentive was calculated",
		}),
	})
	.meta({
		description: "List of incentive status for all three types",
	});
export type IncentiveStatusResponseDTO = z.infer<
	typeof incentiveStatusResponseDTO
>;
