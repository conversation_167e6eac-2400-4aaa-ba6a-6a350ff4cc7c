import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const incentiveRecord = z
	.object({
		id: z
			.string()
			.meta({ description: "Unique identifier for the incentive record" }),
		userId: z.string().meta({ description: "ID of the user" }),
		incentiveType: z.string().meta({
			description: "Type of incentive (ATTENDANCE, PRESENCE, MOTOR)",
		}),
		period: z
			.string()
			.meta({ description: "Period for incentive calculation" }),
		isEligible: z
			.boolean()
			.meta({ description: "Whether user is eligible for this incentive" }),
		amount: z
			.number()
			.nullable()
			.meta({ description: "Incentive amount (if applicable)" }),
		/**
		 *  noLateCheckIn: ColumnType<boolean, boolean | undefined, boolean>;
		 
			 noEarlyCheckOut: ColumnType<boolean, boolean | undefined, boolean>;
		 
			 noMissedAttendance: ColumnType<boolean, boolean | undefined, boolean>;
		 
			 noLeaveNotCountedAsPresent: ColumnType<boolean, boolean | undefined, boolean>;
		 
			 fullMonthPresence: ColumnType<boolean, boolean | undefined, boolean>;
		 
			 allOfficeLeaveOfficial: ColumnType<boolean, boolean | undefined, boolean>;
		 */
		noLateCheckIn: z
			.boolean()
			.meta({ description: "No late check-in for the month" }),
		noEarlyCheckOut: z
			.boolean()
			.meta({ description: "No early check-out for the month" }),
		noMissedAttendance: z
			.boolean()
			.meta({ description: "No missed attendance for the month" }),
		noLeaveNotCountedAsPresent: z
			.boolean()
			.meta({ description: "No leave not counted as present for the month" }),
		fullMonthPresence: z
			.boolean()
			.meta({ description: "Full month presence for the month" }),
		allOfficeLeaveOfficial: z
			.boolean()
			.meta({ description: "All office leave official for the month" }),
		notes: z
			.string()
			.nullable()
			.meta({ description: "Additional notes about incentive calculation" }),
		calculatedAt: z.iso.date().meta({
			description: "When the incentive was calculated",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("IncentiveRecord");

export const incentiveRecordQueryDTO = tableQueryDTO.extend({
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	incentiveType: filterFieldDTO.optional(),
	isEligible: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type IncentiveRecordQueryDTO = z.infer<typeof incentiveRecordQueryDTO>;
