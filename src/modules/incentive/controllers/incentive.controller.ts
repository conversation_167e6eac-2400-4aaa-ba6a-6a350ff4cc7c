import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import * as incentiveService from "../services/incentive.service";

export const getIncentiveStatus = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const month = Number(req.query?.month);
		const year = Number(req.query?.year);

		const filter: { month?: number; year?: number } = {};
		if (!Number.isNaN(month)) filter.month = month;
		if (!Number.isNaN(year)) filter.year = year;

		const result = await incentiveService.getMyIncentiveStatus(
			req.user as AuthUser,
			filter,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				incentives: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
