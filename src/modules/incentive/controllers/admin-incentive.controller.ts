import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { IncentiveRecordQueryDTO } from "../dtos";
import * as adminIncentiveService from "../services/admin-incentive.service";

export const getAllIncentiveRecord = async (
	req: Request,
	res: Response,
	next: NextFunction,
): Promise<void> => {
	try {
		const validatedQuery = req.validatedQuery as IncentiveRecordQueryDTO;

		const result =
			await adminIncentiveService.getAllIncentiveRecord(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminIncentiveService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=incentiveRecords.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
