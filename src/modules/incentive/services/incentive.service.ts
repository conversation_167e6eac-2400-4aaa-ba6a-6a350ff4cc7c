import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { IncentiveRecordsId } from "@/database/types/public/IncentiveRecords";
import type { UsersId } from "@/database/types/public/Users";
import type { IncentiveStatusResponseDTO } from "@/modules/incentive/dtos";
import { AttendanceStatus, AttendanceType } from "@/shared/enums";
import { IncentiveType } from "@/shared/enums/performance.enum";
import { CACHE_KEYS, CACHE_TTL, cacheHelpers } from "@/shared/lib/redis";
import type { AuthUser } from "@/shared/types";
import { toDateOnlyString } from "@/shared/utils/date";

interface IncentiveRequirementState {
	noEarlyCheckOut: boolean;
	noLateCheckIn: boolean;
	noMissedAttendance: boolean;
	noLeaveNotCountedAsPresent: boolean;
	fullMonthPresence: boolean;
	allOfficeLeaveOfficial: boolean;
}

interface IncentiveFilter {
	month?: number; // 1-12
	year?: number;
}

export const getMyIncentiveStatus = async (
	user: AuthUser,
	filter?: IncentiveFilter,
): Promise<IncentiveStatusResponseDTO[]> => {
	const results: IncentiveStatusResponseDTO[] = [];

	for (const incentiveType of Object.values(IncentiveType)) {
		const incentiveStatus = await getIncentiveStatus(
			user.id,
			incentiveType,
			filter,
		);
		if (incentiveStatus) {
			results.push(incentiveStatus);
		}
	}

	return results;
};

export const getIncentiveStatus = async (
	userId: string,
	incentiveType: IncentiveType,
	filter?: IncentiveFilter,
): Promise<IncentiveStatusResponseDTO | null> => {
	// Dapatkan periode yang sesuai dengan filter
	const periodData = _getIncentivePeriod(incentiveType, filter);
	if (!periodData) return null; // Tidak ada periode yang cocok

	const { period, startDate, endDate, isCurrentPeriod } = periodData;

	// Cek cache terlebih dahulu
	const cacheKey = CACHE_KEYS.incentive(userId, incentiveType, period);
	const cached = await cacheHelpers.get<IncentiveStatusResponseDTO>(cacheKey);
	if (cached) {
		return cached;
	}

	// Cari record yang sudah ada
	const record = await db
		.selectFrom("incentiveRecords")
		.selectAll()
		.where("userId", "=", userId as UsersId)
		.where("incentiveType", "=", incentiveType)
		.where("period", "=", period)
		.executeTakeFirst();

	if (record) {
		const result: IncentiveStatusResponseDTO = {
			period,
			incentiveType,
			isEligible: record.isEligible,
			notes: record.notes,
			requirements: {
				noEarlyCheckOut: record.noEarlyCheckOut,
				noLateCheckIn: record.noLateCheckIn,
				noMissedAttendance: record.noMissedAttendance,
				noLeaveNotCountedAsPresent: record.noLeaveNotCountedAsPresent,
				fullMonthPresence: record.fullMonthPresence,
				allOfficeLeaveOfficial: record.allOfficeLeaveOfficial,
			},
			amount: record.amount,
			calculatedAt: record.calculatedAt,
		};

		await cacheHelpers.set(cacheKey, result, CACHE_TTL.INCENTIVE);
		return result;
	}

	// Jika tidak ada record dan bukan periode saat ini, return null
	if (!isCurrentPeriod) {
		return null;
	}

	// Jika belum ada perhitungan dan ini periode saat ini, buat default dan hitung
	const requirements = await _getIncentiveRequirements(
		userId,
		incentiveType,
		startDate,
		endDate,
	);

	const { isEligible, notes } = _evaluateIncentiveEligibility(
		incentiveType,
		requirements,
	);

	const defaultIncentive: IncentiveStatusResponseDTO = {
		period,
		incentiveType,
		isEligible,
		notes,
		requirements,
		amount: null,
		calculatedAt: new Date().toISOString(),
	};

	// Simpan ke database
	await db
		.insertInto("incentiveRecords")
		.values({
			id: nanoid() as IncentiveRecordsId,
			userId: userId as UsersId,
			incentiveType,
			period,
			isEligible,
			notes,
			noEarlyCheckOut: requirements.noEarlyCheckOut,
			noLateCheckIn: requirements.noLateCheckIn,
			noMissedAttendance: requirements.noMissedAttendance,
			noLeaveNotCountedAsPresent: requirements.noLeaveNotCountedAsPresent,
			fullMonthPresence: requirements.fullMonthPresence,
			allOfficeLeaveOfficial: requirements.allOfficeLeaveOfficial,
			amount: null,
			calculatedAt: new Date().toISOString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.executeTakeFirstOrThrow();

	// Cache hasil
	await cacheHelpers.set(cacheKey, defaultIncentive, CACHE_TTL.INCENTIVE);

	return defaultIncentive;
};

export const recalculateAndUpsertIncentiveRecord = async (
	userId: string,
	incentiveType: IncentiveType,
	filter?: IncentiveFilter,
) => {
	const periodData = _getIncentivePeriod(incentiveType, filter);
	if (!periodData) {
		throw new Error("Cannot recalculate incentive for non-existent period");
	}

	const { period, startDate, endDate } = periodData;

	// Hitung semua requirement dari user
	const requirements = await _getIncentiveRequirements(
		userId,
		incentiveType,
		startDate,
		endDate,
	);

	// Evaluasi apakah eligible atau tidak
	const { isEligible, notes } = _evaluateIncentiveEligibility(
		incentiveType,
		requirements,
	);

	// Cek apakah sudah ada record sebelumnya
	const existing = await db
		.selectFrom("incentiveRecords")
		.select(["id"])
		.where("userId", "=", userId as UsersId)
		.where("incentiveType", "=", incentiveType)
		.where("period", "=", period)
		.executeTakeFirst();

	if (existing) {
		await db
			.updateTable("incentiveRecords")
			.set({
				isEligible,
				notes,
				noEarlyCheckOut: requirements.noEarlyCheckOut,
				noLateCheckIn: requirements.noLateCheckIn,
				noMissedAttendance: requirements.noMissedAttendance,
				noLeaveNotCountedAsPresent: requirements.noLeaveNotCountedAsPresent,
				fullMonthPresence: requirements.fullMonthPresence,
				allOfficeLeaveOfficial: requirements.allOfficeLeaveOfficial,
				amount: null,
				calculatedAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.where("id", "=", existing.id)
			.executeTakeFirstOrThrow();
	} else {
		await db
			.insertInto("incentiveRecords")
			.values({
				id: nanoid() as IncentiveRecordsId,
				userId: userId as UsersId,
				incentiveType,
				period,
				isEligible,
				notes,
				noEarlyCheckOut: requirements.noEarlyCheckOut,
				noLateCheckIn: requirements.noLateCheckIn,
				noMissedAttendance: requirements.noMissedAttendance,
				noLeaveNotCountedAsPresent: requirements.noLeaveNotCountedAsPresent,
				fullMonthPresence: requirements.fullMonthPresence,
				allOfficeLeaveOfficial: requirements.allOfficeLeaveOfficial,
				amount: null,
				calculatedAt: new Date().toISOString(),
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();
	}

	// Cache ke Redis
	const cacheKey = CACHE_KEYS.incentive(userId, incentiveType, period);
	const incentive: IncentiveStatusResponseDTO = {
		period,
		incentiveType,
		isEligible,
		notes,
		requirements,
		amount: null,
		calculatedAt: new Date().toISOString(),
	};
	await cacheHelpers.set(cacheKey, incentive, CACHE_TTL.INCENTIVE);
};

const _getIncentivePeriod = (
	incentiveType: IncentiveType,
	filter?: IncentiveFilter,
): {
	period: string;
	startDate: Date;
	endDate: Date;
	isCurrentPeriod: boolean;
} | null => {
	const now = new Date();
	let targetDate = now;

	// Jika ada filter, gunakan tanggal berdasarkan filter
	if (filter?.year && filter?.month) {
		targetDate = new Date(filter.year, filter.month - 1, 1);
	} else if (filter?.year) {
		targetDate = new Date(filter.year, 0, 1);
	} else if (filter?.month) {
		targetDate = new Date(now.getFullYear(), filter.month - 1, 1);
	}

	switch (incentiveType) {
		case IncentiveType.ATTENDANCE: {
			// Cut off: tanggal 26 bulan sebelumnya sampai tanggal 25 bulan berjalan
			let currentMonth: Date;
			let prevMonth: Date;

			if (filter?.year && filter?.month) {
				currentMonth = new Date(filter.year, filter.month - 1, 25, 23, 59, 59);
				prevMonth = new Date(filter.year, filter.month - 2, 26);
			} else {
				currentMonth = new Date(
					targetDate.getFullYear(),
					targetDate.getMonth(),
					25,
					23,
					59,
					59,
				);
				prevMonth = new Date(
					targetDate.getFullYear(),
					targetDate.getMonth() - 1,
					26,
				);
			}

			const period = `${prevMonth.getFullYear()}-${String(prevMonth.getMonth() + 1).padStart(2, "0")}-26_${currentMonth.getFullYear()}-${String(currentMonth.getMonth() + 1).padStart(2, "0")}-25`;

			// Cek apakah ini periode saat ini
			const nowPrevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 26);
			const nowCurrentMonth = new Date(
				now.getFullYear(),
				now.getMonth(),
				25,
				23,
				59,
				59,
			);
			const currentPeriod = `${nowPrevMonth.getFullYear()}-${String(nowPrevMonth.getMonth() + 1).padStart(2, "0")}-26_${nowCurrentMonth.getFullYear()}-${String(nowCurrentMonth.getMonth() + 1).padStart(2, "0")}-25`;

			return {
				period,
				startDate: prevMonth,
				endDate: currentMonth,
				isCurrentPeriod: period === currentPeriod,
			};
		}

		case IncentiveType.PRESENCE: {
			// Cut off: tanggal 1 sampai akhir bulan
			const year = filter?.year ?? targetDate.getFullYear();
			const month = filter?.month ?? targetDate.getMonth() + 1;

			const firstDay = new Date(year, month - 1, 1);
			const lastDay = new Date(year, month, 0, 23, 59, 59);
			const period = `${year}-${String(month).padStart(2, "0")}`;

			// Cek apakah ini periode saat ini
			const currentPeriod = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;

			return {
				period,
				startDate: firstDay,
				endDate: lastDay,
				isCurrentPeriod: period === currentPeriod,
			};
		}

		case IncentiveType.MOTOR: {
			// Cut off: 1 Januari – 31 Desember
			const year = filter?.year ?? targetDate.getFullYear();

			const yearStart = new Date(year, 0, 1);
			const yearEnd = new Date(year, 11, 31, 23, 59, 59);
			const period = year.toString();

			// Cek apakah ini periode saat ini
			const currentPeriod = now.getFullYear().toString();

			return {
				period,
				startDate: yearStart,
				endDate: yearEnd,
				isCurrentPeriod: period === currentPeriod,
			};
		}

		default:
			throw new Error(`Unknown incentive type: ${incentiveType}`);
	}
};

const _getIncentiveRequirements = async (
	userId: string,
	incentiveType: IncentiveType,
	startDate: Date,
	endDate: Date,
): Promise<IncentiveRequirementState> => {
	const [
		noLateCheckIn,
		noEarlyCheckOut,
		noMissedAttendance,
		noLeaveNotCountedAsPresent,
		fullMonthPresence,
		allOfficeLeaveOfficial,
	] = await Promise.all([
		_checkNoLateCheckIn(userId, startDate, endDate),
		_checkNoEarlyCheckOut(userId, startDate, endDate),
		_checkNoMissedAttendance(userId, startDate, endDate),
		_checkHasLeaveIsNoCountedAsPresent(userId, startDate, endDate),
		incentiveType !== IncentiveType.ATTENDANCE
			? _checkFullMonthPresence(userId, startDate, endDate)
			: Promise.resolve(true),
		incentiveType === IncentiveType.MOTOR ||
		incentiveType === IncentiveType.PRESENCE
			? _checkAllOfficeLeaveOfficial(userId, startDate, endDate)
			: Promise.resolve(true),
	]);

	return {
		noLateCheckIn,
		noEarlyCheckOut,
		noMissedAttendance,
		noLeaveNotCountedAsPresent,
		fullMonthPresence,
		allOfficeLeaveOfficial,
	};
};

const _evaluateIncentiveEligibility = (
	incentiveType: IncentiveType,
	req: IncentiveRequirementState,
): { isEligible: boolean; notes: string } => {
	const failed: string[] = [];

	if (!req.noLateCheckIn) failed.push("Terlambat saat check-in");
	if (!req.noEarlyCheckOut) failed.push("Check-out lebih awal");
	if (!req.noMissedAttendance) failed.push("Ada hari kerja tanpa absensi");
	if (!req.noLeaveNotCountedAsPresent)
		failed.push("Ada cuti yang tidak dianggap hadir");

	if (
		incentiveType === IncentiveType.PRESENCE ||
		incentiveType === IncentiveType.MOTOR
	) {
		if (!req.fullMonthPresence) failed.push("Tidak hadir penuh dalam sebulan");
		if (!req.allOfficeLeaveOfficial)
			failed.push("Ada izin keluar yang tidak resmi");
	}

	const isEligible = failed.length === 0;
	return {
		isEligible,
		notes: isEligible
			? "Memenuhi semua syarat"
			: `Tidak memenuhi syarat: ${failed.join(", ")}`,
	};
};

// HELPER FUNCTIONS - Tetap sama seperti kode asli
const _checkNoLateCheckIn = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	const lateLogs = await db
		.selectFrom("attendanceLogs")
		.select("id")
		.where("userId", "=", userId as UsersId)
		.where("type", "=", AttendanceType.CHECK_IN)
		.where("status", "=", AttendanceStatus.LATE)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	return lateLogs.length === 0;
};

const _checkNoEarlyCheckOut = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	const earlyLogs = await db
		.selectFrom("attendanceLogs")
		.select("id")
		.where("userId", "=", userId as UsersId)
		.where("type", "=", AttendanceType.CHECK_OUT)
		.where("status", "=", AttendanceStatus.EARLY)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	return earlyLogs.length === 0;
};

const _checkNoMissedAttendance = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	const attendanceLogs = await db
		.selectFrom("attendanceLogs")
		.select(["logDate", "type"])
		.where("userId", "=", userId as UsersId)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const logsMap = new Map<string, { checkIn?: boolean; checkOut?: boolean }>();

	for (const log of attendanceLogs) {
		const dateStr = log.logDate.split("T")[0] as string;
		const daily = logsMap.get(dateStr) ?? {};
		if (log.type === AttendanceType.CHECK_IN) daily.checkIn = true;
		if (log.type === AttendanceType.CHECK_OUT) daily.checkOut = true;
		logsMap.set(dateStr, daily);
	}

	const holidays = await db
		.selectFrom("holidays")
		.select("date")
		.where((eb) =>
			eb.between(
				"date",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const holidaySet = new Set(holidays.map((h) => h.date.split("T")[0]));

	const current = new Date(startDate);
	while (current <= endDate) {
		const day = current.getDay();
		const dateStr = current.toISOString().split("T")[0] as string;
		const isWorkDay = day !== 0 && day !== 6 && !holidaySet.has(dateStr);

		if (isWorkDay) {
			const log = logsMap.get(dateStr);
			const hasCheckInOut = log?.checkIn && log?.checkOut;
			if (!hasCheckInOut) return false;
		}

		current.setDate(current.getDate() + 1);
	}

	return true;
};

const _checkHasLeaveIsNoCountedAsPresent = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	const leaves = await db
		.selectFrom("leaveRequests")
		.innerJoin(
			"leavePolicies",
			"leaveRequests.leavePolicyId",
			"leavePolicies.id",
		)
		.select("leaveRequests.id")
		.where("leaveRequests.userId", "=", userId as UsersId)
		.where("leaveRequests.status", "=", "APPROVED")
		.where("leavePolicies.isCountedAsPresent", "=", false)
		.where((eb) =>
			eb.or([
				eb.between(
					"leaveRequests.startDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.between(
					"leaveRequests.endDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.and([
					eb("leaveRequests.startDate", "<=", toDateOnlyString(startDate)),
					eb("leaveRequests.endDate", ">=", toDateOnlyString(endDate)),
				]),
			]),
		)
		.execute();

	return leaves.length === 0;
};

const _checkFullMonthPresence = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	// 1. Ambil semua hari libur nasional
	const holidays = await db
		.selectFrom("holidays")
		.select(["date"])
		.where((eb) =>
			eb.between(
				"date",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const holidaySet = new Set(holidays.map((h) => h.date.split("T")[0]));

	// 2. Ambil semua log absensi
	const logs = await db
		.selectFrom("attendanceLogs")
		.select(["logDate", "type"])
		.where("userId", "=", userId as UsersId)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	const logsByDate = new Map<
		string,
		{ checkIn?: boolean; checkOut?: boolean }
	>();
	for (const log of logs) {
		const dateStr = log.logDate.split("T")[0] as string;
		const existing = logsByDate.get(dateStr) ?? {};
		if (log.type === AttendanceType.CHECK_IN) existing.checkIn = true;
		if (log.type === AttendanceType.CHECK_OUT) existing.checkOut = true;
		logsByDate.set(dateStr, existing);
	}

	// 3. Ambil semua cuti yang APPROVED
	const leaves = await db
		.selectFrom("leaveRequests")
		.select(["startDate", "endDate"])
		.where("userId", "=", userId as UsersId)
		.where("status", "=", "APPROVED")
		.where((eb) =>
			eb.or([
				eb.between(
					"startDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.between(
					"endDate",
					toDateOnlyString(startDate),
					toDateOnlyString(endDate),
				),
				eb.and([
					eb("startDate", "<=", toDateOnlyString(startDate)),
					eb("endDate", ">=", toDateOnlyString(endDate)),
				]),
			]),
		)
		.execute();

	// 4. Ubah cuti menjadi Set tanggal
	const leaveDates = new Set<string>();
	for (const leave of leaves) {
		const d = new Date(leave.startDate);
		const until = new Date(leave.endDate);
		while (d <= until) {
			const day = d.getDay();
			const dateStr = d.toISOString().split("T")[0] as string;
			if (day !== 0 && day !== 6 && !holidaySet.has(dateStr)) {
				leaveDates.add(dateStr);
			}
			d.setDate(d.getDate() + 1);
		}
	}

	// 5. Iterasi semua hari kerja dan cek kehadiran langsung (tanpa cuti)
	const current = new Date(startDate);
	while (current <= endDate) {
		const day = current.getDay();
		const dateStr = current.toISOString().split("T")[0] as string;

		const isWeekend = day === 0 || day === 6;
		const isHoliday = holidaySet.has(dateStr);
		const isWorkday = !isWeekend && !isHoliday;

		if (isWorkday) {
			// ❌ Tidak boleh cuti
			if (leaveDates.has(dateStr)) return false;

			// ❌ Wajib ada CHECK_IN dan CHECK_OUT
			const log = logsByDate.get(dateStr);
			if (!log?.checkIn || !log?.checkOut) return false;
		}

		current.setDate(current.getDate() + 1);
	}

	return true;
};

const _checkAllOfficeLeaveOfficial = async (
	userId: string,
	startDate: Date,
	endDate: Date,
): Promise<boolean> => {
	const leaves = await db
		.selectFrom("officeLeaves")
		.select(["id", "isOfficialBusiness"])
		.where("userId", "=", userId as UsersId)
		.where("isOfficialBusiness", "=", false)
		.where((eb) =>
			eb.between(
				"date",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	return leaves.length === 0;
};
