import { parse } from "node:querystring";
import { db } from "@/database/connection";
import type { IncentiveRecordsId } from "@/database/types/public/IncentiveRecords";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type IncentiveRecordQueryDTO,
	type IncentiveRecordResponseDTO,
	incentiveRecordQueryDTO,
} from "../dtos";

const INCENTIVE_FILTERABLE_FIELDS: FilterableFields = {
	id: "incentiveRecords.id",
	userName: "users.name",
	userEmail: "users.email",
	incentiveType: "incentiveRecords.incentiveType",
	createdAt: "incentiveRecords.createdAt",
} as const;

const INCENTIVE_FIELD_TYPES: FieldTypes = {
	id: "string",
	userName: "string",
	userEmail: "string",
	incentiveType: "string",
	createdAt: "date",
} as const;

const INCENTIVE_SORTABLE_FIELDS: SortableFields = {
	id: "incentiveRecords.id",
	userName: "users.name",
	userEmail: "users.email",
	incentiveType: "incentiveRecords.incentiveType",
	createdAt: "incentiveRecords.createdAt",
} as const;

export const getAllIncentiveRecord = async (
	queryParams: IncentiveRecordQueryDTO,
): Promise<{ data: IncentiveRecordResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		userName,
		userEmail,
		incentiveType,
		isEligible,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("incentiveRecords")
		.innerJoin("users", "users.id", "incentiveRecords.userId")
		.selectAll("incentiveRecords")
		.select(["users.name as userName", "users.email as userEmail"]);

	const countQuery = db
		.selectFrom("incentiveRecords")
		.innerJoin("users", "users.id", "incentiveRecords.userId");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		userName?: string | string[];
		userEmail?: string | string[];
		incentiveType?: string | string[];
		isEligible?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (incentiveType) filters.incentiveType = incentiveType;
	if (isEligible) filters.isEligible = isEligible;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: INCENTIVE_FIELD_TYPES,
			filterableFields: INCENTIVE_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: INCENTIVE_SORTABLE_FIELDS,
		defaultSort: {
			field: "incentiveRecords.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [incentiveRecords, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: incentiveRecords,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("incentiveRecords")
		.innerJoin("users", "users.id", "incentiveRecords.userId")
		.selectAll("incentiveRecords")
		.select(["users.name as userName", "users.email as userEmail"]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			userName,
			userEmail,
			incentiveType,
			isEligible,
			createdAt,
		} = incentiveRecordQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filtering
		const filters: {
			userName?: string | string[];
			userEmail?: string | string[];
			incentiveType?: string | string[];
			isEligible?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (incentiveType) filters.incentiveType = incentiveType;
		if (isEligible) filters.isEligible = isEligible;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: INCENTIVE_FILTERABLE_FIELDS,
			fieldTypes: INCENTIVE_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: INCENTIVE_SORTABLE_FIELDS,
			defaultSort: {
				field: "incentiveRecords.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as IncentiveRecordsId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"incentiveRecords.id",
			"in",
			selection.selectedIds as IncentiveRecordsId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
