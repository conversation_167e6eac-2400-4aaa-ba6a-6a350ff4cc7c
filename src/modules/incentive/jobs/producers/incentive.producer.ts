import { IncentiveType } from "@/shared/enums";
import { incentiveQueue } from "../queues/incentive.queue";

export async function scheduleDailyIncentiveJobs() {
	const types = [
		IncentiveType.ATTENDANCE,
		IncentiveType.PRESENCE,
		IncentiveType.MOTOR,
	];

	for (const type of types) {
		await incentiveQueue.upsertJobScheduler(
			`incentive-daily-scheduler-${type}`, // unique ID per type
			{
				pattern: "0 0 23 * * *", // 23:00 WIB
				tz: "Asia/Jakarta",
			},
			{
				name: `recalculate-incentive-${type}`,
				data: {},
				opts: {
					attempts: 3,
					backoff: { type: "exponential", delay: 5000 },
					removeOnComplete: true,
					removeOnFail: 50,
				},
			},
		);

		console.log(`📅 Incentive job scheduler registered for type: ${type}`);
	}
}
