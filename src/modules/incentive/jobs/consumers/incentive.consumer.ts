import { type Job, Worker } from "bullmq";
import Redis from "ioredis";
import { db } from "@/database/connection";
import type { IncentiveType } from "@/shared/enums";
import { recalculateAndUpsertIncentiveRecord } from "../../services/incentive.service";

const connection = new Redis({
	host: process.env.REDIS_HOST as string,
	port: Number(process.env.REDIS_PORT),
	password: process.env.REDIS_PASSWORD as string,
});

export const incentiveWorker = new Worker(
	"incentive-queue",
	async (job: Job) => {
		if (job.name.startsWith("recalculate-incentive-")) {
			const type = job.name.split("recalculate-incentive-")[1] as IncentiveType;

			console.log(`🚀 Running daily Incentive recalculation for ${type}...`);

			const users = await db.selectFrom("users").select(["id"]).execute();

			for (const user of users) {
				await recalculateAndUpsertIncentiveRecord(user.id, type);
			}

			console.log(`✅ Incentive recalculation done for ${type}!`);
		}
	},
	{ connection },
);

incentiveWorker.on("completed", (job) => {
	console.log(`✅ Job ${job.id} (${job.name}) selesai`);
});

incentiveWorker.on("failed", (job, err) => {
	console.error(`❌ Job ${job?.id} gagal:`, err);
});
