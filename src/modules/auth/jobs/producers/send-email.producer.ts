import type { Template<PERSON><PERSON><PERSON><PERSON> } from "@/shared/lib/mail";
import { sendEmailQueue } from "../queues/send-email.queue";

export async function sendEmailProducer({
	from,
	to,
	subject,
	templateName,
	variables,
}: {
	from: string;
	to: string;
	subject: string;
	templateName: TemplateMailKey;
	variables: Record<string, any>;
}) {
	await sendEmailQueue.add(
		"send-email",
		{ from, to, subject, templateName, variables },
		{
			attempts: 5, // coba maksimal 5 kali
			backoff: {
				type: "exponential", // atau "fixed"
				delay: 1000, // delay 1 detik awal
			},
		},
	);
}
