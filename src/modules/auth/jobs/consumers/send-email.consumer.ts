import { Worker } from "bullmq";
import { QUEUE_LIST } from "@/shared/constants";
import { getMailTemplate, sendEmail, type Template<PERSON><PERSON><PERSON><PERSON> } from "@/shared/lib/mail";
import { cacheClient } from "@/shared/lib/redis";

new Worker(
	QUEUE_LIST.SEND_EMAIL,
	async (job) => {
		try {
			const { from, to, subject, templateName, variables } = job.data;
			const template = getMailTemplate(templateName as unknown as Template<PERSON>ailKey, variables);

			await sendEmail({
				from,
				to,
				subject,
				html: template,
			});

			console.log(`✅ Email sent to ${to}`);
		} catch (error) {
			console.error(`❌ Failed to send email to ${job.data.to}:`, error);
			throw error;
		}
	},
	{ connection: cacheClient, concurrency: 15 },
);
