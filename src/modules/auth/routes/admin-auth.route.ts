import { Router } from "express";
import { validateBody } from "@/shared/middlewares";
import * as authController from "../controllers/auth.controller";
import {
	forgotPasswordDTO,
	resetPasswordDTO,
	signInCredentialDTO,
} from "../dtos/request";

export const adminAuthRouter = Router();

adminAuthRouter.post(
	"/login",
	validateBody(signInCredentialDTO),
	authController.signInCredentialAdmin,
);
adminAuthRouter.post(
	"/forgot-password",
	validateBody(forgotPasswordDTO),
	authController.forgotPasswordAdmin,
);
adminAuthRouter.post(
	"/reset-password",
	validateBody(resetPasswordDTO),
	authController.resetPasswordAdmin,
);
