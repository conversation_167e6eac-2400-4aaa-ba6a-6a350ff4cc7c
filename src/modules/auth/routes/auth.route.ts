import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { refreshTokenMiddleware, validateBody } from "@/shared/middlewares";
import { multiAuthMiddleware } from "@/shared/middlewares/multi-auth.middleware";
import * as authController from "../controllers/auth.controller";
import {
	forgotPasswordDTO,
	resetPasswordDTO,
	signInCredentialDTO,
} from "../dtos/request";

export const authRouter = Router();

authRouter.get(
	"/me",
	multiAuthMiddleware([AuthStrategy.JWT_USER, AuthStrategy.JWT_ADMIN]),
	authController.getMe,
);
authRouter.post(
	"/login",
	validateBody(signInCredentialDTO),
	authController.signInCredential,
);
authRouter.post(
	"/refresh-token",
	refreshTokenMiddleware,
	authController.refreshToken,
);
authRouter.post(
	"/forgot-password",
	validateBody(forgotPasswordDTO),
	authController.forgotPassword,
);
authRouter.post(
	"/reset-password",
	validateBody(resetPasswordDTO),
	authController.resetPassword,
);
authRouter.post(
	"/logout",
	multiAuthMiddleware([AuthStrategy.JWT_USER, AuthStrategy.JWT_ADMIN]),
	authController.logout,
);
