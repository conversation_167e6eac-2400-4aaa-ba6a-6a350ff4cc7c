import type { NextFunction, Request, Response } from "express";
import { Env } from "@/shared/config/env.config";
import { UnauthorizedError } from "@/shared/exceptions";
import { PATHS } from "@/shared/lib/paths";
import type { AuthTokenPayload } from "@/shared/types";
import type {
	ForgotPasswordDTO,
	ResetPasswordDTO,
	SignInCredentialDTO,
} from "../dtos/request";
import * as authService from "../services/auth.service";

export const signInCredential = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as SignInCredentialDTO;
		const ipAddress =
			(req.headers["x-forwarded-for"]?.toString().split(",")[0] as string) ||
			(req.socket.remoteAddress as string);
		const userAgent =
			req.useragent?.source || req.headers["user-agent"] || "unknown";

		const result = await authService.signInCredential(
			validatedBody,
			userAgent,
			ipAddress,
		);

		const clientType = req.headers["x-client-type"];
		const isWebClient = clientType === "web";

		if (isWebClient) {
			res.cookie("accessToken", result.accessToken, {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
				maxAge: Env.COOKIE_ACCESS_TOKEN_EXPIRATION,
			});

			res.cookie("refreshToken", result.refreshToken, {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
				maxAge: Env.COOKIE_REFRESH_TOKEN_EXPIRATION,
			});
		}

		const response = {
			status: "success",
			message: "Login berhasil",
			data: {
				user: result.user,
				isFaceSampleRegistered: result.isFaceSampleRegistered,
				...(isWebClient
					? {}
					: {
							accessToken: result.accessToken,
							refreshToken: result.refreshToken,
						}),
			},
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
export const signInCredentialAdmin = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as SignInCredentialDTO;
		const ipAddress =
			(req.headers["x-forwarded-for"]?.toString().split(",")[0] as string) ||
			(req.socket.remoteAddress as string);
		const userAgent =
			req.useragent?.source || req.headers["user-agent"] || "unknown";

		const result = await authService.signInCredentialAdmin(
			validatedBody,
			userAgent,
			ipAddress,
		);

		const clientType = req.headers["x-client-type"];
		const isWebClient = clientType === "web";

		if (isWebClient) {
			res.cookie("accessToken", result.accessToken, {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
				maxAge: Env.COOKIE_ACCESS_TOKEN_EXPIRATION,
			});

			res.cookie("refreshToken", result.refreshToken, {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
				maxAge: Env.COOKIE_REFRESH_TOKEN_EXPIRATION,
			});
		}

		const response = {
			status: "success",
			message: "Login berhasil",
			data: {
				user: result.user,
				isFaceSampleRegistered: result.isFaceSampleRegistered,
				...(isWebClient
					? {}
					: {
							accessToken: result.accessToken,
							refreshToken: result.refreshToken,
						}),
			},
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMe = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const user = req.user;

		if (!user) {
			throw new UnauthorizedError(
				"Sesi login anda tidak ditemukan, tolong login terlebih dahulu",
			);
		}

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				...user,
				image: `${Env.SERVER_URL}${PATHS.toUrl(user.image)}`,
			},
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const refreshToken = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await authService.refreshToken(
			req.tokenPayload as AuthTokenPayload,
		);

		const response = {
			status: "success",
			message: "Refresh token berhasil",
			data: { ...result },
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const forgotPassword = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as ForgotPasswordDTO;

		await authService.forgotPassword(validatedBody);

		const response = {
			status: "success",
			message: "Email telah dikirimkan",
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const forgotPasswordAdmin = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as ForgotPasswordDTO;

		await authService.forgotPasswordAdmin(validatedBody);

		const response = {
			status: "success",
			message: "Email telah dikirimkan",
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const resetPassword = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as ResetPasswordDTO;

		await authService.resetPassword(validatedBody);

		const response = {
			status: "success",
			message: "Password telah berhasil diubah",
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const resetPasswordAdmin = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as ResetPasswordDTO;

		await authService.resetPasswordAdmin(validatedBody);

		const response = {
			status: "success",
			message: "Password telah berhasil diubah",
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const logout = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const tokenPayload = req.tokenPayload as AuthTokenPayload;
		await authService.logout(tokenPayload);

		// Periksa header x-client-type
		const clientType = req.headers["x-client-type"];
		const isWebClient = clientType === "web";

		if (isWebClient) {
			res.clearCookie("accessToken", {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
			});

			res.clearCookie("refreshToken", {
				httpOnly: true,
				sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
				secure: process.env.NODE_ENV === "production",
			});
		}

		const response = {
			status: "success",
			message: "Berhasil logout",
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
