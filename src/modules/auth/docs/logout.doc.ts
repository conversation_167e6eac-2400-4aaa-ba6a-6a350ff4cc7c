import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";

// POST /api/v1/auth/logout - Logout
registry.registerPath({
	summary: "Logout",
	method: "post",
	path: "/api/v1/auth/logout",
	tags: ["auth"],
	description: "Logout endpoint both for user and admin",
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
