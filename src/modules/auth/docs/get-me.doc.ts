import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { getMeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Me",
	method: "get",
	path: "/api/v1/auth/me",
	tags: ["auth"],
	description: "Get current logged-in user",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: getMeResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
