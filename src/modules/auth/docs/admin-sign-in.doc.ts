import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { signInCredentialDTO } from "../dtos/request";
import { signInCredentialResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "SignIn [Admin]",
	method: "post",
	path: "/api/v1/admin/auth/login",
	tags: ["auth"],
	description: "Login with email",
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: signInCredentialDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: signInCredentialResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
