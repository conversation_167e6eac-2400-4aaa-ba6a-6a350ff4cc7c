import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { refreshTokenResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Refresh Token",
	method: "post",
	path: "/api/v1/auth/refresh-token",
	tags: ["auth"],
	description: "Refresh access token using token refresh",
	security: [
		{
			bearerAuth: [],
		},
	],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: refreshTokenResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
