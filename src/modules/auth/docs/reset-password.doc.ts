import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { resetPasswordDTO } from "../dtos/request";

// POST /api/v1/auth/reset-password - Reset password with token
registry.registerPath({
	summary: "Reset Password",
	method: "post",
	path: "/api/v1/auth/reset-password",
	tags: ["auth"],
	description: "Reset password using token received via email",
	request: {
		body: {
			description: "Reset password data",
			content: {
				"application/json": {
					schema: resetPasswordDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
