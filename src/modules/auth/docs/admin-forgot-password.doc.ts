import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { forgotPasswordDTO } from "../dtos/request";

registry.registerPath({
	summary: "Forgot Password [Admin]",
	method: "post",
	path: "/api/v1/admin/auth/forgot-password",
	tags: ["auth"],
	description: "Request password reset via email",
	request: {
		body: {
			description: "Email for password reset",
			content: {
				"application/json": {
					schema: forgotPasswordDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		404: errorResponses.NotFound,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
