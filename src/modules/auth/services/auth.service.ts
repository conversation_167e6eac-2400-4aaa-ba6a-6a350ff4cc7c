import bcrypt from "bcryptjs";
import dayjs from "dayjs";
import type { StringValue } from "ms";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AdministratorUsersId } from "@/database/types/public/AdministratorUsers";
import type { SessionsId } from "@/database/types/public/Sessions";
import type { UsersId } from "@/database/types/public/Users";
import type { VerificationsId } from "@/database/types/public/Verifications";
import { Env } from "@/shared/config/env.config";
import { TEMPLATE_MAIL_KEY } from "@/shared/constants";
import { BadRequestError, UnauthorizedError } from "@/shared/exceptions";
import { generateToken as generateJwtToken } from "@/shared/lib/jwt";
import { PATHS } from "@/shared/lib/paths";
import type {
	AuthTokenPayload,
	ForgotPasswordTokenPayload,
	OwnerType,
} from "@/shared/types";
import type {
	ForgotPasswordDTO,
	ResetPasswordDTO,
	SignInCredentialDTO,
} from "../dtos/request";
import type {
	RefreshTokenResponseDTO,
	SignInResponseDTO,
} from "../dtos/response";
import { sendEmailProducer } from "../jobs/producers/send-email.producer";

export const generateAccessToken = (
	ownerId: string,
	ownerType: OwnerType,
	sessionId: string,
) => {
	return generateJwtToken<AuthTokenPayload>(
		{ id: ownerId, ownerType, sessionId },
		{
			secret: Env.ACCESS_TOKEN_SECRET,
			expiresIn: Env.ACCESS_TOKEN_EXPIRATION as StringValue,
		},
	);
};

export const generateRefreshToken = (
	ownerId: string,
	ownerType: OwnerType,
	sessionId: string,
) => {
	return generateJwtToken<AuthTokenPayload>(
		{ id: ownerId, ownerType, sessionId },
		{
			secret: Env.REFRESH_TOKEN_SECRET,
			expiresIn: Env.REFRESH_TOKEN_EXPIRATION as StringValue,
		},
	);
};

export const generateResetPasswordToken = (userId: string) => {
	return generateJwtToken<ForgotPasswordTokenPayload>(
		{ userId },
		{
			secret: Env.FORGOT_PASSWORD_TOKEN_SECRET,
			expiresIn: Env.FORGOT_PASSWORD_TOKEN_EXPIRATION as StringValue,
		},
	);
};

export const signInCredential = async (
	payload: SignInCredentialDTO,
	userAgent: string,
	ipAddress: string,
): Promise<SignInResponseDTO> => {
	const existUser = await db
		.selectFrom("users")
		.where((eb) =>
			eb.or([
				eb("email", "=", payload.credential.toLowerCase()),
				eb("nik", "=", payload.credential),
				eb("mobileNumber", "=", payload.credential),
			]),
		)
		.innerJoin("accounts", "accounts.userId", "users.id")
		.select([
			"users.id",
			"users.name",
			"users.email",
			"users.image",
			"accounts.password",
		])
		.orderBy("accountId", "asc")
		.executeTakeFirst();

	if (!existUser) {
		throw new UnauthorizedError(
			"Kredensial tidak valid. Silakan periksa credential dan kata sandi Anda.",
		);
	}

	if (!existUser.password) {
		throw new UnauthorizedError(
			"Kredensial tidak valid. Silakan periksa credential dan kata sandi Anda.",
		);
	}

	const passwordMatch = await bcrypt.compare(
		payload.password,
		existUser.password,
	);

	if (!passwordMatch) {
		throw new UnauthorizedError(
			"Kredensial tidak valid. Silakan periksa credential dan kata sandi Anda.",
		);
	}

	const newSessionId = nanoid();
	const accessToken = generateAccessToken(existUser.id, "user", newSessionId);
	const refreshToken = generateRefreshToken(existUser.id, "user", newSessionId);

	const expiresAt = new Date();
	expiresAt.setDate(expiresAt.getDate() + 30);

	await db.transaction().execute(async (trx) => {
		await trx
			.deleteFrom("sessions")
			.where("ownerId", "=", existUser.id)
			.where("ownerType", "=", "user")
			.where("ipAddress", "=", ipAddress)
			.where("userAgent", "=", userAgent)
			.execute();

		await trx
			.insertInto("sessions")
			.values({
				id: newSessionId as SessionsId,
				ownerId: existUser.id,
				ownerType: "user",
				expiresAt: expiresAt.toISOString(),
				token: refreshToken,
				ipAddress,
				userAgent,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();
	});

	const faceSamples = await db
		.selectFrom("faceVectors")
		.select(["id"])
		.where("userId", "=", existUser.id)
		.execute();
	const roles = await db
		.selectFrom("usersRoles")
		.innerJoin("roles", "roles.id", "usersRoles.roleId")
		.select(["roles.id", "roles.name"])
		.where("usersRoles.userId", "=", existUser.id)
		.execute();

	return {
		accessToken,
		refreshToken,
		user: {
			id: existUser.id,
			name: existUser.name,
			email: existUser.email,
			image: `${Env.SERVER_URL}${PATHS.toUrl(existUser.image)}`,
			roles: roles.map((role) => role.name),
		},
		isFaceSampleRegistered: faceSamples.length >= 3,
	};
};

export const signInCredentialAdmin = async (
	payload: SignInCredentialDTO,
	userAgent: string,
	ipAddress: string,
): Promise<SignInResponseDTO> => {
	const existAdmin = await db
		.selectFrom("administratorUsers")
		.select([
			"administratorUsers.id",
			"administratorUsers.name",
			"administratorUsers.email",
			"administratorUsers.image",
			"administratorUsers.password",
		])
		.where("administratorUsers.email", "=", payload.credential)
		.executeTakeFirst();

	if (!existAdmin) {
		throw new UnauthorizedError(
			"Kredensial tidak valid. Silakan periksa email dan kata sandi Anda.",
		);
	}

	const passwordMatch = await bcrypt.compare(
		payload.password,
		existAdmin.password,
	);

	if (!passwordMatch) {
		throw new UnauthorizedError(
			"Kredensial tidak valid. Silakan periksa email dan kata sandi Anda.",
		);
	}

	const newSessionId = nanoid();
	const accessToken = generateAccessToken(existAdmin.id, "admin", newSessionId);
	const refreshToken = generateRefreshToken(
		existAdmin.id,
		"admin",
		newSessionId,
	);

	const expiresAt = new Date();
	expiresAt.setDate(expiresAt.getDate() + 30);

	await db.transaction().execute(async (trx) => {
		await trx
			.deleteFrom("sessions")
			.where("ownerId", "=", existAdmin.id)
			.where("ownerType", "=", "admin")
			.where("ipAddress", "=", ipAddress)
			.where("userAgent", "=", userAgent)
			.execute();

		await trx
			.insertInto("sessions")
			.values({
				id: newSessionId as SessionsId,
				ownerId: existAdmin.id,
				ownerType: "admin",
				expiresAt: expiresAt.toISOString(),
				token: refreshToken,
				ipAddress,
				userAgent,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();
	});

	return {
		accessToken,
		refreshToken,
		user: {
			id: existAdmin.id,
			name: existAdmin.name,
			email: existAdmin.email,
			image: `${Env.SERVER_URL}${PATHS.toUrl(existAdmin.image)}`,
			roles: [],
		},
		isFaceSampleRegistered: true,
	};
};

export const refreshToken = async (
	payload: AuthTokenPayload,
): Promise<RefreshTokenResponseDTO> => {
	const existSession = await db
		.selectFrom("sessions")
		.select(["id", "expiresAt", "ownerId", "ownerType"])
		.where("id", "=", payload.sessionId as SessionsId)
		.executeTakeFirst();

	if (!existSession) {
		throw new UnauthorizedError(
			"Sesi anda telah berakhir. Silakan login lagi.",
		);
	}

	const now = dayjs();
	const expires = dayjs(existSession.expiresAt);

	if (now.isAfter(expires)) {
		throw new UnauthorizedError(
			"Sesi anda telah berakhir. Silakan login lagi.",
		);
	}

	const expiresAt = new Date();
	expiresAt.setDate(expiresAt.getDate() + 30);
	const newAccessToken = generateAccessToken(
		existSession.ownerId,
		existSession.ownerType as OwnerType,
		existSession.id,
	);
	const newRefreshToken = generateRefreshToken(
		existSession.ownerId,
		existSession.ownerType as OwnerType,
		existSession.id,
	);

	await db
		.updateTable("sessions")
		.set({
			expiresAt: expiresAt.toISOString(),
			token: newRefreshToken,
			updatedAt: new Date().toISOString(),
		})
		.where("id", "=", existSession.id)
		.executeTakeFirstOrThrow();

	return {
		accessToken: newAccessToken,
		refreshToken: newRefreshToken,
	};
};

export const forgotPassword = async (
	payload: ForgotPasswordDTO,
): Promise<void> => {
	const existUser = await db
		.selectFrom("users")
		.select(["id", "name"])
		.where("email", "=", payload.email)
		.executeTakeFirst();

	if (!existUser) {
		throw new BadRequestError("Akun tidak ditemukan");
	}

	const token = nanoid();
	const expiresAt = new Date();
	expiresAt.setHours(expiresAt.getHours() + 1);

	await db
		.insertInto("verifications")
		.values({
			id: nanoid() as VerificationsId,
			identifier: existUser.id,
			value: `password_reset:${token}`,
			expiresAt: new Date(expiresAt).toISOString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.executeTakeFirstOrThrow();

	await sendEmailProducer({
		from: Env.COMPANY_MAIL,
		to: payload.email,
		subject: `Password Reset for Account ${existUser.name}`,
		templateName: TEMPLATE_MAIL_KEY.RESET_PASSWORD,
		variables: {
			link: `${Env.FRONTEND_URL}/forgot-password?token=${token}`,
		},
	});
};

export const forgotPasswordAdmin = async (
	payload: ForgotPasswordDTO,
): Promise<void> => {
	const existAdmin = await db
		.selectFrom("administratorUsers")
		.select(["id", "name"])
		.where("email", "=", payload.email)
		.executeTakeFirst();

	if (!existAdmin) {
		throw new BadRequestError("Akun tidak ditemukan");
	}

	const token = nanoid();
	const expiresAt = new Date();
	expiresAt.setHours(expiresAt.getHours() + 1);

	await db
		.insertInto("verifications")
		.values({
			id: nanoid() as VerificationsId,
			identifier: existAdmin.id,
			value: `password_reset:${token}`,
			expiresAt: new Date(expiresAt).toISOString(),
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.executeTakeFirstOrThrow();

	await sendEmailProducer({
		from: Env.COMPANY_MAIL,
		to: payload.email,
		subject: `Password Reset for Admin Account: ${existAdmin.name}`,
		templateName: TEMPLATE_MAIL_KEY.RESET_PASSWORD,
		variables: {
			link: `${Env.FRONTEND_URL}/forgot-password?token=${token}`,
		},
	});
};

export const resetPassword = async (
	payload: ResetPasswordDTO,
): Promise<void> => {
	const verification = await db
		.selectFrom("verifications")
		.select(["id", "identifier", "value", "expiresAt"])
		.where("value", "=", `password_reset:${payload.token}`)
		.executeTakeFirst();

	if (!verification || new Date(verification.expiresAt) < new Date()) {
		throw new BadRequestError("Token tidak valid atau sudah kadaluarsa");
	}

	const existUser = await db
		.selectFrom("users")
		.select(["id"])
		.where("id", "=", verification.identifier as UsersId)
		.executeTakeFirst();

	if (!existUser) {
		throw new BadRequestError("Akun tidak ditemukan");
	}

	await db.transaction().execute(async (trx) => {
		await trx
			.updateTable("accounts")
			.set({
				password: bcrypt.hashSync(payload.newPassword),
				updatedAt: new Date().toISOString(),
			})
			.where("userId", "=", existUser.id)
			.where("providerId", "=", "credential")
			.executeTakeFirstOrThrow();

		await trx
			.deleteFrom("verifications")
			.where("id", "=", verification.id as VerificationsId)
			.executeTakeFirstOrThrow();
	});
};

export const resetPasswordAdmin = async (
	payload: ResetPasswordDTO,
): Promise<void> => {
	const verification = await db
		.selectFrom("verifications")
		.select(["id", "identifier", "value", "expiresAt"])
		.where("value", "=", `password_reset:${payload.token}`)
		.executeTakeFirst();

	if (!verification || new Date(verification.expiresAt) < new Date()) {
		throw new BadRequestError("Token tidak valid atau sudah kadaluarsa");
	}

	const existAdmin = await db
		.selectFrom("administratorUsers")
		.select(["id"])
		.where("id", "=", verification.identifier as AdministratorUsersId)
		.executeTakeFirst();

	if (!existAdmin) {
		throw new BadRequestError("Akun tidak ditemukan");
	}

	await db.transaction().execute(async (trx) => {
		await trx
			.updateTable("administratorUsers")
			.set({
				password: bcrypt.hashSync(payload.newPassword),
				updatedAt: new Date().toISOString(),
			})
			.where("id", "=", existAdmin.id)
			.executeTakeFirstOrThrow();

		await trx
			.deleteFrom("verifications")
			.where("id", "=", verification.id as VerificationsId)
			.executeTakeFirstOrThrow();
	});
};

export const logout = async (payload: AuthTokenPayload): Promise<void> => {
	await db
		.deleteFrom("sessions")
		.where("id", "=", payload.sessionId as SessionsId)
		.executeTakeFirstOrThrow();
};
