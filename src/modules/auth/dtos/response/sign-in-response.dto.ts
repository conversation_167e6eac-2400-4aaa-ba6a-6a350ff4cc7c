import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const signInCredentialResponseDTO = z.object({
	accessToken: z.string().optional().meta({
		description: "JWT access token",
		example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
	}),
	refreshToken: z.string().optional().meta({
		description: "JWT refresh token",
		example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
	}),
	user: z
		.object({
			id: z.string().meta({
				description: "User ID",
				example: "Qah4cccEK0MsspUJ9ywAy",
			}),
			name: z.string().meta({
				description: "User full name",
				example: "<PERSON>",
			}),
			email: z.email().meta({
				description: "User email address",
				example: "<EMAIL>",
			}),
			image: z.string().meta({
				description: "User profile image URL",
				example: "https://example.com/avatar.jpg",
			}),
			roles: z.array(z.string()).meta({
				example: ["Director"],
				description: "Array of user role",
			}),
		})
		.meta({
			description: "User information",
		}),
	isFaceSampleRegistered: z.boolean().meta({
		description:
			"Whether user has registered face samples for biometric authentication",
		example: true,
	}),
});
export type SignInResponseDTO = z.infer<typeof signInCredentialResponseDTO>;
