import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const refreshTokenResponseDTO = z.object({
	accessToken: z.string().nonempty().meta({
		description: "New JWT access token",
		example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
	}),
	refreshToken: z.string().nonempty().meta({
		description: "New JWT refresh token",
		example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
	}),
});
export type RefreshTokenResponseDTO = z.infer<typeof refreshTokenResponseDTO>;
