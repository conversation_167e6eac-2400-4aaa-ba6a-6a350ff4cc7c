import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const accountDTO = z
	.object({
		id: z.string().meta({
			description: "Unique identifier for the account",
			example: "wvYpwZVh5trLkQOKNknQV",
		}),
		userId: z.string().meta({
			description: "Unique identifier for the user",
			example: "uiKhnH8WNQNZqKHcHXqUb",
		}),
		accountId: z.string().meta({
			description: "Identifier for the account provider",
			example: "google-account-id",
		}),
		providerId: z.string().meta({
			description: "Identifier for the account provider",
			example: "some-provider-id",
		}),
		accessToken: z.string().nullable().meta({
			description: "Access token for the account",
			example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
		}),
		refreshToken: z.string().nullable().meta({
			description: "Refresh token for the account",
			example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
		}),
		accessTokenExpiresAt: z.iso.datetime().nullable().meta({
			description: "Expiration date for the access token",
			example: "2023-03-01T00:00:00.000Z",
		}),
		refreshTokenExpiresAt: z.iso.datetime().nullable().meta({
			description: "Expiration date for the refresh token",
			example: "2023-03-01T00:00:00.000Z",
		}),
		scope: z.string().nullable().meta({
			description: "Scope for the access token",
			example: "openid profile email",
		}),
		idToken: z.string().nullable().meta({
			description: "ID token for the access token",
			example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
		}),
		password: z.string().nullable().meta({
			description: "Hashed password for the user",
			example: "hash-of-password",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Account");
