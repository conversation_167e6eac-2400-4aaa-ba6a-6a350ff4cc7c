import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const session = z
	.object({
		id: z.string().meta({
			example: "faqxmAFi2g5IaGtivRSys",
			description: "Unique identifier for the session",
		}),
		ownerId: z.string().meta({
			example: "vQFWg0viuK2PJEP0Vw3V8",
			description: "ID of the user or admin user who owns this session",
		}),
		ownerType: z.string().meta({
			example: "user",
			description: "Type of the owner (user or admin_user)",
		}),
		token: z.string().meta({
			example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
			description: "JWT refresh token or any token for this session",
		}),
		expiresAt: z.iso.datetime().meta({
			example: "2025-01-15T08:30:15.000Z",
			description: "Expiration timestamp for session",
		}),
		ipAddress: z.string().nullable().meta({
			example: "***********",
			description: "Ip address of the client",
		}),
		userAgent: z.string().nullable().meta({
			example:
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			description: "User agent of the client",
		}),
		impersonatedBy: z.string().nullable().meta({
			example: null,
			description: "ID of the user or admin user who impersonated this session",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Session");
