import * as z from "zod";

export const resetPasswordDTO = z
	.object({
		token: z.string().nonempty().meta({
			description: "Reset password token received via email",
			example: "abc123def456",
		}),
		newPassword: z.string().min(6).meta({
			description: "New password (minimum 6 characters)",
			example: "newPassword123",
		}),
		newPasswordConfirmation: z.string().meta({
			description: "Confirm new password",
			example: "newPassword123",
		}),
	})
	.refine((data) => data.newPassword === data.newPasswordConfirmation, {
		message: "Konfirmasi password baru tidak cocok",
		path: ["newPasswordConfirmation"],
	});
export type ResetPasswordDTO = z.infer<typeof resetPasswordDTO>;
