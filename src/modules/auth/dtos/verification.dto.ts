import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const verification = z
	.object({
		id: z.string().meta({
			description: "Unique identifier for the verification",
			example: "v_xEHrmF2czmf7joyscp5J2",
		}),
		identifier: z.string().meta({
			description:
				"Identifier (e.g., userId, email, etc.) for the verification",
		}),
		value: z.string().meta({
			description: "Verification value (e.g., token, code, etc.)",
			example: "password_reset:token",
		}),
		expiresAt: z.iso.datetime().meta({
			description: "Expiration date for the verification",
			example: "2025-01-15T08:30:15.000Z",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Verification");
