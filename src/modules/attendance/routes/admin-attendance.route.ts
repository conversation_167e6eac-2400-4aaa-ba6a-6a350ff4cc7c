import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as attendanceAdminController from "../controllers/admin-attendance.controller";
import { attendanceLogQueryDTO } from "../dtos";
import {
	adminCreateAttendanceLogDTO,
	adminUpdateAttendanceLogDTO,
} from "../dtos/request";

export const adminAttendanceRouter = Router();

adminAttendanceRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(attendanceLogQueryDTO),
	attendanceAdminController.getAllAttendanceLog,
);

adminAttendanceRouter.get(
	"/:attendanceLogId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	attendanceAdminController.getAttendanceLog,
);

adminAttendanceRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	localUploadMiddleware.single("photo", {
		allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users`,
		userFolder: true,
		required: true,
		fileNamePrefix: "attendance",
		maxFileSize: 5 * 1024 * 1024, // 5MB
	}),
	preprocessors.addFilePaths([
		{ field: "photo", bodyKey: "photo", pathProcessor: PATHS.toDatabasePath },
	]),
	validateBody(adminCreateAttendanceLogDTO),
	attendanceAdminController.createAttendanceLog,
);

adminAttendanceRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	attendanceAdminController.bulkAction,
);

adminAttendanceRouter.put(
	"/:attendanceLogId",
	localUploadMiddleware.single("photo", {
		allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users`,
		userFolder: true,
		required: false,
		fileNamePrefix: "attendance",
		maxFileSize: 5 * 1024 * 1024, // 5MB
	}),
	preprocessors.addFilePaths([
		{ field: "photo", bodyKey: "photo", pathProcessor: PATHS.toDatabasePath },
	]),
	validateBody(adminUpdateAttendanceLogDTO),
	attendanceAdminController.updateAttendanceLog,
);
