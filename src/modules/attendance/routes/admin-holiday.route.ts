import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import { authMiddleware, validateBody, validateQuery } from "@/shared/middlewares";
import * as adminHolidayController from "../controllers/admin-holiday.controller";
import { adminCreateHolidayDTO, adminUpdateHolidayDTO } from "../dtos/request";
import { holidayQueryDTO } from "../dtos";

export const adminHolidayRouter = Router();

adminHolidayRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
  validateQuery(holidayQueryDTO),
	adminHolidayController.getAllHoliday,
);

adminHolidayRouter.get(
	"/:holidayId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminHolidayController.getHoliday,
);

adminHolidayRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateHolidayDTO),
	adminHolidayController.createHoliday,
);

adminHolidayRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminHolidayController.bulkAction,
);

adminHolidayRouter.put(
	"/:holidayId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateHolidayDTO),
	adminHolidayController.updateHoliday,
);

adminHolidayRouter.delete(
	"/:holidayId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminHolidayController.deleteHoliday,
);
