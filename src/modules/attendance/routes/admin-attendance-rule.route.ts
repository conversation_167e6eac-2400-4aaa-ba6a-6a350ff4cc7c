import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { authMiddleware, validateBody } from "@/shared/middlewares";
import * as adminAttendanceRuleController from "../controllers/admin-attendance-rule.controller";
import { adminUpdateAttendanceRuleDTO } from "../dtos/request";

export const adminAttendanceRuleRouter = Router();

adminAttendanceRuleRouter.put(
	"/:attendanceRuleId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateAttendanceRuleDTO),
	adminAttendanceRuleController.updateAttendanceRule,
);
