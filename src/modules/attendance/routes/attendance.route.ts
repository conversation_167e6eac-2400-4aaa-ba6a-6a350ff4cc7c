import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
} from "@/shared/middlewares";
import * as attendanceController from "../controllers/attendance.controller";
import { createAttendanceLogDTO, registerFaceSampleDTO } from "../dtos/request";

export const attendanceRouter = Router();

attendanceRouter.get(
	"/configurations",
	authMiddleware(AuthStrategy.JWT_USER),
	attendanceController.getAttendanceConfiguration,
);

attendanceRouter.get(
	"/history",
	authMiddleware(AuthStrategy.JWT_USER),
	attendanceController.getMyAttendanceHistory,
);

attendanceRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.single("face", {
		allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
		folder: `${PATHS.PRIVATE.UPLOADS}/users`,
		userFolder: true,
		required: true,
		fileNamePrefix: "attendance",
		maxFileSize: 5 * 1024 * 1024, // 5MB
	}),
	preprocessors.addFilePaths([
		{ field: "face", bodyKey: "face", pathProcessor: PATHS.toDatabasePath },
	]),
	validateBody(createAttendanceLogDTO),
	attendanceController.recordAttendance,
);

attendanceRouter.post(
	"/register-face-sample",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.multiple([
		{
			name: "face1",
			maxCount: 1,
			required: true, // Add required flag for better error handling
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/users`,
			userFolder: true,
			fileNamePrefix: "face-sample",
			maxFileSize: 5 * 1024 * 1024, // 5MB
		},
		{
			name: "face2",
			maxCount: 1,
			required: true,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/users`,
			userFolder: true,
			fileNamePrefix: "face-sample",
			maxFileSize: 5 * 1024 * 1024, // 5MB
		},
		{
			name: "face3",
			maxCount: 1,
			required: true,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/users`,
			userFolder: true,
			fileNamePrefix: "face-sample",
			maxFileSize: 5 * 1024 * 1024, // 5MB
		},
	]),
	preprocessors.addFilePaths([
		{ field: "face1", bodyKey: "face1", pathProcessor: PATHS.toDatabasePath },
		{ field: "face2", bodyKey: "face2", pathProcessor: PATHS.toDatabasePath },
		{ field: "face3", bodyKey: "face3", pathProcessor: PATHS.toDatabasePath },
	]),
	validateBody(registerFaceSampleDTO),
	attendanceController.registerFaceSample,
);
