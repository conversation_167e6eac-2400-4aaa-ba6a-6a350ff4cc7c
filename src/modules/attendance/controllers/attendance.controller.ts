import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import type {
	CreateAttendanceLogDTO,
	RegisterFaceSampleDTO,
} from "../dtos/request";
import * as attendanceService from "../services/attendance.service";

export const registerFaceSample = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as RegisterFaceSampleDTO;

		const result = await attendanceService.registerFaceSample(
			validatedBody,
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mendaftarkan sample wajah.",
			data: result,
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const recordAttendance = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as CreateAttendanceLogDTO;
		const userAgent =
			req.useragent?.source || req.headers["user-agent"] || "unknown";

		const result = await attendanceService.recordAttendance(
			validatedBody,
			req.user as AuthUser,
			userAgent,
		);

		const response = {
			status: "success",
			message: "Berhasil melakukan presensi kehadiran.",
			data: { ...result },
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMyAttendanceHistory = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await attendanceService.getMyAttendanceHistory({
			user: req.user as AuthUser,
			startDate: req.query?.startDate as string,
			endDate: req.query?.endDate as string,
		});

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				histories: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAttendanceConfiguration = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await attendanceService.getAttendanceConfiguration(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
