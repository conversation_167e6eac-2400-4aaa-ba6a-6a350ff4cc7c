import type { NextFunction, Request, Response } from "express";
import * as attendanceDebugService from "../services/attendance.debug.service";

export const getAttendanceAttempts = async (
    req: Request,
    res: Response,
    next: NextFunction,
) => {
    try {
        const result = await attendanceDebugService.getAttendanceAttempts();

        const response = {
            status: "success",
            message: "Attendance attempts retrieved successfully",
            data: result,
        };

        res.status(200).json(response);
    } catch (error) {
        next(error);
    }
};