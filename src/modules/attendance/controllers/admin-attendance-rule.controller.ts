import type { NextFunction, Request, Response } from "express";
import type { AdminUpdateAttendanceRuleDTO } from "../dtos/request";
import * as adminAttendanceRuleService from "../services/admin-attendance-rule.service";

export const updateAttendanceRule = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const attendanceRuleId = req.params.attendanceRuleId as string;
		const validatedBody = req.validatedBody as AdminUpdateAttendanceRuleDTO;
		const result = await adminAttendanceRuleService.updateAttendanceRule(
			attendanceRuleId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: { ...result },
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
