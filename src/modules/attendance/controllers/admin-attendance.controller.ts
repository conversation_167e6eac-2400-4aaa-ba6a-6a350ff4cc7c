import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { AttendanceLogQueryDTO } from "../dtos";
import type {
	AdminCreateAttendanceLogDTO,
	AdminUpdateAttendanceLogDTO,
} from "../dtos/request";
import * as adminAttendanceService from "../services/admin-attendance.service";

export const getAllAttendanceLog = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as AttendanceLogQueryDTO;
		const result =
			await adminAttendanceService.getAllAttendanceLog(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result.data,
			meta: result.meta,
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAttendanceLog = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const attendanceLogId = req.params.attendanceLogId as string;
		const result =
			await adminAttendanceService.getAttendanceLog(attendanceLogId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createAttendanceLog = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateAttendanceLogDTO;
	const userAgent	 =
			req.useragent?.source || req.headers["user-agent"] || "unknown";

		const result = await adminAttendanceService.createAttendanceLog(
			validatedBody,
			userAgent,
		);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru.",
			data: result,
		};

		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateAttendanceLog = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const attendanceLogId = req.params.attendanceLogId as string;
		const validatedBody = req.validatedBody as AdminUpdateAttendanceLogDTO;

		const result = await adminAttendanceService.updateAttendanceLog(
			attendanceLogId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data.",
			data: result,
		};

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminAttendanceService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=attendanceLogs.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
