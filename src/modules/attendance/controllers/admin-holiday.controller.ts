import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { HolidayQueryDTO } from "../dtos";
import type {
	AdminCreateHolidayDTO,
	AdminUpdateHolidayDTO,
} from "../dtos/request";
import * as adminHolidayService from "../services/admin-holiday.service";

export const getAllHoliday = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as HolidayQueryDTO;
		const result = await adminHolidayService.getAllHoliday(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getHoliday = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const holidayId = req.params.holidayId as string;
		const result = await adminHolidayService.getHoliday(holidayId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createHoliday = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateHolidayDTO;
		const result = await adminHolidayService.createHoliday(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateHoliday = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const holidayId = req.params.holidayId as string;
		const validatedBody = req.validatedBody as AdminUpdateHolidayDTO;
		const result = await adminHolidayService.updateHoliday(
			holidayId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const deleteHoliday = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const holidayId = req.params.holidayId as string;
		await adminHolidayService.deleteHoliday(holidayId);

		const response = {
			status: "success",
			message: "Berhasil menghapus data",
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminHolidayService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=holiday.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
