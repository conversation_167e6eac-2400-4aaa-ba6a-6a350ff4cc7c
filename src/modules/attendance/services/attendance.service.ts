import "dayjs/locale/id.js";

import dayjs, { type Dayjs } from "dayjs";
import timezone from "dayjs/plugin/timezone.js";
import utc from "dayjs/plugin/utc.js";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AttendanceAttemptsId } from "@/database/types/public/AttendanceAttempts";
import type { AttendanceLogsId } from "@/database/types/public/AttendanceLogs";
import type { FaceVectorsId } from "@/database/types/public/FaceVectors";
import type { UsersId } from "@/database/types/public/Users";
import type { WorksitesId } from "@/database/types/public/Worksites";
import { Env } from "@/shared/config/env.config";
import {
	AttendanceStatus,
	AttendanceType,
	OfficeLeaveStatus,
} from "@/shared/enums";
import { BadRequestError } from "@/shared/exceptions";
import {
	compareFaceDescriptorFaceApi,
	extractFaceDescriptor,
} from "@/shared/lib/face-api";
import { PATHS } from "@/shared/lib/paths";
import { CACHE_KEYS, CACHE_TTL, cacheHelpers } from "@/shared/lib/redis";
import type { AuthUser } from "@/shared/types";
import { toDateOnlyString } from "@/shared/utils/date";
import type {
	CreateAttendanceLogDTO,
	RegisterFaceSampleDTO,
} from "../dtos/request";
import type {
	AttendanceConfigurationResponseDTO,
	AttendanceHistoryResponseDTO,
	RecordAttendanceResponseDTO,
	RegisterFaceSampleResponseDTO,
} from "../dtos/response";

type AttendanceUIState =
	| "HOLIDAY" // 🏖️  Hari libur nasional - Tampilkan pesan "Hari Libur Nasional" (tidak ada tombol)
	| "ON_LEAVE" // 🌴  User sedang cuti - Tampilkan pesan "Sedang Cuti" (tidak ada tombol)
	| "NON_WORKING_DAY" // 📅  Hari libur (weekend) - Tampilkan pesan "Hari Libur" (tidak ada tombol)
	| "NOT_YET_CHECKED_IN" // 🔵  Belum check-in - Tampilkan tombol "Check In"
	| "CHECKED_IN" // 🟢  Sudah check-in - Tampilkan tombol "Istirahat", "Izin Keluar", "Check Out"
	| "ON_BREAK" // 🟡  Sedang istirahat/izin - Tampilkan tombol "Kembali"
	| "READY_TO_CHECKOUT" // 🟠  Sudah kembali dari istirahat - Tampilkan tombol "Check Out"
	| "DONE"; // ✅  Sudah check-out - Tampilkan pesan "Absensi Hari Ini Selesai" (tidak ada tombol)

dayjs.extend(utc);
dayjs.extend(timezone);

export const getDistanceMeter = ({
	location1,
	location2,
}: {
	location1: { lat: number; long: number };
	location2: { lat: number; long: number };
}) => {
	const R = 6371000; // radius bumi dalam meter
	const dLat = ((location2.lat - location1.lat) * Math.PI) / 180;
	const dLon = ((location2.long - location1.long) * Math.PI) / 180;
	const lat1Rad = (location1.lat * Math.PI) / 180;
	const lat2Rad = (location2.lat * Math.PI) / 180;
	const a =
		Math.sin(dLat / 2) ** 2 +
		Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(dLon / 2) ** 2;
	return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
};

export const checkIsWorkingDay = async (date: Dayjs): Promise<boolean> => {
	const day = date.format("dddd").toLowerCase();

	const workingDays = ["monday", "tuesday", "wednesday", "thursday", "friday"];

	if (!workingDays.includes(day)) return false;

	const isHoliday = await db
		.selectFrom("holidays")
		.select(["id"])
		.where("date", "=", toDateOnlyString(date))
		.executeTakeFirst();

	return !isHoliday;
};

export const registerFaceSample = async (
	payload: RegisterFaceSampleDTO,
	user: AuthUser,
): Promise<RegisterFaceSampleResponseDTO> => {
	const imagePaths = [payload.face1, payload.face2, payload.face3].filter(
		(path) => path && path.length > 0,
	);
	const vectors = [];

	for (const imagePath of imagePaths) {
		const fullPath = PATHS.toDiskPath(imagePath);
		const descriptor = await extractFaceDescriptor(fullPath);

		vectors.push({
			id: nanoid() as FaceVectorsId,
			userId: user.id as UsersId,
			descriptor,
			imagePath,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		});
	}

	await db.insertInto("faceVectors").values(vectors).execute();

	return {
		isFaceSampleRegistered: true,
	};
};

export const recordAttendance = async (
	payload: CreateAttendanceLogDTO,
	user: AuthUser,
	userAgent: string,
): Promise<RecordAttendanceResponseDTO> => {
	const worksiteCacheKey = CACHE_KEYS.worksite(user.id);
	// TODO: Implementing cache for worksite
	// let worksite = await cacheHelpers.get<Worksites>(worksiteCacheKey);
	let worksite = null;

	if (!worksite) {
		const localWorksite = await db
			.selectFrom("usersWorksites")
			.innerJoin("worksites", "worksites.id", "usersWorksites.worksiteId")
			.select([
				"worksites.id",
				"worksites.name",
				"worksites.type",
				"worksites.address",
				"worksites.description",
				"worksites.createdAt",
				"worksites.updatedAt",
			])
			.where("usersWorksites.userId", "=", user.id as UsersId)
			.executeTakeFirst();

		if (localWorksite) {
			worksite = localWorksite;
			await cacheHelpers.set(
				worksiteCacheKey,
				localWorksite,
				CACHE_TTL.WORK_SITE,
			);
		}
	}

	if (!worksite) {
		throw new BadRequestError(
			"Anda belum di assign ke worksite manapun. Segera hubungi admin.",
		);
	}

	// const attendanceRuleCacheKey = CACHE_KEYS.attendanceRule(worksite.id);
	// TODO: Implementing cache for attendance rule
	// let attendanceRule = await cacheHelpers.get<AttendanceRules>(
	// 	attendanceRuleCacheKey,
	// );
	let attendanceRule = null;

	if (!attendanceRule) {
		const attendanceRuleLocal = await db
			.selectFrom("attendanceRules")
			.selectAll()
			.where("worksiteId", "=", worksite.id as WorksitesId)
			.executeTakeFirst();

		if (!attendanceRuleLocal) {
			throw new BadRequestError("Belum ada aturan absensi di work-site ini.");
		}

		attendanceRule = attendanceRuleLocal;
		// await cacheHelpers.set(
		// 	attendanceRuleCacheKey,
		// 	attendanceRule,
		// 	CACHE_TTL.ATTENDANCE_RULES,
		// );
	}

	const timezone = attendanceRule.timezone || "Asia/Jakarta";

	// Get current time
	const now = dayjs().tz(timezone);
	const today = now.format("YYYY-MM-DD");

	console.log(now, "NOW");
	console.log(today, "TODAY");
	console.log(toDateOnlyString(now), "NOW DATE ONLY");

	// validation queries with caching
	const holidayCacheKey = CACHE_KEYS.holidayCheck(today);
	const leaveCacheKey = CACHE_KEYS.leaveCheck(user.id, today);

	const [_isWorkingDay, isHoliday, isOnLeave] = await Promise.all([
		checkIsWorkingDay(now),
		cacheHelpers.get<boolean>(holidayCacheKey).then(async (cached) => {
			if (cached !== null) return cached;

			const result = await db
				.selectFrom("holidays")
				.select(["id"])
				.where("date", "=", toDateOnlyString(now))
				.executeTakeFirst();

			await cacheHelpers.set(
				holidayCacheKey,
				!!result,
				CACHE_TTL.HOLIDAY_CHECK,
			);
			return !!result;
		}),
		cacheHelpers.get<boolean>(leaveCacheKey).then(async (cached) => {
			if (cached !== null) return cached;

			const result = await db
				.selectFrom("leaveRequests")
				.select(["id"])
				.where("userId", "=", user.id as UsersId)
				.where("status", "=", "APPROVED")
				.where("startDate", "<=", toDateOnlyString(now))
				.where("endDate", ">=", toDateOnlyString(now))
				.executeTakeFirst();

			await cacheHelpers.set(leaveCacheKey, !!result, CACHE_TTL.LEAVE_CHECK);
			return !!result;
		}),
	]);

	if (isHoliday)
		throw new BadRequestError("Hari ini adalah hari libur nasional.");
	// if (!isWorkingDay) throw new BadRequestError("Hari ini bukan hari kerja.");
	if (isOnLeave) throw new BadRequestError("Anda sedang dalam masa cuti.");

	const todayAttendanceLogs = await db
		.selectFrom("attendanceLogs")
		.selectAll()
		.where("userId", "=", user.id as UsersId)
		.where("logDate", "=", toDateOnlyString(now))
		.execute();

	const completedTypes = todayAttendanceLogs.map((log) => log.type);

	// Cegah duplikat berdasarkan type
	if (completedTypes.includes(payload.action)) {
		throw new BadRequestError(
			`Absensi tipe ${payload.action} sudah dicatat hari ini.`,
		);
	}

	// Validasi urutan berdasarkan tipe absensi
	switch (payload.action) {
		case AttendanceType.CHECK_IN:
			// CHECK_IN harus yang pertama, tidak boleh ada absensi lain sebelumnya
			if (completedTypes.length > 0) {
				throw new BadRequestError(
					"Tidak dapat check-in karena anda sudah melakukan check-in hari ini.",
				);
			}
			break;

		case AttendanceType.BREAK:
			// BREAK hanya bisa dilakukan setelah CHECK_IN dan belum CHECK_OUT
			if (!completedTypes.includes(AttendanceType.CHECK_IN)) {
				throw new BadRequestError("Tidak dapat istirahat sebelum check-in.");
			}
			if (completedTypes.includes(AttendanceType.CHECK_OUT)) {
				throw new BadRequestError("Tidak dapat istirahat setelah check-out.");
			}
			break;

		case AttendanceType.RETURN:
			// RETURN hanya bisa dilakukan setelah BREAK dan belum CHECK_OUT
			if (!completedTypes.includes(AttendanceType.BREAK)) {
				throw new BadRequestError(
					"Tidak dapat kembali karena belum melakukan istirahat.",
				);
			}
			if (completedTypes.includes(AttendanceType.CHECK_OUT)) {
				throw new BadRequestError("Tidak dapat kembali setelah check-out.");
			}
			break;

		case AttendanceType.CHECK_OUT:
			// CHECK_OUT harus setelah CHECK_IN
			if (!completedTypes.includes(AttendanceType.CHECK_IN)) {
				throw new BadRequestError("Tidak dapat check-out sebelum check-in.");
			}
			// Jika ada BREAK, harus ada RETURN juga
			if (
				completedTypes.includes(AttendanceType.BREAK) &&
				!completedTypes.includes(AttendanceType.RETURN)
			) {
				throw new BadRequestError(
					"Tidak dapat check-out sebelum kembali dari istirahat.",
				);
			}
			break;

		default:
			throw new BadRequestError("Tipe absensi tidak valid.");
	}

	// Validate location
	const distanceMeter = getDistanceMeter({
		location1: { lat: payload.latitude, long: payload.longitude },
		location2: { lat: attendanceRule.latitude, long: attendanceRule.longitude },
	});

	console.log("Location distance meter: ", distanceMeter);
	console.log("Rule: ", attendanceRule);

	if (distanceMeter > attendanceRule.radiusInMeter) {
		throw new BadRequestError(
			"Lokasi tidak dalam jangkauan radius untuk absensi.",
		);
	}

	// TODO: Implmenting cache
	// Face validation with caching
	// const faceVectorsCacheKey = CACHE_KEYS.faceVectors(user.id);
	// let userVectors = await cacheHelpers.get<FaceVectors[]>(faceVectorsCacheKey);
	let userVectors = null;
	if (!userVectors) {
		userVectors = await db
			.selectFrom("faceVectors")
			.selectAll()
			.where("userId", "=", user.id as UsersId)
			.execute();

		// if (userVectors.length > 0) {
		// 	await cacheHelpers.set(
		// 		faceVectorsCacheKey,
		// 		userVectors,
		// 		CACHE_TTL.FACE_VECTORS,
		// 	);
		// }
	}

	const imagePath = PATHS.toDiskPath(payload.face);
	const inputDescriptorStr = await extractFaceDescriptor(imagePath);

	const THRESHOLD = 0.50;
	const distance = await compareFaceDescriptorFaceApi(
		userVectors.map((v) => v.descriptor),
		inputDescriptorStr,
	);

	const isMatch = distance <= THRESHOLD;

	await recordAttendanceAttempt({
		id: nanoid() as AttendanceAttemptsId,
		userId: user.id as UsersId,
		photo: payload.face,
		distance,
		isMatch,
		threshold: THRESHOLD,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
	});

	if (!isMatch) {
		console.log("Face distance score:", distance);
		throw new BadRequestError("Upps... sepertinya ini bukanlah wajah Anda");
	}

	let status: AttendanceStatus = AttendanceStatus.ON_TIME;

	if (payload.action === AttendanceType.CHECK_IN) {
		const start = dayjs.tz(
			`${today} ${attendanceRule.checkInStartTime}`,
			timezone,
		);
		const end = dayjs.tz(`${today} ${attendanceRule.checkInEndTime}`, timezone);

		// ❌ Lewat dari batas akhir check-in (tidak bisa absen)
		if (now.isAfter(end)) {
			throw new BadRequestError(
				`Waktu check-in sudah ditutup. Batas akhir check-in adalah jam ${attendanceRule.checkInEndTime}`,
			);
		}

		// ⚠️ Telat jika lewat dari start + tolerance
		if (
			now.isAfter(start.add(attendanceRule.checkInToleranceMinutes, "minute"))
		) {
			status = AttendanceStatus.LATE;
		}
	} else if (payload.action === AttendanceType.CHECK_OUT) {
		const start = dayjs.tz(
			`${today} ${attendanceRule.checkOutStartTime}`,
			timezone,
		);
		const end = dayjs.tz(
			`${today} ${attendanceRule.checkOutEndTime}`,
			timezone,
		);

		// ❌ Belum waktunya pulang
		if (now.isBefore(start)) {
			// throw new BadRequestError(
			// 	`Belum waktunya untuk pulang. Tidak bisa CHECK_OUT sebelum jam ${attendanceRule.checkOutStartTime}`,
			// );
			status = AttendanceStatus.EARLY;
		}

		// ❌ Lewat dari batas akhir check-out
		if (now.isAfter(end)) {
			throw new BadRequestError(
				`Waktu check-out sudah ditutup. Batas akhir check-out adalah jam ${attendanceRule.checkOutEndTime}`,
			);
		}
	}

	console.log(toDateOnlyString(now), "SAVED DATE TO DB");
	console.log(now.format("HH:mm:ss"), "SAVED TIME TO DB");

	// save attendance log
	await db
		.insertInto("attendanceLogs")
		.values({
			id: nanoid() as AttendanceLogsId,
			userId: user.id as UsersId,
			worksiteId: worksite.id as WorksitesId,
			type: payload.action,
			logDate: toDateOnlyString(now),
			logTime: now.format("HH:mm:ss"),
			status,
			locationLat: payload.latitude,
			locationLong: payload.longitude,
			distanceScore: distance,
			photo: payload.face,
			deviceInfo: userAgent,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.executeTakeFirstOrThrow();

	return {
		date: now.locale("id").format("dddd, D MMMM YYYY"),
		time: now.locale("id").format("HH:mm:ss"),
		attendanceStatus: status,
		faceStatus: "Terdeteksi dan sesuai",
		locationStatus: "Sesuai",
		type: payload.action,
		locationDistanceMeter: distanceMeter,
	};
};

export const getMyAttendanceHistory = async ({
	user,
	startDate,
	endDate,
}: {
	user: AuthUser;
	startDate?: string;
	endDate?: string;
}): Promise<AttendanceHistoryResponseDTO> => {
	let startDateFormatted: dayjs.Dayjs;
	let endDateFormatted: dayjs.Dayjs;

	if (startDate && endDate) {
		startDateFormatted = dayjs(startDate);
		endDateFormatted = dayjs(endDate);
	} else {
		const today = dayjs();
		const sevenDaysAgo = today.subtract(7, "day");
		startDateFormatted = sevenDaysAgo;
		endDateFormatted = today;
	}

	const logs = await db
		.selectFrom("attendanceLogs")
		.select(["id", "logDate", "logTime", "status", "type"])
		.where("userId", "=", user.id as UsersId)
		.where((eb) =>
			eb.between(
				"logDate",
				toDateOnlyString(startDateFormatted),
				toDateOnlyString(endDateFormatted),
			),
		)
		.execute();

	const histories: Record<
		string,
		{ id: string; date: string; time: string; status: string; type: string }[]
	> = {};
	logs.forEach((log) => {
		const formattedDate = dayjs(log.logDate).format("DD MMM YYYY");

		if (!histories[formattedDate]) {
			histories[formattedDate] = [];
		}

		histories[formattedDate].push({
			id: log.id,
			date: formattedDate,
			time: log.logTime,
			status: log.status,
			type: log.type,
		});
	});

	return histories;
};

export const getAttendanceState = (
	done: AttendanceType[],
	isHoliday: boolean,
	isOnLeave: boolean,
	isWorkingDay: boolean,
): AttendanceUIState => {
	// Priority 1: Check if it's a holiday
	if (isHoliday) {
		return "HOLIDAY";
	}

	// Priority 2: Check if user is on leave
	if (isOnLeave) {
		return "ON_LEAVE";
	}

	// Priority 3: Check if it's a working day
	// if (!isWorkingDay) {
	// 	return "NON_WORKING_DAY";
	// }

	// Priority 4: Check attendance flow for working days
	if (done.includes(AttendanceType.CHECK_OUT)) {
		return "DONE";
	}

	if (!done.includes(AttendanceType.CHECK_IN)) {
		return "NOT_YET_CHECKED_IN";
	}

	const isOnBreak = done.includes(AttendanceType.BREAK);

	if (isOnBreak && !done.includes(AttendanceType.RETURN)) {
		return "ON_BREAK";
	}

	if (done.includes(AttendanceType.RETURN)) {
		return "READY_TO_CHECKOUT";
	}

	return "CHECKED_IN";
};

export const getAttendanceConfiguration = async (
	user: AuthUser,
): Promise<AttendanceConfigurationResponseDTO> => {
	// TODO: Implementing cache
	// const worksiteCacheKey = CACHE_KEYS.worksite(user.id);
	// let worksite = await cacheHelpers.get<Worksites>(worksiteCacheKey);
	let worksite = null;

	if (!worksite) {
		const localWorksite = await db
			.selectFrom("usersWorksites")
			.innerJoin("worksites", "worksites.id", "usersWorksites.worksiteId")
			.select([
				"worksites.id",
				"worksites.name",
				"worksites.type",
				"worksites.address",
				"worksites.description",
				"worksites.createdAt",
				"worksites.updatedAt",
			])
			.where("userId", "=", user.id as UsersId)
			.executeTakeFirst();

		worksite = localWorksite;
		// TODO: Set caching
		// if (localWorksite) {
		// 	worksite = localWorksite;
		// 	await cacheHelpers.set(
		// 		worksiteCacheKey,
		// 		localWorksite,
		// 		CACHE_TTL.WORK_SITE,
		// 	);
		// }
	}

	if (!worksite) {
		throw new BadRequestError("Work site not assigned");
	}

	const attendanceRule = await db
		.selectFrom("attendanceRules")
		.selectAll()
		.where("worksiteId", "=", worksite.id as WorksitesId)
		.executeTakeFirst();

	if (!attendanceRule) {
		throw new BadRequestError("Belum ada aturan absensi di work-site ini.");
	}

	const timezone = attendanceRule.timezone || "Asia/Jakarta";
	const now = dayjs().tz(timezone);
	const today = now.format("YYYY-MM-DD");
	const _todayDay = now.day();
	const isWorkingDay = true;

	const [isHoliday, isOnLeave] = await Promise.all([
		db
			.selectFrom("holidays")
			.select(["id"])
			.where("date", "=", toDateOnlyString(now))
			.executeTakeFirst()
			.then((result: any) => !!result),
		db
			.selectFrom("leaveRequests")
			.select(["id"])
			.where("userId", "=", user.id as UsersId)
			.where("status", "=", "APPROVED")
			.where("startDate", "<=", toDateOnlyString(now))
			.where("endDate", ">=", toDateOnlyString(now))
			.executeTakeFirst()
			.then((result: any) => !!result),
	]);

	const logsToday = await db
		.selectFrom("attendanceLogs")
		.selectAll()
		.where("userId", "=", user.id as UsersId)
		.where("logDate", "=", toDateOnlyString(now))
		.execute();

	const doneTypes = logsToday.map((log: any) => log.type) as AttendanceType[];
	const attendanceState = getAttendanceState(
		doneTypes,
		isHoliday,
		isOnLeave,
		isWorkingDay,
	);

	console.log("loggedUser: ", user.name);
	console.log("logsToday", logsToday);
	console.log("attendanceState", attendanceState);
	console.log("location config: ", {
		latitude: attendanceRule.latitude,
		longitude: attendanceRule.longitude,
		maxDistanceInMeter: attendanceRule.radiusInMeter,
	});

	const officeLeaves = user.roles.find((role) => role.id === Env.HR_ROLE_ID)
		? await db
			.selectFrom("officeLeaves")
			.innerJoin("users", "users.id", "officeLeaves.userId")
			.leftJoin("users as reviewer", "reviewer.id", "reviewedBy")
			.selectAll("officeLeaves")
			.select(["users.name as userName", "users.email as userEmail"])
			.select([
				"reviewer.name as reviewerName",
				"reviewer.email as reviewerEmail",
			])
			.where("status", "=", OfficeLeaveStatus.NEED_REVIEW)
			.execute()
		: null;

	const config = {
		today: now.locale("id").format("dddd, D MMMM YYYY"),
		location: {
			latitude: attendanceRule.latitude,
			longitude: attendanceRule.longitude,
			maxDistanceInMeter: attendanceRule.radiusInMeter,
		},
		attendanceState,
		timestamps: {
			checkInAt:
				logsToday.find((log) => log.type === AttendanceType.CHECK_IN)
					?.logTime ?? null,
			breakAt:
				logsToday.find((log) => log.type === AttendanceType.BREAK)?.logTime ??
				null,
			returnAt:
				logsToday.find((log) => log.type === AttendanceType.RETURN)?.logTime ??
				null,
			checkOutAt:
				logsToday.find((log) => log.type === AttendanceType.CHECK_OUT)
					?.logTime ?? null,
		},
		reviewableOfficeLeaves: officeLeaves,
	};

	return config;
};

const recordAttendanceAttempt = async (attemptData: {
	id: AttendanceAttemptsId;
	userId: UsersId;
	photo: string;
	distance: number;
	isMatch: boolean;
	threshold: number;
	createdAt: string;
	updatedAt: string;
}) => {
	try {
		await db.insertInto("attendanceAttempts").values(attemptData).execute();
	} catch (error) {
		console.error("Failed to record attendance attempt:", error);
	}
};
