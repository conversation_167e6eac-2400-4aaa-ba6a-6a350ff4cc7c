import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type {
	AttendanceLogs,
	AttendanceLogsId,
} from "@/database/types/public/AttendanceLogs";
import type { UsersId } from "@/database/types/public/Users";
import type { WorksitesId } from "@/database/types/public/Worksites";
import { Env } from "@/shared/config/env.config";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import { type AttendanceLogQueryDTO, attendanceLogQueryDTO } from "../dtos";
import type {
	AdminCreateAttendanceLogDTO,
	AdminUpdateAttendanceLogDTO,
} from "../dtos/request";
import type { AttendanceLogResponseDTO } from "../dtos/response";

const ATTENDANCE_LOG_FILTERABLE_FIELDS: FilterableFields = {
	id: "attendanceLogs.id",
	status: "attendanceLogs.status",
	type: "attendanceLogs.type",
	logDate: "attendanceLogs.logDate",
	logTime: "attendanceLogs.logTime",
	userName: "users.name",
	userEmail: "users.email",
	worksiteName: "worksites.name",
	createdAt: "attendanceLogs.createdAt",
} as const;

const ATTENDANCE_LOG_FIELD_TYPES: FieldTypes = {
	id: "string",
	status: "string",
	type: "string",
	logDate: "date",
	logTime: "string",
	userName: "string",
	userEmail: "string",
	worksiteName: "string",
	createdAt: "date",
} as const;

const ATTENDANCE_LOG_SORTABLE_FIELDS: SortableFields = {
	id: "attendanceLogs.id",
	status: "attendanceLogs.status",
	type: "attendanceLogs.type",
	logDate: "attendanceLogs.logDate",
	logTime: "attendanceLogs.logTime",
	userName: "users.name",
	userEmail: "users.email",
	worksiteName: "worksites.name",
	createdAt: "attendanceLogs.createdAt",
} as const;

export const getAllAttendanceLog = async (
	queryParams: AttendanceLogQueryDTO,
): Promise<{ data: AttendanceLogResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		status,
		type,
		logDate,
		logTime,
		userName,
		userEmail,
		worksiteName,
	} = queryParams;

	let query = db
		.selectFrom("attendanceLogs")
		.innerJoin("users", "attendanceLogs.userId", "users.id")
		.innerJoin("worksites", "attendanceLogs.worksiteId", "worksites.id")
		.selectAll("attendanceLogs")
		.select([
			"users.name as userName",
			"users.email as userEmail",
			"worksites.name as worksiteName",
		]);

	let countQuery = db
		.selectFrom("attendanceLogs")
		.innerJoin("users", "attendanceLogs.userId", "users.id")
		.innerJoin("worksites", "attendanceLogs.worksiteId", "worksites.id");

	// searching
	if (search) {
		const searchTerm = `%${search}%`;
		query = query.where((eb) =>
			eb.or([
				eb("users.name", "ilike", searchTerm),
				eb("users.email", "ilike", searchTerm),
			]),
		);
		countQuery = countQuery.where((eb) =>
			eb.or([
				eb("users.name", "ilike", searchTerm),
				eb("users.email", "ilike", searchTerm),
			]),
		);
	}

	// filtering
	const filters: {
		status?: string | string[];
		type?: string | string[];
		logDate?: string | string[];
		logTime?: string | string[];
		userName?: string | string[];
		userEmail?: string | string[];
		worksiteName?: string | string[];
	} = {};
	if (status) filters.status = status;
	if (type) filters.type = type;
	if (logDate) filters.logDate = logDate;
	if (logTime) filters.logTime = logTime;
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (worksiteName) filters.worksiteName = worksiteName;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: ATTENDANCE_LOG_FIELD_TYPES,
			filterableFields: ATTENDANCE_LOG_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: ATTENDANCE_LOG_SORTABLE_FIELDS,
		defaultSort: {
			field: "attendanceLogs.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [attendanceLogs, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedAttendanceLogs = attendanceLogs.map((log) => ({
		...log,
		logDate: new Date(log.logDate).toLocaleDateString("id-ID", {
			year: "numeric",
			month: "long",
			day: "numeric",
		}),
		photo: `${Env.SERVER_URL}${signPath(PATHS.toPrivateUrl(log.photo))}`,
	}));

	return {
		data: formattedAttendanceLogs,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getAttendanceLog = async (
	attendanceLogId: string,
): Promise<AttendanceLogResponseDTO> => {
	const existAttendanceLog = await db
		.selectFrom("attendanceLogs")
		.innerJoin("users", "attendanceLogs.userId", "users.id")
		.innerJoin("worksites", "attendanceLogs.worksiteId", "worksites.id")
		.selectAll("attendanceLogs")
		.select([
			"users.name as userName",
			"users.email as userEmail",
			"worksites.name as worksiteName",
		])
		.where("attendanceLogs.id", "=", attendanceLogId as AttendanceLogsId)
		.executeTakeFirst();

	if (!existAttendanceLog) {
		throw new NotFoundError("Kehadiran tidak ditemukan");
	}

	return existAttendanceLog;
};

export const createAttendanceLog = async (
	payload: AdminCreateAttendanceLogDTO,
	userAgent: string,
): Promise<AttendanceLogResponseDTO> => {
	const [existUser, existWorksite] = await Promise.all([
		db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst(),
		db
			.selectFrom("worksites")
			.select(["id", "name"])
			.where("id", "=", payload.worksiteId as WorksitesId)
			.executeTakeFirst(),
	]);

	if (!existUser) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	if (!existWorksite) {
		throw new BadRequestError("Lokasi kerja yang dipilih tidak ditemukan");
	}

	const newAttendanceLog = await db
		.insertInto("attendanceLogs")
		.values({
			id: nanoid() as AttendanceLogsId,
			...payload,
			userId: existUser.id,
			worksiteId: existWorksite.id,
			distanceScore: 0,
			deviceInfo: userAgent,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newAttendanceLog,
		userName: existUser.name,
		userEmail: existUser.email,
		worksiteName: existWorksite.name,
	};
};

export const updateAttendanceLog = async (
	attendanceLogId: string,
	payload: AdminUpdateAttendanceLogDTO,
): Promise<AttendanceLogResponseDTO> => {
	const existAttendanceLog = await db
		.selectFrom("attendanceLogs")
		.selectAll()
		.where("id", "=", attendanceLogId as AttendanceLogsId)
		.executeTakeFirst();

	if (!existAttendanceLog) {
		throw new NotFoundError("Data kehadiran tidak ditemukan");
	}

	let existUser = null;
	if (payload.userId) {
		existUser = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existUser) {
			throw new BadRequestError("User yang dipilih tidak ditemukan");
		}
	}

	let existWorksite = null;
	if (payload.worksiteId) {
		existWorksite = await db
			.selectFrom("worksites")
			.select(["id", "name"])
			.where("id", "=", payload.worksiteId as WorksitesId)
			.executeTakeFirst();

		if (!existWorksite) {
			throw new BadRequestError("Lokasi kerja yang dipilih tidak ditemukan");
		}
	}

	const updateAttendanceLogData: Partial<AttendanceLogs> = {};

	if (payload.type) updateAttendanceLogData.type = payload.type;
	if (payload.status) updateAttendanceLogData.status = payload.status;
	if (payload.logDate) updateAttendanceLogData.logDate = payload.logDate;
	if (payload.logTime) updateAttendanceLogData.logTime = payload.logTime;
	if (payload.photo) updateAttendanceLogData.photo = payload.photo;
	if (payload.locationLong)
		updateAttendanceLogData.locationLong = payload.locationLong;
	if (payload.locationLat)
		updateAttendanceLogData.locationLat = payload.locationLat;
	if (payload.userId)
		updateAttendanceLogData.userId = payload.userId as UsersId;
	if (payload.worksiteId)
		updateAttendanceLogData.worksiteId = payload.worksiteId as WorksitesId;

	updateAttendanceLogData.updatedAt = new Date().toISOString();

	let updatedAttendanceLog = existAttendanceLog;
	if (Object.keys(updateAttendanceLogData).length > 1) {
		updatedAttendanceLog = await db
			.updateTable("attendanceLogs")
			.set(updateAttendanceLogData)
			.where("id", "=", attendanceLogId as AttendanceLogsId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	const finalUser =
		existUser ||
		(await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", updatedAttendanceLog.userId)
			.executeTakeFirstOrThrow());
	const finalWorksite =
		existWorksite ||
		(await db
			.selectFrom("worksites")
			.select(["id", "name"])
			.where("id", "=", updatedAttendanceLog.worksiteId)
			.executeTakeFirstOrThrow());

	return {
		...updatedAttendanceLog,
		userName: finalUser.name,
		userEmail: finalUser.email,
		worksiteName: finalWorksite.name,
	};
};

export const bulkAction = async (
	payload: BulkActionDTO,
): Promise<string | null> => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("attendanceLogs")
		.innerJoin("users", "attendanceLogs.userId", "users.id")
		.innerJoin("worksites", "attendanceLogs.worksiteId", "worksites.id")
		.selectAll("attendanceLogs")
		.select([
			"users.name as userName",
			"users.email as userEmail",
			"worksites.name as worksiteName",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			status,
			type,
			logDate,
			logTime,
			userName,
			userEmail,
			worksiteName,
		} = attendanceLogQueryDTO.parse(queryObj);

		// searching
		if (search) {
			const searchTerm = `%${search}%`;
			query = query.where((eb) =>
				eb.or([
					eb("users.name", "ilike", searchTerm),
					eb("users.email", "ilike", searchTerm),
				]),
			);
		}

		// filtering
		const filters: {
			status?: string | string[];
			type?: string | string[];
			logDate?: string | string[];
			logTime?: string | string[];
			userName?: string | string[];
			userEmail?: string | string[];
			worksiteName?: string | string[];
		} = {};
		if (status) filters.status = status;
		if (type) filters.type = type;
		if (logDate) filters.logDate = logDate;
		if (logTime) filters.logTime = logTime;
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (worksiteName) filters.worksiteName = worksiteName;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: ATTENDANCE_LOG_FILTERABLE_FIELDS,
			fieldTypes: ATTENDANCE_LOG_FIELD_TYPES,
		});

		// sorting
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: ATTENDANCE_LOG_SORTABLE_FIELDS,
			defaultSort: {
				field: "attendanceLogs.createdAt",
				direction: "desc",
			},
		});

		// apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as AttendanceLogsId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const roles = await sortedQuery.execute();

			const csv = await createCsv(roles);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"attendanceLogs.id",
			"in",
			selection.selectedIds as AttendanceLogsId[],
		);
		const roles = await query.execute();

		if (action === "export") {
			const csv = await createCsv(roles);

			return csv;
		}
	}

	return null;
};
