import { db } from "@/database/connection";
import type { AttendanceRulesId } from "@/database/types/public/AttendanceRules";
import { NotFoundError } from "@/shared/exceptions";
import { extractChangedFields } from "@/shared/utils";
import type { AdminUpdateAttendanceRuleDTO } from "../dtos/request";
import type { AttendanceRuleResponseDTO } from "../dtos/response";

export const updateAttendanceRule = async (
	attendanceRuleId: string,
	payload: AdminUpdateAttendanceRuleDTO,
): Promise<AttendanceRuleResponseDTO> => {
	const existAttendanceRule = await db
		.selectFrom("attendanceRules")
		.selectAll()
		.where("id", "=", attendanceRuleId as AttendanceRulesId)
		.executeTakeFirst();

	if (!existAttendanceRule) {
		throw new NotFoundError("Konfigurasi absensi tidak ditemukan");
	}

	const updateData = extractChangedFields(existAttendanceRule, payload, [
		"breakStartTime",
		"breakToleranceMinutes",
		"checkInStartTime",
		"checkInToleranceMinutes",
		"checkOutStartTime",
		"checkOutToleranceMinutes",
		"returnStartTime",
		"returnToleranceMinutes",
		"latitude",
		"longitude",
		"timezone",
		"radiusInMeter",
	]);

	let updatedAttendanceRule = existAttendanceRule;
	if (Object.entries(updateData).length > 0) {
		updateData.updatedAt = new Date().toISOString();

		updatedAttendanceRule = await db
			.updateTable("attendanceRules")
			.set(updateData)
			.where("id", "=", attendanceRuleId as AttendanceRulesId)
			.returningAll()
			.executeTakeFirstOrThrow();

		// TODO: add to admin activity log/audit log directly or using worker.
	}

	return updatedAttendanceRule;
};
