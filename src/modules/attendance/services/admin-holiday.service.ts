import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { HolidaysId } from "@/database/types/public/Holidays";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	extractChangedFields,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type HolidayDTO,
	type HolidayQueryDTO,
	holidayQueryDTO,
} from "../dtos";
import type {
	AdminCreateHolidayDTO,
	AdminUpdateHolidayDTO,
} from "../dtos/request";
import type { HolidayResponseDTO } from "../dtos/response";

const HOLIDAY_FILTERABLE_FIELDS: FilterableFields = {
	id: "holidays.id",
	date: "holidays.date",
	createdAt: "holidays.createdAt",
} as const;

const HOLIDAY_FIELD_TYPES: FieldTypes = {
	id: "string",
	date: "date",
	createdAt: "date",
} as const;

const HOLIDAY_SORTABLE_FIELDS: SortableFields = {
	id: "holidays.id",
	date: "holidays.date",
	createdAt: "holidays.createdAt",
} as const;

export const getAllHoliday = async (
	queryParams: HolidayQueryDTO,
): Promise<{ data: HolidayResponseDTO[]; meta: ResponseMetaDTO }> => {
	const { pageIndex, pageSize, search, sort, date, createdAt } = queryParams;

	const query = db.selectFrom("holidays").selectAll();

	const countQuery = db.selectFrom("holidays");

	// searching
	if (search) {
	}

	// filtering
	const filters: { date?: string | string[]; createdAt?: string | string[] } =
		{};
	if (date) filters.date = date;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: HOLIDAY_FIELD_TYPES,
			filterableFields: HOLIDAY_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: HOLIDAY_SORTABLE_FIELDS,
		defaultSort: {
			field: "holidays.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [roles, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: roles,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getHoliday = async (holidayId: string): Promise<HolidayDTO> => {
	const existHoliday = await db
		.selectFrom("holidays")
		.selectAll()
		.where("id", "=", holidayId as HolidaysId)
		.executeTakeFirst();

	if (!existHoliday) {
		throw new NotFoundError("Daftar libur tidak ditemukan");
	}

	return existHoliday;
};

export const createHoliday = async (
	payload: AdminCreateHolidayDTO,
): Promise<HolidayResponseDTO> => {
	const newHoliday = await db
		.insertInto("holidays")
		.values({
			id: nanoid() as HolidaysId,
			...payload,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return newHoliday;
};

export const updateHoliday = async (
	holidayId: string,
	payload: AdminUpdateHolidayDTO,
): Promise<HolidayResponseDTO> => {
	const existHoliday = await db
		.selectFrom("holidays")
		.selectAll()
		.where("id", "=", holidayId as HolidaysId)
		.executeTakeFirst();

	if (!existHoliday) {
		throw new NotFoundError("Daftar libur tidak ditemukan");
	}

	const updateData = extractChangedFields(existHoliday, payload, [
		"date",
		"description",
	]);

	let updatedHoliday = existHoliday;
	if (Object.entries(updateData).length > 0) {
		updatedHoliday = await db
			.updateTable("holidays")
			.set({
				...updateData,
				updatedAt: new Date().toISOString(),
			})
			.where("id", "=", holidayId as HolidaysId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	return updatedHoliday;
};

export const deleteHoliday = async (holidayId: string): Promise<void> => {
	const existHoliday = await db
		.selectFrom("holidays")
		.select(["id"])
		.where("id", "=", holidayId as HolidaysId)
		.executeTakeFirst();

	if (!existHoliday) {
		throw new NotFoundError("Daftar libur tidak ditemukan");
	}

	await db
		.deleteFrom("holidays")
		.where("id", "=", holidayId as HolidaysId)
		.executeTakeFirstOrThrow();
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db.selectFrom("holidays").selectAll();

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, date, createdAt } = holidayQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filters
		const filters: {
			date?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (date) filters.date = date;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: HOLIDAY_FILTERABLE_FIELDS,
			fieldTypes: HOLIDAY_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: HOLIDAY_SORTABLE_FIELDS,
			defaultSort: {
				field: "holidays.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as HolidaysId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"holidays.id",
			"in",
			selection.selectedIds as HolidaysId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
