import "dayjs/locale/id.js";

import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone.js";
import utc from "dayjs/plugin/utc.js";
import { db } from "@/database/connection";
import type { AttendanceAttempts } from "@/database/types/public/AttendanceAttempts";
import type { FaceVectors } from "@/database/types/public/FaceVectors";
import type { Users, UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import type { AttendanceAttemptResponseDTO } from "../dtos/response/attendance-attempt-response.dto";

dayjs.extend(utc);
dayjs.extend(timezone);

export const getAttendanceAttempts = async (): Promise<
	AttendanceAttemptResponseDTO[]
> => {
	const attempts = (await db
		.selectFrom("attendanceAttempts")
		.select(["photo", "distance", "isMatch", "createdAt", "userId", "threshold"])
		.orderBy("createdAt", "desc")
		.execute()) as AttendanceAttempts[];

	if (attempts.length === 0) {
		return [];
	}

	const users = (await db
		.selectFrom("users")
		.select(["id", "name", "image"])
		.where("id", "in", attempts.map((a) => a.userId) as UsersId[])
		.execute()) as Users[];

	const registrationImages = (await db
		.selectFrom("faceVectors")
		.select(["imagePath", "userId"])
		.where("userId", "in", users.map((u) => u.id) as UsersId[])
		.execute()) as FaceVectors[];

	return users.map((user) => {
		const userImage = user.image
			? `${Env.SERVER_URL}${signPath(PATHS.toUrl(user.image))}`
			: "";

		return {
			user: {
				...user,
				image: userImage,
			},
			registrationImages: registrationImages
				.filter((img) => img.userId === user.id)
				.map((img) => ({
					...img,
					imagePath: `${Env.SERVER_URL}${signPath(PATHS.toUrl(img.imagePath))}`,
				})),
			attempts: attempts
				.filter((attempt) => attempt.userId === user.id)
				.slice(0, 25)
				.map((attempt) => ({
					...attempt,
					photo: `${Env.SERVER_URL}${signPath(PATHS.toUrl(attempt.photo))}`,
					threshold: attempt.threshold ?? 0.4,
				})),
		};
	});
};
