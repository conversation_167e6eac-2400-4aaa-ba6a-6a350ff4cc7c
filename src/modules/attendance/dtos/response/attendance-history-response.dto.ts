import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const attendanceHistoryItemDTO = z.object({
	id: z.string().meta({
		description: "Attendance log ID",
		example: "Qah4cccEK0MsspUJ9ywAy",
	}),
	date: z.string().meta({
		description: "Formatted date",
		example: "15 Jan 2025",
	}),
	time: z.string().meta({
		description: "Time in HH:mm:ss:ss format",
		example: "08:30:15",
	}),
	status: z.string().meta({
		description: "Attendance status",
		example: "ON_TIME",
	}),
	type: z.string().meta({
		description: "Attendance type",
		example: "CHECK_IN",
	}),
});

export const attendanceHistoryResponseDTO = z
	.record(z.string(), z.array(attendanceHistoryItemDTO))
	.meta({
		description: "Attendance history grouped by date",
		example: {
			"15 Jan 2025": [
				{
					id: "abc123",
					date: "15 Jan 2025",
					time: "08:30:15",
					status: "ON_TIME",
					type: "CHECK_IN",
				},
			],
		},
	});
export type AttendanceHistoryResponseDTO = z.infer<
	typeof attendanceHistoryResponseDTO
>;
