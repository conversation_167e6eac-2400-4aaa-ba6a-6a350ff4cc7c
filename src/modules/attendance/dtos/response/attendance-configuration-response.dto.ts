import { officeLeaveResponseDTO } from "@/modules/office-leave/dtos/response";
import * as z from "zod";

export const attendanceConfigurationResponseDTO = z.object({
	today: z.string().meta({
		description: "Today's date in formatted string",
		example: "15 Jan 2025",
	}),
	location: z
		.object({
			latitude: z.number().meta({
				description: "Work site latitude",
				example: -6.2088,
			}),
			longitude: z.number().meta({
				description: "Work site longitude",
				example: 106.8456,
			}),
			maxDistanceInMeter: z.number().meta({
				description:
					"Maximum allowed distance in meters for attendance check-in",
				example: 250,
			}),
		})
		.meta({
			description: "Work site location coordinates",
		}),
	attendanceState: z
		.enum([
			"HOLIDAY",
			"ON_LEAVE",
			"NON_WORKING_DAY",
			"NOT_YET_CHECKED_IN",
			"CHECKED_IN",
			"ON_BREAK",
			"READY_TO_CHECKOUT",
			"DONE",
		])
		.meta({
			description:
				"Current attendance state - single source of truth for UI logic",
			example: "NOT_YET_CHECKED_IN",
		}),
	timestamps: z
		.object({
			checkInAt: z.string().nullable().meta({
				description: "Check-in time if already checked in",
				example: "08:30:15",
			}),
			breakAt: z.string().nullable().meta({
				description: "Break time if on break",
				example: null,
			}),
			returnAt: z.string().nullable().meta({
				description: "Return time if returned from break",
				example: null,
			}),
			checkOutAt: z.string().nullable().meta({
				description: "Check-out time if already checked out",
				example: null,
			}),
		})
		.meta({
			description: "Attendance timestamps for today",
		}),
	reviewableOfficeLeaves: z.array(officeLeaveResponseDTO).nullable(),
});
export type AttendanceConfigurationResponseDTO = z.infer<
	typeof attendanceConfigurationResponseDTO
>;
