import type { AttendanceAttempts } from "@/database/types/public/AttendanceAttempts";
import type { FaceVectors } from "@/database/types/public/FaceVectors";
import type { Users } from "@/database/types/public/Users";

export type AttendanceAttemptResponseDTO = {
    user: Pick<Users, "name" | "image">;
    registrationImages: Pick<FaceVectors, "imagePath">[];
    attempts: Pick<
        AttendanceAttempts,
        "photo" | "distance" | "isMatch" | "createdAt"
    >[];
};