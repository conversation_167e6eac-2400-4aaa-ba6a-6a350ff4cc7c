import z from "zod";
import "@/shared/lib/zod-extensions";

export const recordAttendanceResponseDTO = z.object({
	date: z.string().meta({
		description: "Formatted date in Indonesian locale",
		example: "Senin, 15 Januari 2025",
	}),
	time: z.string().meta({
		description: "Formatted time in HH:mm:ss:ss format",
		example: "08:30:15",
	}),
	attendanceStatus: z.string().meta({
		description: "Status of attendance (ON_TIME, LATE, EARLY)",
		example: "ON_TIME",
	}),
	faceStatus: z.string().meta({
		description: "Face recognition status",
		example: "<PERSON>rde<PERSON><PERSON>i dan sesuai",
	}),
	locationStatus: z.string().meta({
		description: "Location verification status",
		example: "Sesuai",
	}),
	type: z.string().meta({
		description: "Type of attendance action",
		example: "CHECK_IN",
	}),
	locationDistanceMeter: z.number().meta({
		description: "Distance from work site in meters",
		example: 25.5,
	}),
});
export type RecordAttendanceResponseDTO = z.infer<
	typeof recordAttendanceResponseDTO
>;
