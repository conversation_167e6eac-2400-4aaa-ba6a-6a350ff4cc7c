import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const faceVectorDTO = z
	.object({
		id: z.string().meta({
			description: "Unique identifier for the face vector",
			example: "cv_xEHrmF2czmf7joyscp5J2",
		}),
		userId: z.string().meta({
			description: "ID of the user who registered the face vector",
			example: "user_bXn1LnLm8GNqn2OKSE4db",
		}),
		descriptor: z.string().meta({
			description: "Binary face descriptor",
		}),
		imagePath: z.string().meta({
			description: "Path to the image file",
			example: "public/uploads/users/user123/face-sample-abc123.jpg",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("FaceVector");
