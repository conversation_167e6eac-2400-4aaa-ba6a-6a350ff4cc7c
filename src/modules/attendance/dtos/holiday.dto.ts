import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const holidayDTO = z
	.object({
		id: z.string().meta({
			example: "h_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the holiday",
		}),
		date: z.iso.date().meta({
			example: "2025-08-17",
			description: "Holiday date (DATEONLY format)",
		}),
		description: z.string().nullable().meta({
			example: "Hari Kemerdekaan Indonesia",
			description: "Holiday description/name",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Holiday");
export type HolidayDTO = z.infer<typeof holidayDTO>;

export const holidayQueryDTO = tableQueryDTO.extend({
	date: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type HolidayQueryDTO = z.infer<typeof holidayQueryDTO>;
