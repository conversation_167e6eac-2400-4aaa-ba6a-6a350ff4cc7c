import * as z from "zod";
import { tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const attendanceRuleDTO = z
	.object({
		id: z.string().meta({
			example: "ar_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the attendance rule",
		}),
		worksiteId: z.string().meta({
			example: "ws_Qah4cccEK0MsspUJ9ywAy",
			description: "ID of the work site this rule applies to",
		}),

		latitude: z.number().meta({
			description: "Latitude coordinate of the work site",
			example: -6.2088,
		}),
		longitude: z.number().meta({
			description: "Longitude coordinate of the work site",
			example: 106.8456,
		}),
		radiusInMeter: z.number().meta({
			description: "Allowed radius in meters for attendance check-in",
			example: 100,
		}),
		timezone: z.string().meta({
			description: "Timezone for the work site",
			example: "Asia/Jakarta",
		}),
		checkInStartTime: z.iso.time().meta({
			example: "06:00:00",
			description: "Start time for check-in in HH:mm:ss format",
		}),
		checkInEndTime: z.iso.time().meta({
			example: "10:15:00",
			description: "End time for check-in in HH:mm:ss format",
		}),
		checkInToleranceMinutes: z.number().meta({
			example: 30,
			description: "Tolerance in minutes for check-in",
		}),

		checkOutStartTime: z.iso.time().meta({
			example: "17:00:00",
			description: "Start time for check-out in HH:mm:ss format",
		}),
		checkOutEndTime: z.iso.time().meta({
			example: "23:59:00",
			description: "End time for check-out in HH:mm:ss format",
		}),
		checkOutToleranceMinutes: z.number().meta({
			example: 30,
			description: "Tolerance in minutes for check-out",
		}),

		breakStartTime: z.iso.time().meta({
			example: "12:00:00",
			description: "Start time for break in HH:mm:ss format",
		}),
		breakEndTime: z.iso.time().meta({
			example: "13:00:00",
			description: "End time for break in HH:mm:ss format",
		}),
		breakToleranceMinutes: z.number().meta({
			example: 30,
			description: "Tolerance in minutes for break",
		}),

		returnStartTime: z.iso.time().meta({
			example: "13:00:00",
			description: "Start time for return in HH:mm:ss format",
		}),
		returnEndTime: z.iso.time().meta({
			example: "14:00:00",
			description: "End time for return in HH:mm:ss format",
		}),
		returnToleranceMinutes: z.number().meta({
			example: 30,
			description: "Tolerance in minutes for return",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("AttendanceRule");
export type AttendanceRuleDTO = z.infer<typeof attendanceRuleDTO>;

export const attendanceRuleQueryDTO = tableQueryDTO.extend({});
export type AttendanceRuleQueryDTO = z.infer<typeof attendanceRuleQueryDTO>;
