import * as z from "zod";

export const registerFaceSampleDTO = z.object({
	face1: z.string().nonempty().meta({
		description: "Face sample 1",
		example: "/uploads/users/user123/face-sample-abc123.jpg",
	}),
	face2: z.string().nonempty().meta({
		description: "Face sample 2",
		example: "/uploads/users/user123/face-sample-abc123.jpg",
	}),
	face3: z.string().nonempty().meta({
		description: "Face sample 3",
		example: "/uploads/users/user123/face-sample-abc123.jpg",
	}),
});
export type RegisterFaceSampleDTO = z.infer<typeof registerFaceSampleDTO>;
