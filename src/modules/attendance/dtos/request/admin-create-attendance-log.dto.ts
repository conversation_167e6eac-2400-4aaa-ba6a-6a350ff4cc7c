import * as z from "zod";
import { AttendanceStatus, AttendanceType } from "@/shared/enums";
import "@/shared/lib/zod-extensions";

export const adminCreateAttendanceLogDTO = z.object({
	userId: z.string().nonempty(),
	worksiteId: z.string().nonempty(),
	type: z
		.enum([
			AttendanceType.CHECK_IN,
			AttendanceType.CHECK_OUT,
			AttendanceType.BREAK,
			AttendanceType.RETURN,
		])
		.meta({
			description: "Type of attendance action",
			example: AttendanceType.CHECK_IN,
		}),
	logDate: z.iso.date().meta({
		example: "2025-01-15",
		description: "Date of attendance (DATEONLY format)",
	}),
	logTime: z.iso.time().meta({
		example: "08:30:15",
		description: "Time of attendance in HH:mm:ss:ss format",
	}),
	status: z
		.enum([
			AttendanceStatus.ON_TIME,
			AttendanceStatus.EARLY,
			AttendanceStatus.LATE,
		])
		.meta({
			example: "ON_TIME",
			description: "Attendance status",
			enum: [AttendanceStatus.ON_TIME, AttendanceStatus.LATE],
		}),
	locationLat: z.coerce.number().min(-90).max(90).meta({
		example: -6.2088,
		description: "Latitude coordinate of attendance location",
	}),
	locationLong: z.coerce.number().min(-180).max(180).meta({
		example: 106.8456,
		description: "Longitude coordinate of attendance location",
	}),
	photo: z.string().nonempty({ error: "Photo gagal di unggah" }).meta({
		description: "Face image path after upload processing",
		example: "/uploads/users/user123/attendance-xyz789.jpg",
		format: "binary",
	}),
});
export type AdminCreateAttendanceLogDTO = z.infer<
	typeof adminCreateAttendanceLogDTO
>;
