import * as z from "zod";
import { AttendanceType } from "@/shared/enums";
import "@/shared/lib/zod-extensions";

export const createAttendanceLogDTO = z.object({
	action: z
		.enum([
			AttendanceType.CHECK_IN,
			AttendanceType.CHECK_OUT,
			AttendanceType.BREAK,
			AttendanceType.RETURN,
		])
		.meta({
			description: "Type of attendance action",
			example: AttendanceType.CHECK_IN,
		}),
	face: z.string().nonempty().meta({
		description: "Face image path after upload processing",
		example: "/uploads/users/user123/attendance-xyz789.jpg",
	}),
	latitude: z.coerce.number().min(-90).max(90).meta({
		description: "Latitude coordinate of attendance location",
		example: -6.2088,
	}),
	longitude: z.coerce.number().min(-180).max(180).meta({
		description: "Longitude coordinate of attendance location",
		example: 106.8456,
	}),
});
export type CreateAttendanceLogDTO = z.infer<typeof createAttendanceLogDTO>;
