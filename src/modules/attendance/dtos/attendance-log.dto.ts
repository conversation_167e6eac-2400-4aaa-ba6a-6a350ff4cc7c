import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import { AttendanceStatus, AttendanceType } from "@/shared/enums";
import "@/shared/lib/zod-extensions";

export const attendanceLogDTO = z
	.object({
		id: z.string().meta({
			description: "Unique identifier for the attendance log",
			example: "att_xEHrmF2czmf7joyscp5J2",
		}),
		userId: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "ID of the user who recorded attendance",
		}),
		worksiteId: z.string().meta({
			example: "ws_Qah4cccEK0MsspUJ9ywAy",
			description: "ID of the work site where attendance was recorded",
		}),
		type: z.string().meta({
			example: "CHECK_IN",
			description: "Type of attendance",
			enum: [
				AttendanceType.CHECK_IN,
				AttendanceType.CHECK_OUT,
				AttendanceType.BREAK,
				AttendanceType.RETURN,
			],
		}),
		logDate: z.iso.date().meta({
			example: "2025-01-15",
			description: "Date of attendance (DATEONLY format)",
		}),
		logTime: z.iso.time().meta({
			example: "08:30:15",
			description: "Time of attendance in HH:mm:ss:ss format",
		}),
		status: z.string().meta({
			example: "ON_TIME",
			description: "Attendance status",
			enum: [AttendanceStatus.ON_TIME, AttendanceStatus.LATE],
		}),
		locationLat: z.number().meta({
			example: -6.2088,
			description: "Latitude coordinate of attendance location",
		}),
		locationLong: z.number().meta({
			example: 106.8456,
			description: "Longitude coordinate of attendance location",
		}),
		distanceScore: z.number().meta({
			example: 0.95,
			description: "Face recognition distance score (0-1)",
		}),
		photo: z.string().meta({
			example: "/uploads/attendance/user123/att-photo-xyz789.jpg",
			description: "Path to attendance photo",
		}),
		deviceInfo: z.string().meta({
			example: "Android 12, Samsung Galaxy S21",
			description: "Device information used for attendance",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("AttendanceLog");

export const attendanceLogQueryDTO = tableQueryDTO.extend({
	type: filterFieldDTO.optional(),
	logDate: filterFieldDTO.optional(),
	logTime: filterFieldDTO.optional(),
	status: filterFieldDTO.optional(),
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	worksiteName: filterFieldDTO.optional(),
});
export type AttendanceLogQueryDTO = z.infer<typeof attendanceLogQueryDTO>;
