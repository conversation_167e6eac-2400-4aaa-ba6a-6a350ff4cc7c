import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { attendanceHistoryResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Attendance History",
	method: "get",
	path: "/api/v1/attendance/history",
	tags: ["attendance"],
	description: "Get user's attendance history",
	security: [{ bearerAuth: [] }],
	parameters: [
		{
			name: "startDate",
			in: "query",
			required: false,
			schema: {
				type: "string",
				format: "date",
				example: "2025-01-15",
			},
			description: "Start date for history filter (YYYY-MM-DD)",
		},
		{
			name: "endDate",
			in: "query",
			required: false,
			schema: {
				type: "string",
				format: "date",
				example: "2025-01-15",
			},
			description: "End date for history filter (YYYY-MM-DD)",
		},
	],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({ histories: attendanceHistoryResponseDTO }),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
