import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { recordAttendanceResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Record Attendance",
	method: "post",
	path: "/api/v1/attendance",
	tags: ["attendance"],
	description: "Record attendance with face recognition",
	security: [{ bearerAuth: [] }],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: ["face", "action", "latitude", "longitude"],
					properties: {
						face: {
							type: "string",
							format: "binary",
							description:
								"Face image file (JPEG, JPG, PNG, GIF, WEBP only, max 5MB)",
						},
						action: {
							type: "string",
							enum: ["CHECK_IN", "CHECK_OUT", "BREAK", "RETURN"],
							description: "Type of attendance action",
							example: "CHECK_IN",
						},
						latitude: {
							type: "number",
							minimum: -90,
							maximum: 90,
							description: "Latitude coordinate of attendance location",
							example: -6.2088,
						},
						longitude: {
							type: "number",
							minimum: -180,
							maximum: 180,
							description: "Longitude coordinate of attendance location",
							example: 106.8456,
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: recordAttendanceResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
