import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { attendanceLogResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Attendance Log [Admin]",
	method: "get",
	path: "/api/v1/admin/attendance/{attendanceLogId}",
	tags: ["attendance"],
	description:
		"Get list of attendance rules with pagination and filtering (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: attendanceLogResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
