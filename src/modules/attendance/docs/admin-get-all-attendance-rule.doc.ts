import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { attendanceRuleResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Attendance Rule [Admin]",
	method: "get",
	path: "/api/v1/admin/attendance-rules",
	tags: ["attendance"],
	description:
		"Get list of attendance rules with pagination and filtering (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(attendanceRuleResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
