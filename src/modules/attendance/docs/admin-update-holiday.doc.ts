import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateHolidayDTO } from "../dtos/request";
import { holidayResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Holiday [Admin]",
	method: "put",
	path: "/api/v1/admin/holidays/{holidayId}",
	tags: ["attendance"],
	description: "Update holiday for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			holidayId: z.string(),
		}),
		body: {
			content: {
				"application/json": {
					schema: adminCreateHolidayDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: holidayResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
