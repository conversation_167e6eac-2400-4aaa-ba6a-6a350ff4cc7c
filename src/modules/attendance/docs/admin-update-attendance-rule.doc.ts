import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateAttendanceRuleDTO } from "../dtos/request";
import { attendanceRuleResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Attendance Rule [Admin]",
	method: "put",
	path: "/api/v1/admin/attendance-rules/{attendanceRuleId}",
	tags: ["attendance"],
	description: "Create a new attendance rule (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			attendanceRuleId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateAttendanceRuleDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: attendanceRuleResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
