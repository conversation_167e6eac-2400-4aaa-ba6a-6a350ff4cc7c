import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateHolidayDTO } from "../dtos/request";
import { holidayResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create Holiday [Admin]",
	method: "post",
	path: "/api/v1/admin/holidays",
	tags: ["attendance"],
	description: "Create holiday for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			content: {
				"application/json": {
					schema: adminCreateHolidayDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: holidayResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
