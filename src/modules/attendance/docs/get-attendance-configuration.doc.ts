import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { attendanceConfigurationResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Attendance Configuration",
	method: "get",
	path: "/api/v1/attendance/configurations",
	tags: ["attendance"],
	description: "Get attendance configuration for current user",
	security: [{ bearerAuth: [] }],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: attendanceConfigurationResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
