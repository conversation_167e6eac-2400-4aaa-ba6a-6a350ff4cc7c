import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { attendanceLogResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create Attendance [Admin]",
	method: "post",
	path: "/api/v1/admin/attendance",
	tags: ["attendance"],
	description: "Create attendance log for admin (Admin Only)",
	security: [{ bearerAuth: [] }],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: [
						"userId",
						"worksiteId",
						"type",
						"logDate",
						"logTime",
						"status",
						"locationLat",
						"locationLong",
						"photo",
					],
					properties: {
						userId: {
							type: "string",
							description: "User ID",
							example: "user_abc123",
						},
						worksiteId: {
							type: "string",
							description: "Worksite ID",
							example: "site_456xyz",
						},
						type: {
							type: "string",
							enum: ["CHECK_IN", "CHECK_OUT", "BREAK", "RETURN"],
							description: "Type of attendance action",
							example: "CHECK_IN",
						},
						logDate: {
							type: "string",
							format: "date",
							description: "Date of attendance (DATEONLY format)",
							example: "2025-01-15",
						},
						logTime: {
							type: "string",
							pattern: "^\\d{2}:\\d{2}:\\d{2}$",
							description: "Time of attendance in HH:mm:ss format",
							example: "08:30:15",
						},
						status: {
							type: "string",
							enum: ["ON_TIME", "LATE"],
							description: "Attendance status",
							example: "ON_TIME",
						},
						locationLat: {
							type: "number",
							minimum: -90,
							maximum: 90,
							description: "Latitude coordinate of attendance location",
							example: -6.2088,
						},
						locationLong: {
							type: "number",
							minimum: -180,
							maximum: 180,
							description: "Longitude coordinate of attendance location",
							example: 106.8456,
						},
						photo: {
							type: "string",
							format: "binary",
							description:
								"Face image file (JPEG, JPG, PNG, GIF, WEBP only, max 5MB)",
						},
					},
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: attendanceLogResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
