import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { registerFaceSampleResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Register Face Sample",
	method: "post",
	path: "/api/v1/attendance/register-face-sample",
	tags: ["attendance"],
	description: "Register 3 face samples for biometric authentication",
	security: [{ bearerAuth: [] }],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: ["face1", "face2", "face3"],
					properties: {
						face1: {
							type: "string",
							format: "binary",
							description:
								"First face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)",
						},
						face2: {
							type: "string",
							format: "binary",
							description:
								"Second face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)",
						},
						face3: {
							type: "string",
							format: "binary",
							description:
								"Third face sample image (JPEG, JPG, PNG, WEBP only, max 5MB)",
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: registerFaceSampleResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
