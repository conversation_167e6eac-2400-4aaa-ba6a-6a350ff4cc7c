import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const notificationRecipient = z
	.object({
		id: z.string().meta({
			example: "nr_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for notification recipient",
		}),
		notificationId: z.string().meta({
			example: "n_bXn1LnLm8GNqn2OKSE4db",
			description: "ID of the notification",
		}),
		recipientId: z.string().meta({
			example: "rJGI0UrX29J2pJN5lj9vN",
			description: "ID of the recipient (user or admin_user)",
		}),
		recipientType: z.string().meta({
			example: "user",
			description: "Type of the recipient (user or admin_user)",
		}),
		status: z.string().meta({
			example: "UNREAD",
			description: "Notification status (UNREAD, READ, DISMISSED)",
		}),
		readAt: z.iso.datetime().nullable().meta({
			example: "2025-01-15T10:30:00Z",
			description: "When the notification was read",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("NotificationRecipientDTO");
