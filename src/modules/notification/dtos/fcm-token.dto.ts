import * as z from "zod";

export const fcmTokenDTO = z
	.object({
		id: z.string().meta({
			example: "ZnyW_MFf4YOkwpjoNzJf4",
			description: "Unique identifier for the FCM token",
		}),
		token: z.string().meta({
			example: "token",
			description: "FCM token string",
		}),
		ownerId: z.string().meta({
			example: "vQFWg0viuK2PJEP0Vw3V8",
			description: "ID of the user or admin user who owns this session",
		}),
		ownerType: z.string().meta({
			example: "user",
			description: "Type of the owner (user or admin_user)",
		}),
		timestamp: z.iso.datetime().meta({
			example: "2025-01-15T08:30:15.000Z",
			description: "Timestamp when the FCM token was created",
		}),
	})
	.openapi("FcmToken");
