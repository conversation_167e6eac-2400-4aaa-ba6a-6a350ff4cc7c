import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const notificationDTO = z
	.object({
		id: z.string().meta({
			example: "n_bXn1LnLm8GNqn2OKSE4db",
			description: "Unique identifier for the notification",
		}),
		type: z.string().meta({
			example: "TASK_ASSIGNED",
			description:
				"Type of notification, example (TASK_ASSIGNED, TASK_COMPLETED, LEAVE_REQUEST_APPROVED, SYSTEM_MESSAGE, etc.)",
		}),
		entityType: z.string().nullable().meta({
			example: "TASK",
			description:
				"Type of entity that triggered the notification, null if system message",
		}),
		entityId: z.string().nullable().meta({
			example: "task_xEHrmF2czmf7joyscp5J2",
			description: "ID of the entity that triggered the notification",
		}),
		actorId: z.string().nullable().meta({
			example: "user_Orx3CcXbu9m9xyjne6bbB",
			description:
				"ID of the user who performed the action, null if system message",
		}),
		actorType: z.string().nullable().meta({
			example: "user",
			description:
				"Type of the actor (user or admin_user), null if system message",
		}),
		messageTemplate: z.string().meta({
			example: "You have been assigned a new task: {{taskName}}",
			description: "Message template with placeholders",
		}),
		messageData: z.string().meta({
			example: '{"taskName": "Complete monthly report"}',
			description: "JSON data for message template placeholders",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Notification");
