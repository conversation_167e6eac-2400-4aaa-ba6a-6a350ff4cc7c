import * as z from "zod";

export const createUserHierarchyDTO = z
	.object({
		userId: z.string().nonempty().meta({
			description: "ID of the user being supervised",
			example: "user123",
		}),
		supervisorId: z.string().nonempty().meta({
			description: "ID of the supervisor",
			example: "supervisor456",
		}),
		roleId: z.string().nonempty().meta({
			description: "ID of the role that determines organizational level",
			example: "role789",
		}),
		isActive: z.boolean().default(true).meta({
			description: "Whether this hierarchy record is active",
			example: true,
		}),
	})
	.refine(
		(data) => {
			return data.userId !== data.supervisorId;
		},
		{
			path: ["supervisorId"],
			message: "User cannot be their own supervisor",
		},
	)
	.openapi("CreateUserHierarchyDTO");
export type CreateUserHierarchyDTO = z.infer<typeof createUserHierarchyDTO>;
