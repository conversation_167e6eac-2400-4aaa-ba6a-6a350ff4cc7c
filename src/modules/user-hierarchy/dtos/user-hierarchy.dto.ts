import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const userHierarchy = z
	.object({
		id: z.string().meta({
			example: "rArH5dqrePGhtUXdxmQfe",
			description: "Unique identifier for the user hierarchy",
		}),
		userId: z.string().meta({
			example: "rJGI0UrX29J2pJN5lj9vN",
			description: "ID of the user being supervised",
		}),
		supervisorId: z.string().nullable().meta({
			example: "Orx3CcXbu9m9xyjne6bbB",
			description: "ID of the supervisor (null for top level)",
		}),
		roleId: z.string().meta({
			example: "dJYbsuY0iO52io5rQoBrE",
			description: "ID of the role that determines organizational level",
		}),
		isActive: z.boolean().meta({
			example: true,
			description: "Whether this hierarchy record is active",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("UserHierarchy");
