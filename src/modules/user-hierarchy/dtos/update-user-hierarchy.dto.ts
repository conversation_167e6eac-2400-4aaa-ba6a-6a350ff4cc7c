import * as z from "zod";

export const updateUserHierarchyDTO = z
	.object({
		supervisorId: z.string().nonempty().optional().meta({
			description: "ID of the supervisor",
			example: "supervisor456",
		}),
		roleId: z.string().nonempty().optional().meta({
			description: "ID of the role that determines organizational level",
			example: "role789",
		}),
		isActive: z.boolean().optional().meta({
			description: "Whether this hierarchy record is active",
			example: true,
		}),
	})
	.openapi("UpdateUserHierarchyDTO");
export type UpdateUserHierarchyDTO = z.infer<typeof updateUserHierarchyDTO>;
