import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminViolationController from "../controllers/admin-violation.controller";
import { violationQueryDTO } from "../dtos";
import {
	adminCreateViolationDTO,
	adminUpdateViolationDTO,
} from "../dtos/request";

export const adminViolationRouter = Router();

adminViolationRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(violationQueryDTO),
	adminViolationController.getAllViolation,
);

adminViolationRouter.get(
	"/:violationId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminViolationController.getViolation,
);

adminViolationRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateViolationDTO),
	adminViolationController.createViolation,
);

adminViolationRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminViolationController.bulkAction,
);

adminViolationRouter.put(
	"/:violationId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateViolationDTO),
	adminViolationController.updateViolation,
);
