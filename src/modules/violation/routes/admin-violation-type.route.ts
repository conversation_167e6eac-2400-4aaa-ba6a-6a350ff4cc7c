import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminViolationTypeController from "../controllers/admin-violation-type.controller";
import { violationTypeQueryDTO } from "../dtos";
import {
	adminCreateViolationTypeDTO,
	adminUpdateViolationTypeDTO,
} from "../dtos/request";

export const adminViolationTypeRouter = Router();

adminViolationTypeRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(violationTypeQueryDTO),
	adminViolationTypeController.getAllViolationType,
);

adminViolationTypeRouter.get(
	"/options",
	adminViolationTypeController.getViolationTypeOptions,
);

adminViolationTypeRouter.get(
	"/:violationTypeId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminViolationTypeController.getViolationType,
);

adminViolationTypeRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateViolationTypeDTO),
	adminViolationTypeController.createViolationType,
);

adminViolationTypeRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminViolationTypeController.bulkAction,
);

adminViolationTypeRouter.put(
	"/:violationTypeId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateViolationTypeDTO),
	adminViolationTypeController.updateViolationType,
);
