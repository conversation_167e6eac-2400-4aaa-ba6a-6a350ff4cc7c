import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { authMiddleware, validateBody } from "@/shared/middlewares";
import * as violationController from "../controllers/violation.controller";
import { createViolationDTO } from "../dtos/request";

export const violationRouter = Router();

violationRouter.get(
	"/types",
	authMiddleware(AuthStrategy.JWT_USER),
	violationController.getViolationTypes,
);
violationRouter.get(
	"/points",
	authMiddleware(AuthStrategy.JWT_USER),
	violationController.getMyViolationListPoint,
);
violationRouter.get(
	"/types/:violationTypeId",
	authMiddleware(AuthStrategy.JWT_USER),
	violationController.getViolationTypeDetail,
);
violationRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	validate<PERSON>ody(createViolationDTO),
	violationController.createViolation,
);
