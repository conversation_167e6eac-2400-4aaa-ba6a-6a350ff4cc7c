import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { violationTypeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Violation Types",
	method: "get",
	path: "/api/v1/violations/types",
	tags: ["violation"],
	description: "Get violation types/category",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							violationTypes: z.array(violationTypeResponseDTO),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
