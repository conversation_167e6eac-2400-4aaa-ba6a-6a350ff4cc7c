import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateViolationTypeDTO } from "../dtos/request";
import { violationTypeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Violation Type [Admin]",
	method: "post",
	path: "/api/v1/admin/violation-types",
	tags: ["violation"],
	description: "Create new violation types/category for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			content: {
				"application/json": {
					schema: adminCreateViolationTypeDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationTypeResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
