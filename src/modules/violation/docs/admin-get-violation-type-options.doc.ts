import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO, optionItemDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get Violation Type Options [Admin]",
	method: "get",
	path: "/api/v1/admin/violation-types/options",
	tags: ["violation"],
	description: "Get violation-type options for admin",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(optionItemDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
