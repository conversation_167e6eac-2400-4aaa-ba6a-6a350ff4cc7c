import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { violationDTO } from "../dtos";

registry.registerPath({
	summary: "Get Violation [Admin]",
	method: "get",
	path: "/api/v1/admin/violations/{violationId}",
	tags: ["violation"],
	description: "Get single violation type for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			violationId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
