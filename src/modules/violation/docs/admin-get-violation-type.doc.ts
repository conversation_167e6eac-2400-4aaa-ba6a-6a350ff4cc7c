import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { violationTypeDTO } from "../dtos";

registry.registerPath({
	summary: "Get Violation Type [Admin]",
	method: "get",
	path: "/api/v1/admin/violation-types/{violationTypeId}",
	tags: ["violation"],
	description: "Get single violation type for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			violationTypeId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationTypeDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
