import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateViolationDTO } from "../dtos/request";
import { violationResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Violation [Admin]",
	method: "post",
	path: "/api/v1/admin/violations",
	tags: ["violation"],
	description: "Create new violation for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			content: {
				"application/json": {
					schema: adminCreateViolationDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
