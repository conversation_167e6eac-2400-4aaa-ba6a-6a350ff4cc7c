import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateViolationTypeDTO } from "../dtos/request";
import { violationTypeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Violation Type [Admin]",
	method: "put",
	path: "/api/v1/admin/violation-types/{violationTypeId}",
	tags: ["violation"],
	description: "Update violation types/category for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			violationTypeId: z.string(),
		}),
		body: {
			content: {
				"application/json": {
					schema: adminUpdateViolationTypeDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationTypeResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
