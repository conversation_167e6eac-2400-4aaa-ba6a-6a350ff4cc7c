import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { violationTypeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Violation Type [Admin]",
	method: "get",
	path: "/api/v1/admin/violation-types",
	tags: ["violation"],
	description: "Get all violation type for admin",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(violationTypeResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
