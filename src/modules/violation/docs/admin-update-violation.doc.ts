import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateViolationDTO } from "../dtos/request";
import { violationResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Violation [Admin]",
	method: "put",
	path: "/api/v1/admin/violations/{violationId}",
	tags: ["violation"],
	description: "Update violation for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			violationId: z.string(),
		}),
		body: {
			content: {
				"application/json": {
					schema: adminUpdateViolationDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
