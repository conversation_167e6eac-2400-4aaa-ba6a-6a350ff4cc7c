import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { violationTypeResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Violation Type Detail",
	method: "get",
	path: "/api/v1/violations/types/{violationTypeId}",
	tags: ["violation"],
	description: "Get violation-type detail",
	security: [{ bearerAuth: [] }],
	request: {},
	parameters: [
		{
			name: "violationTypeId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Violation type Id",
		},
	],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: violationTypeResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
