import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { UsersId } from "@/database/types/public/Users";
import type {
	Violations,
	ViolationsId,
} from "@/database/types/public/Violations";
import type { ViolationTypesId } from "@/database/types/public/ViolationTypes";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type ViolationDTO,
	type ViolationQueryDTO,
	violationQueryDTO,
} from "../dtos";
import type {
	AdminCreateViolationDTO,
	AdminUpdateViolationDTO,
} from "../dtos/request";
import type { ViolationResponseDTO } from "../dtos/response";

const VIOLATION_FILTERABLE_FIELDS: FilterableFields = {
	id: "violations.id",
	userName: "users.name",
	userEmail: "users.email",
	name: "violationTypes.name",
	penaltyPoints: "violationTypes.penaltyPoints",
	punishment: "violationTypes.punishment",
	violationDate: "violations.violationDate",
	recorderName: "recorder.name",
	recorderEmail: "recorder.email",
	createdAt: "violations.createdAt",
} as const;

const VIOLATION_FIELD_TYPES: FieldTypes = {
	id: "string",
	userName: "string",
	userEmail: "string",
	name: "string",
	penaltyPoints: "number",
	punishment: "string",
	violationDate: "date",
	recorderName: "string",
	recorderEmail: "string",
	createdAt: "date",
} as const;

const VIOLATION_SORTABLE_FIELDS: SortableFields = {
	id: "violations.id",
	userName: "users.name",
	userEmail: "users.email",
	name: "violationTypes.name",
	penaltyPoints: "violationTypes.penaltyPoints",
	punishment: "violationTypes.punishment",
	violationDate: "violations.violationDate",
	recorderName: "recorder.name",
	recorderEmail: "recorder.email",
	createdAt: "violations.createdAt",
} as const;

export const getAllViolation = async (
	queryParams: ViolationQueryDTO,
): Promise<{ data: ViolationResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		userName,
		userEmail,
		name,
		penaltyPoints,
		punishment,
		violationDate,
		recorderName,
		recorderEmail,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("violations")
		.innerJoin(
			"violationTypes",
			"violationTypes.id",
			"violations.violationTypeId",
		)
		.innerJoin("users", "users.id", "violations.userId")
		.innerJoin("users as recorder", "recorder.id", "violations.recordedBy")
		.selectAll("violations")
		.select([
			"violationTypes.name",
			"violationTypes.penaltyPoints",
			"violationTypes.punishment",
			"violationTypes.description",
		])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"recorder.name as recorderName",
			"recorder.email as recorderEmail",
		]);

	const countQuery = db
		.selectFrom("violations")
		.innerJoin(
			"violationTypes",
			"violationTypes.id",
			"violations.violationTypeId",
		)
		.innerJoin("users", "users.id", "violations.userId")
		.innerJoin("users as recorder", "recorder.id", "violations.recordedBy");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		userName?: string | string[];
		userEmail?: string | string[];
		name?: string | string[];
		penaltyPoints?: string | string[];
		punishment?: string | string[];
		violationDate?: string | string[];
		recorderName?: string | string[];
		recorderEmail?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (userName) filters.userName = userName;
	if (userEmail) filters.userEmail = userEmail;
	if (name) filters.name = name;
	if (penaltyPoints) filters.penaltyPoints = penaltyPoints;
	if (punishment) filters.punishment = punishment;
	if (violationDate) filters.violationDate = violationDate;
	if (recorderName) filters.recorderName = recorderName;
	if (recorderEmail) filters.recorderEmail = recorderEmail;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: VIOLATION_FIELD_TYPES,
			filterableFields: VIOLATION_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: VIOLATION_SORTABLE_FIELDS,
		defaultSort: {
			field: "violations.createdAt",
			direction: "desc",
		}
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [violations, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: violations,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getViolation = async (
	violationId: string,
): Promise<ViolationDTO> => {
	const violation = await db
		.selectFrom("violations")
		.selectAll()
		.where("id", "=", violationId as ViolationsId)
		.executeTakeFirst();

	if (!violation) {
		throw new NotFoundError("Pelanggaran tidak ditemukan");
	}

	return violation;
};

export const createViolation = async (
	payload: AdminCreateViolationDTO,
): Promise<ViolationResponseDTO> => {
	const [existUser, existViolationType, existRecorder] = await Promise.all([
		db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst(),
		db
			.selectFrom("violationTypes")
			.select(["id", "name", "penaltyPoints", "punishment", "description"])
			.where("id", "=", payload.violationTypeId as ViolationTypesId)
			.executeTakeFirst(),
		db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.recorderId as UsersId)
			.executeTakeFirst(),
	]);

	if (!existUser) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	if (!existViolationType) {
		throw new BadRequestError("Tipe pelanggaran yang dipilih tidak ditemukan");
	}

	if (!existRecorder) {
		throw new BadRequestError(
			"Pengisi pelanggaran yang dipilih tidak ditemukan",
		);
	}

	const newViolation = await db
		.insertInto("violations")
		.values({
			id: nanoid() as ViolationsId,
			userId: payload.userId as UsersId,
			violationTypeId: payload.violationTypeId as ViolationTypesId,
			violationDate: payload.violationDate,
			recordedBy: payload.recorderId as UsersId,
			notes: payload.notes,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newViolation,
		userName: existUser.name,
		userEmail: existUser.email,
		recorderName: existRecorder.name,
		recorderEmail: existRecorder.email,
		name: existViolationType.name,
		penaltyPoints: existViolationType.penaltyPoints,
		punishment: existViolationType.punishment,
		description: existViolationType.description,
	};
};

export const updateViolation = async (
	violationId: string,
	payload: AdminUpdateViolationDTO,
): Promise<ViolationResponseDTO> => {
	const existViolation = await getViolation(violationId);

	let existUser = null;
	if (payload.userId && payload.userId !== existViolation.userId) {
		existUser = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existUser) {
			throw new BadRequestError("User yang dipilih tidak ditemukan");
		}
	}

	let existViolationType = null;
	if (
		payload.violationTypeId &&
		payload.violationTypeId !== existViolation.violationTypeId
	) {
		existViolationType = await db
			.selectFrom("violationTypes")
			.select(["id", "name", "penaltyPoints", "punishment", "description"])
			.where("id", "=", payload.violationTypeId as ViolationTypesId)
			.executeTakeFirst();

		if (!existViolationType) {
			throw new BadRequestError(
				"Tipe pelanggaran yang dipilih tidak ditemukan",
			);
		}
	}

	let existRecorder = null;
	if (payload.recorderId && payload.recorderId !== existViolation.recordedBy) {
		existRecorder = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.recorderId as UsersId)
			.executeTakeFirst();

		if (!existRecorder) {
			throw new BadRequestError(
				"Pengisi pelanggaran yang dipilih tidak ditemukan",
			);
		}
	}

	const updateViolationData: Partial<Violations> = {};

	if (payload.userId && payload.userId !== existViolation.userId) {
		updateViolationData.userId = payload.userId as UsersId;
	}

	if (
		payload.violationTypeId &&
		payload.violationTypeId !== existViolation.violationTypeId
	) {
		updateViolationData.violationTypeId =
			payload.violationTypeId as ViolationTypesId;
	}

	if (
		payload.violationDate &&
		payload.violationDate !== existViolation.violationDate
	) {
		updateViolationData.violationDate = payload.violationDate;
	}

	if (payload.recorderId && payload.recorderId !== existViolation.recordedBy) {
		updateViolationData.recordedBy = payload.recorderId as UsersId;
	}

	if (payload.notes && payload.notes !== existViolation.notes) {
		updateViolationData.notes = payload.notes;
	}

	updateViolationData.updatedAt = new Date().toISOString();

	let updatedViolation = existViolation;
	if (Object.entries(updateViolationData).length > 1) {
		updatedViolation = await db
			.updateTable("violations")
			.set(updateViolationData)
			.where("id", "=", violationId as ViolationsId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	const finalUser =
		existUser ||
		(await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", updatedViolation.userId as UsersId)
			.executeTakeFirstOrThrow());
	const finalViolationType =
		existViolationType ||
		(await db
			.selectFrom("violationTypes")
			.select(["id", "name", "penaltyPoints", "punishment", "description"])
			.where("id", "=", updatedViolation.violationTypeId as ViolationTypesId)
			.executeTakeFirstOrThrow());
	const finalRecorder =
		existRecorder ||
		(await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", updatedViolation.recordedBy as UsersId)
			.executeTakeFirstOrThrow());

	return {
		...updatedViolation,
		userName: finalUser.name,
		userEmail: finalUser.email,
		recorderName: finalRecorder.name,
		recorderEmail: finalRecorder.email,
		name: finalViolationType.name,
		penaltyPoints: finalViolationType.penaltyPoints,
		punishment: finalViolationType.punishment,
		description: finalViolationType.description,
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("violations")
		.innerJoin(
			"violationTypes",
			"violationTypes.id",
			"violations.violationTypeId",
		)
		.innerJoin("users", "users.id", "violations.userId")
		.innerJoin("users as recorder", "recorder.id", "violations.recordedBy")
		.selectAll("violations")
		.select([
			"violationTypes.name",
			"violationTypes.penaltyPoints",
			"violationTypes.punishment",
			"violationTypes.description",
		])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"recorder.name as recorderName",
			"recorder.email as recorderEmail",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			userName,
			userEmail,
			name,
			penaltyPoints,
			punishment,
			violationDate,
			recorderName,
			recorderEmail,
			createdAt,
		} = violationQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filtering
		const filters: {
			userName?: string | string[];
			userEmail?: string | string[];
			name?: string | string[];
			penaltyPoints?: string | string[];
			punishment?: string | string[];
			violationDate?: string | string[];
			recorderName?: string | string[];
			recorderEmail?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (userName) filters.userName = userName;
		if (userEmail) filters.userEmail = userEmail;
		if (name) filters.name = name;
		if (penaltyPoints) filters.penaltyPoints = penaltyPoints;
		if (punishment) filters.punishment = punishment;
		if (violationDate) filters.violationDate = violationDate;
		if (recorderName) filters.recorderName = recorderName;
		if (recorderEmail) filters.recorderEmail = recorderEmail;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: VIOLATION_FILTERABLE_FIELDS,
			fieldTypes: VIOLATION_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: VIOLATION_SORTABLE_FIELDS,
			defaultSort: {
				field: "violations.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as ViolationsId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"violations.id",
			"in",
			selection.selectedIds as ViolationsId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
