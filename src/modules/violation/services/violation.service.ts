import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { UsersId } from "@/database/types/public/Users";
import type { ViolationsId } from "@/database/types/public/Violations";
import type { ViolationTypesId } from "@/database/types/public/ViolationTypes";
import { Env } from "@/shared/config/env.config";
import { KpiPeriodType } from "@/shared/enums";
import {
	BadRequestError,
	ForbiddenError,
	NotFoundError,
} from "@/shared/exceptions";
import type { AuthUser } from "@/shared/types";
import { toDateOnlyString } from "@/shared/utils/date";
import type { CreateViolationDTO } from "../dtos/request";
import type {
	ViolationResponseDTO,
	ViolationTypeResponseDTO,
} from "../dtos/response";

const _generateCurrentPeriod = (
	periodType: KpiPeriodType,
	providedPeriod?: string,
): string => {
	if (providedPeriod) return providedPeriod;

	const now = new Date();
	if (periodType === KpiPeriodType.YEARLY) {
		return now.getFullYear().toString();
	}
	// Monthly format: YYYY-MM
	return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
};

const _getPeriodDateRange = (
	period: string,
	periodType: KpiPeriodType,
): { startDate: Date; endDate: Date } => {
	if (periodType === KpiPeriodType.YEARLY) {
		const year = Number.parseInt(period);
		if (Number.isNaN(year)) {
			const now = new Date();
			return {
				startDate: new Date(now.getFullYear(), 0, 1),
				endDate: new Date(now.getFullYear(), 11, 31, 23, 59, 59),
			};
		}
		return {
			startDate: new Date(year, 0, 1),
			endDate: new Date(year, 11, 31, 23, 59, 59),
		};
	}

	const [yearStr, monthStr] = period.split("-");
	const year = Number(yearStr);
	const month = Number(monthStr);

	if (Number.isNaN(year) || Number.isNaN(month) || month < 1 || month > 12) {
		const now = new Date();
		return {
			startDate: new Date(now.getFullYear(), now.getMonth(), 1),
			endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59),
		};
	}

	const startDate = new Date(year, month - 1, 1);
	const endDate = new Date(year, month, 0, 23, 59, 59);
	return { startDate, endDate };
};

export const getViolationTypes = async () => {
	const violationTypes = await db
		.selectFrom("violationTypes")
		.selectAll()
		.where("isActive", "=", true)
		.execute();

	return violationTypes;
};

export const getMyViolationListPoint = async (
	user: AuthUser,
): Promise<ViolationResponseDTO[]> => {
	const currentPeriod = _generateCurrentPeriod(KpiPeriodType.YEARLY);
	const { startDate, endDate } = _getPeriodDateRange(
		currentPeriod,
		KpiPeriodType.YEARLY,
	);

	const violations = await db
		.selectFrom("violations")
		.innerJoin(
			"violationTypes",
			"violationTypes.id",
			"violations.violationTypeId",
		)
		.innerJoin("users", "users.id", "violations.userId")
		.innerJoin("users as recorder", "recorder.id", "violations.recordedBy")
		.selectAll("violations")
		.select([
			"violationTypes.penaltyPoints",
			"violationTypes.name",
			"violationTypes.description",
			"violationTypes.punishment",
		])
		.select(["users.name as userName", "users.email as userEmail"])
		.select([
			"recorder.name as recorderName",
			"recorder.email as recorderEmail",
		])
		.where((eb) =>
			eb.between(
				"violations.violationDate",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.where("violations.userId", "=", user.id as UsersId)
		.execute();

	return violations;
};

export const getViolationTypeDetail = async (
	violationTypeId: string,
): Promise<ViolationTypeResponseDTO> => {
	const violationType = await db
		.selectFrom("violationTypes")
		.selectAll()
		.where("id", "=", violationTypeId as ViolationTypesId)
		.executeTakeFirst();

	if (!violationType) {
		throw new NotFoundError(
			"Kategory pelanggaran yang dipilih tidak ditemukan",
		);
	}

	return violationType;
};

export const createViolation = async (
	payload: CreateViolationDTO,
	user: AuthUser,
): Promise<ViolationResponseDTO> => {
	const isHR = user.roles.find((role) => role.id === Env.HR_ROLE_ID);
	if (!isHR) {
		throw new ForbiddenError(
			"Anda tidak memiliki akses untuk membuat pelanggaran",
		);
	}

	const violationType = await db
		.selectFrom("violationTypes")
		.select(["id", "name", "penaltyPoints", "punishment", "description"])
		.where("id", "=", payload.violationTypeId as ViolationTypesId)
		.executeTakeFirst();

	if (!violationType) {
		throw new BadRequestError(
			"Kategori pelanggaran yang dipilih tidak ditemukan",
		);
	}

	const existUser = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", payload.userId as UsersId)
		.executeTakeFirst();

	if (!existUser) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	const newViolation = await db
		.insertInto("violations")
		.values({
			id: nanoid() as ViolationsId,
			recordedBy: user.id as UsersId,
			userId: existUser.id,
			violationDate: payload.violationDate || toDateOnlyString(new Date()),
			violationTypeId: violationType.id,
			notes: payload.notes,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...newViolation,
		name: violationType.name,
		description: violationType.description,
		penaltyPoints: violationType.penaltyPoints,
		punishment: violationType.punishment,
		userName: existUser.name,
		userEmail: existUser.email,
		recorderName: user.name,
		recorderEmail: user.email,
	};
};
