import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type {
	ViolationTypes,
	ViolationTypesId,
} from "@/database/types/public/ViolationTypes";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
} from "@/shared/dtos";
import { NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type ViolationType,
	type ViolationTypeQueryDTO,
	violationTypeQueryDTO,
} from "../dtos";
import type {
	AdminCreateViolationTypeDTO,
	AdminUpdateViolationTypeDTO,
} from "../dtos/request";
import type { ViolationTypeResponseDTO } from "../dtos/response";

const VIOLATION_TYPE_FILTERABLE_FIELDS: FilterableFields = {
	id: "violationTypes.id",
	name: "violationTypes.name",
	penaltyPoints: "violationTypes.penaltyPoints",
	punishment: "violationTypes.punishment",
	createdAt: "violationTypes.createdAt",
} as const;

const VIOLATION_TYPE_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	penaltyPoints: "number",
	punishment: "string",
	createdAt: "date",
} as const;

const VIOLATION_TYPE_SORTABLE_FIELDS: SortableFields = {
	id: "violationTypes.id",
	name: "violationTypes.name",
	penaltyPoints: "violationTypes.penaltyPoints",
	punishment: "violationTypes.punishment",
	createdAt: "violationTypes.createdAt",
} as const;

export const getAllViolationType = async (
	queryParams: ViolationTypeQueryDTO,
): Promise<{ data: ViolationTypeResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		name,
		penaltyPoints,
		punishment,
		createdAt,
	} = queryParams;

	const query = db.selectFrom("violationTypes").selectAll();

	const countQuery = db.selectFrom("violationTypes");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		name?: string | string[];
		penaltyPoints?: string | string[];
		punishment?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (penaltyPoints) filters.penaltyPoints = penaltyPoints;
	if (punishment) filters.punishment = punishment;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: VIOLATION_TYPE_FIELD_TYPES,
			filterableFields: VIOLATION_TYPE_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: VIOLATION_TYPE_SORTABLE_FIELDS,
		defaultSort: {
			field: "violationTypes.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [violationTypes, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: violationTypes,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getViolationType = async (
	violationTypeId: string,
): Promise<ViolationType> => {
	const violationType = await db
		.selectFrom("violationTypes")
		.where("id", "=", violationTypeId as ViolationTypesId)
		.selectAll()
		.executeTakeFirst();

	if (!violationType) {
		throw new NotFoundError("Kategori Pelanggaran tidak ditemukan");
	}

	return violationType;
};

export const getViolationTypeOptions = async (): Promise<OptionItemDTO[]> => {
	const violationTypes = await db
		.selectFrom("violationTypes")
		.select(["id", "name", "penaltyPoints", "punishment"])
		.execute();

	const options = violationTypes.map((violationType) => ({
		value: violationType.id,
		label: `${violationType.name} | ${violationType.punishment} | ${violationType.penaltyPoints} point`,
	}));

	return options;
};

export const createViolationType = async (
	payload: AdminCreateViolationTypeDTO,
) => {
	const newViolationType = await db
		.insertInto("violationTypes")
		.values({
			id: nanoid() as ViolationTypesId,
			...payload,
			isActive: true,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returningAll()
		.executeTakeFirstOrThrow();

	return newViolationType;
};

export const updateViolationType = async (
	violationTypeId: string,
	payload: AdminUpdateViolationTypeDTO,
): Promise<ViolationTypeResponseDTO> => {
	const existViolationType = await db
		.selectFrom("violationTypes")
		.where("id", "=", violationTypeId as ViolationTypesId)
		.selectAll()
		.executeTakeFirstOrThrow();

	if (!existViolationType) {
		throw new Error("Kategori Pelanggaran tidak ditemukan");
	}

	const updateViolationTypeData: Partial<ViolationTypes> = {};
	if (payload.name) updateViolationTypeData.name = payload.name;
	if (payload.description)
		updateViolationTypeData.description = payload.description;
	if (payload.penaltyPoints)
		updateViolationTypeData.penaltyPoints = payload.penaltyPoints;
	if (payload.punishment)
		updateViolationTypeData.punishment = payload.punishment;

	updateViolationTypeData.updatedAt = new Date().toISOString();

	let updatedViolationType = existViolationType;
	if (Object.entries(updateViolationTypeData).length > 1) {
		updatedViolationType = await db
			.updateTable("violationTypes")
			.where("id", "=", violationTypeId as ViolationTypesId)
			.set(updateViolationTypeData)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	return updatedViolationType;
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db.selectFrom("violationTypes").selectAll();

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, penaltyPoints, punishment, createdAt } =
			violationTypeQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filters
		const filters: {
			name?: string | string[];
			penaltyPoints?: string | string[];
			punishment?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (penaltyPoints) filters.penaltyPoints = penaltyPoints;
		if (punishment) filters.punishment = punishment;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: VIOLATION_TYPE_FILTERABLE_FIELDS,
			fieldTypes: VIOLATION_TYPE_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: VIOLATION_TYPE_SORTABLE_FIELDS,
			defaultSort: {
				field: "violationTypes.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as ViolationTypesId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"violationTypes.id",
			"in",
			selection.selectedIds as ViolationTypesId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
