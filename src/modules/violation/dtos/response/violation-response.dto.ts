import z from "zod";
import { violationDTO } from "../violation.dto";
import "@/shared/lib/zod-extensions";

export const violationResponseDTO = violationDTO
	.extend({
		penaltyPoints: z.number().meta({
			example: 10,
			description: "Penalty point for violation",
		}),
		name: z.string().meta({
			example: "Pelangaran Kategori A",
			description: "Violation type/category name",
		}),
		description: z.string().nullable().meta({
			description: "Description of the violation type/category",
		}),
		punishment: z.string().meta({
			example: "SP3",
			description: "Punishment of violation",
		}),
		userName: z.string().meta({
			example: "<PERSON> Doe",
			description: "Name of the user",
		}),
		userEmail: z.email().meta({
			example: "<EMAIL>",
			description: "Email of the user",
		}),
		recorderName: z.string().meta({
			example: "John Doe",
			description: "Name of the recorder",
		}),
		recorderEmail: z.email().meta({
			example: "<EMAIL>",
			description: "Email of the recorder",
		}),
	})
	.openapi("ViolationResponseDTO");
export type ViolationResponseDTO = z.infer<typeof violationResponseDTO>;
