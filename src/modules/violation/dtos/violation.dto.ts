import * as z from "zod";
import { registry } from "@/shared/docs/openapi-registry";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const violationDTO = z
	.object({
		id: z.string().meta({
			example: "v_bXn1LnLm8GNqn2OKSE4db",
			description: "Unique identifier for the violation",
		}),
		userId: z.string().meta({
			example: "user_rJGI0UrX29J2pJN5lj9vN",
			description: "ID of the user who committed the violation",
		}),
		violationTypeId: z.string().meta({
			example: "vt_xEHrmF2czmf7joyscp5J2",
			description: "ID of the violation type",
		}),
		violationDate: z.iso.datetime().meta({
			example: "2025-01-15",
			description: "Date when violation occurred (YYYY-MM-DD format)",
		}),
		notes: z.string().nullable().meta({
			example: "Terlambat 30 menit tanpa keterangan yang jelas",
			description: "Additional notes about the violation",
		}),
		recordedBy: z.string().meta({
			example: "admin_Orx3CcXbu9m9xyjne6bbB",
			description: "ID of the admin who recorded the violation",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Violation");
export type ViolationDTO = z.infer<typeof violationDTO>;

registry.register("Violation", violationDTO);

export const violationQueryDTO = tableQueryDTO.extend({
	userName: filterFieldDTO.optional(),
	userEmail: filterFieldDTO.optional(),
	name: filterFieldDTO.optional(),
	penaltyPoints: filterFieldDTO.optional(),
	punishment: filterFieldDTO.optional(),
	violationDate: filterFieldDTO.optional(),
	recorderName: filterFieldDTO.optional(),
	recorderEmail: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type ViolationQueryDTO = z.infer<typeof violationQueryDTO>;
