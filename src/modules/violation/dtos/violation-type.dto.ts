import * as z from "zod";
import { registry } from "@/shared/docs/openapi-registry";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const violationTypeDTO = z
	.object({
		id: z.string().meta({
			example: "vt_xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the violation type",
		}),
		name: z.string().meta({
			example: "Terlambat Masuk Kerja",
			description: "Violation type name",
		}),
		description: z.string().nullable().meta({
			example:
				"Pelanggaran ketika karyawan terlambat masuk kerja lebih dari 15 menit",
			description: "Violation type description",
		}),
		penaltyPoints: z.number().min(0).meta({
			example: 10,
			description: "Penalty points for this violation type",
		}),
		punishment: z.string().meta({
			example: "SP3",
			description: "Punishment",
		}),
		isActive: z.boolean().meta({
			example: true,
			description: "Whether this violation type is active",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("ViolationType");
export type ViolationType = z.infer<typeof violationTypeDTO>;

registry.register("ViolationType", violationTypeDTO);

export const violationTypeQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	penaltyPoints: filterFieldDTO.optional(),
	punishment: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type ViolationTypeQueryDTO = z.infer<typeof violationTypeQueryDTO>;
