import * as z from "zod";

export const createViolationDTO = z
	.object({
		userId: z.string().nonempty().meta({
			description: "ID of the user who committed the violat.ion",
			example: "user123",
		}),
		violationTypeId: z.string().nonempty().meta({
			description: "ID of the violation type",
			example: "violation-type-123",
		}),
		violationDate: z.iso.date().meta({
			description: "Date when violation occurred (YYYY-MM-DD format)",
			example: "2024-01-15",
		}),
		notes: z.string().optional().meta({
			description: "Additional notes about the violation",
			example: "Terlambat 30 menit tanpa keterangan",
		}),
	})
export type CreateViolationDTO = z.infer<typeof createViolationDTO>;
