import z from "zod";

export const adminCreateViolationDTO = z.object({
	recorderId: z.string().nonempty().meta({
		description: "ID of the user who recorded the violation",
		example: "user123",
	}),
	userId: z.string().nonempty().meta({
		description: "ID of the user who committed the violat.ion",
		example: "user123",
	}),
	violationTypeId: z.string().nonempty().meta({
		description: "ID of the violation type",
		example: "violation-type-123",
	}),
	violationDate: z.iso.date().meta({
		description: "Date when violation occurred (YYYY-MM-DD format)",
		example: "2024-01-15",
	}),
	notes: z.string().optional().meta({
		description: "Additional notes about the violation",
		example: "Terlambat 30 menit tanpa keterangan",
	}),
});
export type AdminCreateViolationDTO = z.infer<typeof adminCreateViolationDTO>;
