import * as z from "zod";

export const adminCreateViolationTypeDTO = z.object({
	name: z.string().nonempty().meta({
		description: "Violation type name",
		example: "Terlambat Masuk Ker<PERSON>",
	}),
	description: z.string().nonempty().optional().meta({
		description: "Violation type description",
		example: "Pelanggaran ketika karyawan terlambat masuk kerja",
	}),
	penaltyPoints: z.number().min(0).meta({
		description: "Penalty points for this violation type",
		example: 10,
	}),
	punishment: z.string().nonempty().meta({
		description: "Punishment",
		example: "SP3",
	}),
});
export type AdminCreateViolationTypeDTO = z.infer<
	typeof adminCreateViolationTypeDTO
>;
