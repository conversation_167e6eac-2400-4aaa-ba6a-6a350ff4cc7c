import type { NextFunction, Request, Response } from "express";
import type { AuthUser } from "@/shared/types";
import type { CreateViolationDTO } from "../dtos/request";
import * as violationService from "../services/violation.service";

export const getViolationTypes = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await violationService.getViolationTypes();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				violationTypes: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMyViolationListPoint = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await violationService.getMyViolationListPoint(
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				violations: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getViolationTypeDetail = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const violationTypeId = req.params.violationTypeId as string;
		const result =
			await violationService.getViolationTypeDetail(violationTypeId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createViolation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as CreateViolationDTO;
		const result = await violationService.createViolation(
			validatedBody,
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
