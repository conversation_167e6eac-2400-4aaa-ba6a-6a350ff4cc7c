import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { ViolationQueryDTO } from "../dtos";
import type {
	AdminCreateViolationDTO,
	AdminUpdateViolationDTO,
} from "../dtos/request";
import * as adminViolationService from "../services/admin-violation.service";

export const getAllViolation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as ViolationQueryDTO;
		const result = await adminViolationService.getAllViolation(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getViolation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const violationId = req.params.violationId as string;
		const result = await adminViolationService.getViolation(violationId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createViolation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateViolationDTO;
		const result = await adminViolationService.createViolation(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru.",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateViolation = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const violationId = req.params.violationId as string;
		const validatedBody = req.validatedBody as AdminUpdateViolationDTO;
		const result = await adminViolationService.updateViolation(
			violationId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminViolationService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=violations.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
