import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { ViolationTypeQueryDTO } from "../dtos";
import type {
	AdminCreateViolationTypeDTO,
	AdminUpdateViolationTypeDTO,
} from "../dtos/request";
import * as adminViolationTypeService from "../services/admin-violation-type.service";

export const getAllViolationType = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as ViolationTypeQueryDTO;
		const result =
			await adminViolationTypeService.getAllViolationType(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getViolationType = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const violationTypeId = req.params.violationTypeId as string;
		const result =
			await adminViolationTypeService.getViolationType(violationTypeId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getViolationTypeOptions = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await adminViolationTypeService.getViolationTypeOptions();

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createViolationType = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateViolationTypeDTO;
		const result =
			await adminViolationTypeService.createViolationType(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru.",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateViolationType = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const violationTypeId = req.params.violationTypeId as string;
		const validatedBody = req.validatedBody as AdminUpdateViolationTypeDTO;
		const result = await adminViolationTypeService.updateViolationType(
			violationTypeId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminViolationTypeService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=violationTypes.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
