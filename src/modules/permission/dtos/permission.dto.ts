import * as z from "zod";

export const permission = z
	.object({
		id: z.string().meta({
			description: "Unique identifier for the permission",
			example: "p_xEHrmF2czmf7joyscp5J2",
		}),
		name: z.string().meta({
			description: "Name of the permission",
			example: "VIEW_ATTENDANCE",
		}),
		description: z.string().nullable().meta({
			description: "Description of the permission",
			example: "Permission to view attendance",
		}),
	})
	.openapi("Permission");
