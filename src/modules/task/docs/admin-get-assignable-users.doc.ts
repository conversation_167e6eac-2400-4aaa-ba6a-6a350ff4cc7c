import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO, optionItemDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get User Can Receive Task [Admin]",
	method: "get",
	path: "/api/v1/admin/tasks/assignable-users",
	tags: ["task"],
	description:
		"Get users that can be received tasks based on hierarchy for admin",
	security: [{ bearerAuth: [] }],
	request: {
		query: z.object({
			userId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(optionItemDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
