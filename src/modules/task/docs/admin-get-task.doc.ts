import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskDTO } from "../dtos";

registry.registerPath({
	summary: "Get Task [Admin]",
	method: "get",
	path: "/api/v1/admin/tasks/{taskId}",
	tags: ["task"],
	description: "Get task detail by id for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			taskId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: taskDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
