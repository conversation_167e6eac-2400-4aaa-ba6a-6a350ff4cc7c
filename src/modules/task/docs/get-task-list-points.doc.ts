import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Task That Have A Point",
	method: "get",
	path: "/api/v1/tasks/points",
	tags: ["task"],
	description: "Get all task list that have a point",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							tasks: z.array(taskResponseDTO),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
