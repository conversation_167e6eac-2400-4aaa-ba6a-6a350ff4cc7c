import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { TaskStatus } from "@/shared/enums";
import { taskResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Task With Filter",
	method: "get",
	path: "/api/v1/tasks",
	tags: ["task"],
	description:
		"Get all task list with filter, default is viewing PENDING status and self tasks",
	security: [{ bearerAuth: [] }],
	request: {
		query: z.object({
			status: z
				.enum([
					TaskStatus.COMPLETED,
					TaskStatus.IN_PROGRESS,
					TaskStatus.IN_REVIEW,
					TaskStatus.PENDING,
					TaskStatus.REJECTED,
					TaskStatus.REVISION_REQUIRED,
				])
				.optional()
				.meta({
					example: TaskStatus.IN_PROGRESS,
					description: "Task status",
				}),
			userId: z.string().optional().meta({
				example: "5cG7D67SxRwr0LapfXMzc",
				description: "Viewing for the userId data",
			}),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							tasks: z.array(taskResponseDTO),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
