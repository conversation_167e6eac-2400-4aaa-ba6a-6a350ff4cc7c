import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskResponseDTO } from "../dtos/response";
import z from "zod";

registry.registerPath({
	summary: "Update Task [Admin]",
	method: "put",
	path: "/api/v1/admin/tasks/{taskId}",
	tags: ["task"],
	description: "Update task for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			taskId: z.string(),
		})
	},
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: [],
					properties: {
						name: {
							type: "string",
							description: "Task name/title",
							example: "Monthly Sales Report",
						},
						description: {
							type: "string",
							description: "Task description/details",
							example: "Prepare and submit the monthly sales report for Q1",
						},
						deadline: {
							type: "string",
							format: "date",
							description: "Task deadline in YYYY-MM-DD format",
							example: "2025-02-15",
						},
						assignerId: {
							type: "string",
							description: "User ID of the task assigner",
							example: "user1",
						},
						assigneeId: {
							type: "string",
							description: "User ID of the task assignee",
							example: "user2",
						},
						documents: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task document files (All files, max 10MB each, max 5 files)",
						},
						images: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files)",
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: taskResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
