import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { assignableUsersResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Assignable User for Task",
	method: "get",
	path: "/api/v1/tasks/assignable-users",
	tags: ["task"],
	description: "Get users that can be assigned tasks based on hierarchy",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.object({
							users: assignableUsersResponseDTO,
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
