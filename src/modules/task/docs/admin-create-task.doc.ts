import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Task [Admin]",
	method: "post",
	path: "/api/v1/admin/tasks",
	tags: ["task"],
	description: "Create new task for admin",
	security: [{ bearerAuth: [] }],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: [
						"name",
						"description",
						"deadline",
						"assignerId",
						"assigneeIds",
					],
					properties: {
						name: {
							type: "string",
							description: "Task name/title",
							example: "Monthly Sales Report",
						},
						description: {
							type: "string",
							description: "Task description/details",
							example: "Prepare and submit the monthly sales report for Q1",
						},
						deadline: {
							type: "string",
							format: "date",
							description: "Task deadline in YYYY-MM-DD format",
							example: "2025-02-15",
						},
						assignerId: {
							type: "string",
							description: "User ID of the task assigner",
							example: "user1",
						},
						assigneeIds: {
							type: "array",
							items: {
								type: "string",
							},
							description: "Array of user IDs to assign the task to",
							example: ["user1", "user2"],
						},
						documents: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task document files (All files, max 10MB each, max 5 files)",
						},
						images: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files)",
						},
					},
				},
			},
		},
	},
	responses: {
		201: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: z.array(taskResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
