import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { acceptOrRejectTaskDTO } from "../dtos/request";

// POST /api/v1/tasks/:taskId/accept-reject - Accept or reject task
registry.registerPath({
	summary: "Accept/Reject Task",
	method: "post",
	path: "/api/v1/tasks/{taskId}/accept-reject",
	tags: ["task"],
	description: "Accept or reject task (for assignees)",
	security: [{ bearerAuth: [] }],
	parameters: [
		{
			name: "taskId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Task ID",
		},
	],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: acceptOrRejectTaskDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
