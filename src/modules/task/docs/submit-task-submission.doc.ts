import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Submit Task Submission",
	method: "post",
	path: "/api/v1/tasks/{taskId}/submit",
	tags: ["task"],
	description: "Submit task with documents and notes (for assignees)",
	security: [{ bearerAuth: [] }],
	parameters: [
		{
			name: "taskId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Task ID",
		},
	],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					properties: {
						notes: {
							type: "string",
							description: "Submission notes (optional)",
							example:
								"Task completed successfully. Please review the attached documents.",
						},
						documents: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task submission document files (All files, max 10MB each, max 5 files)",
						},
						images: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task submission image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files)",
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "Task submitted successfully",
			content: {
				"application/json": {
					schema: baseResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
