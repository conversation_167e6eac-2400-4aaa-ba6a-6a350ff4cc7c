import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO, bulkActionDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Bulk Action Role [Admin]",
	method: "post",
	path: "/api/v1/admin/tasks/bulk-action",
	tags: ["task"],
	description: "Bulk action task for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: bulkActionDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({ data: z.any().optional() }),
				},
				"text/csv": {
					schema: {
						type: "string",
						format: "binary",
						example:
							"id,name,email\n1,Alice,<EMAIL>\n2,<PERSON>,<EMAIL>",
					},
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
