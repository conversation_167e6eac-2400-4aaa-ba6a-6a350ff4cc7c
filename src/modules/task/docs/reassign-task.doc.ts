import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskResponseDTO } from "../dtos/response";

// POST /api/v1/tasks/:taskId/reassign - Re-assign task
registry.registerPath({
	summary: "Re-Assign Task",
	method: "post",
	path: "/api/v1/tasks/{taskId}/reassign",
	tags: ["task"],
	description: "Re-assign task to another user (for assigners)",
	security: [{ bearerAuth: [] }],
	parameters: [
		{
			name: "taskId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Task ID",
		},
	],
	requestBody: {
		required: true,
		content: {
			"multipart/form-data": {
				schema: {
					type: "object",
					required: ["assigneeId"],
					properties: {
						assigneeId: {
							type: "string",
							description: "ID of the user to reassign the task to",
							example: "user123",
						},
						name: {
							type: "string",
							description: "Updated task name (optional)",
							example: "Updated Monthly Sales Report",
						},
						description: {
							type: "string",
							description: "Updated task description (optional)",
							example: "Updated description for the task",
						},
						deadline: {
							type: "string",
							format: "date",
							description: "Updated task deadline (optional)",
							example: "2025-03-15",
						},
						documents: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task document files (All files, max 10MB each, max 5 files)",
						},
						images: {
							type: "array",
							items: {
								type: "string",
								format: "binary",
							},
							description:
								"Task image files (JPEG, JPG, PNG, WEBP only, max 10MB each, max 5 files)",
						},
					},
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: taskResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		404: errorResponses.NotFound,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
