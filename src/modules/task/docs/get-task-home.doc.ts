import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskHomeResponseDTO } from "../dtos/response";

// GET /api/v1/tasks/home - Get task home data
registry.registerPath({
	summary: "Get Task Data For Home",
	method: "get",
	path: "/api/v1/tasks/home",
	tags: ["task"],
	description: "Get task data for home task page",
	security: [{ bearerAuth: [] }],
	request: {
		query: z.object({
			viewerId: z.string().optional().meta({
				example: "57Tz3obsBL16iN7KKebeB",
				description: "User id for viewing subordinate task data",
			}),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: taskHomeResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
