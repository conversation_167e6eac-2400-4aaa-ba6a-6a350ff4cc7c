import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { taskDetailResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Task Detail",
	method: "get",
	path: "/api/v1/tasks/{taskId}",
	tags: ["task"],
	description: "Get task detail by id",
	security: [{ bearerAuth: [] }],
	request: {},
	parameters: [
		{
			name: "taskId",
			in: "path",
			required: true,
			schema: {
				type: "string",
			},
			description: "Task ID",
		},
	],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: taskDetailResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
