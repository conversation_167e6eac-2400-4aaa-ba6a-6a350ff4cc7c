import dayjs from "dayjs";
import { sql } from "kysely";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { TaskSubmissionsId } from "@/database/types/public/TaskSubmissions";
import type { TasksId } from "@/database/types/public/Tasks";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import {
	KpiPeriodType,
	TASK_STATUS_LABEL_MAP,
	TaskStatus,
	TaskStatusColors,
	TaskSubmissionStatus,
} from "@/shared/enums";
import {
	BadRequestError,
	ForbiddenError,
	NotFoundError,
} from "@/shared/exceptions";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import type { AuthUser } from "@/shared/types";
import { toDateOnlyString } from "@/shared/utils/date";
import type {
	AcceptOrRejectTaskDTO,
	<PERSON><PERSON>TaskDTO,
	MarkTaskDTO,
	ReAssignTaskDTO,
	SubmitTaskSubmissionDTO,
} from "../dtos/request";
import type {
	AssignableUsersResponseDTO,
	TaskDetailResponseDTO,
	TaskHomeResponseDTO,
	TaskResponseDTO,
} from "../dtos/response";
import {
	_canAssignTask,
	_canAssignTaskToMultiple,
	_generateCurrentPeriod,
	_getPeriodDateRange,
	_getRecursiveSubordinates,
} from "./shared-task.service";

type DocumentUrl = {
	documents: string[];
	images: string[];
};

const generateMyTaskTabs = (
	taskStats: Record<TaskStatus, number>,
	currentUserId: UsersId, // ID user yang sedang login
	targetUserId?: UsersId, // ID user yang tugasnya sedang dilihat (opsional, jika melihat bawahan)
	baseApiUrl = `${Env.SERVER_URL}/api/v1/tasks`,
) => {
	return Object.entries(TASK_STATUS_LABEL_MAP).map(([status, label], idx) => {
		const statusValue = status as TaskStatus;

		// Tentukan apakah kita sedang melihat tugas user lain atau tugas sendiri
		const isViewingOtherUser = !!targetUserId && targetUserId !== currentUserId;
		const effectiveUserId = isViewingOtherUser ? targetUserId : currentUserId;

		// Buat URL query string
		let queryString = `status=${statusValue}`;
		// Tambahkan userId ke query string HANYA JIKA sedang melihat user lain
		if (isViewingOtherUser) {
			queryString += `&userId=${effectiveUserId}`;
		}

		// Buat objek filters
		const filters: { status: TaskStatus; userId?: UsersId } = {
			status: statusValue,
		};
		// Tambahkan userId ke objek filters HANYA JIKA sedang melihat user lain
		if (isViewingOtherUser) {
			filters.userId = effectiveUserId;
		}

		return {
			tabId: nanoid(),
			tabTitle: label,
			tabSource: `${baseApiUrl}?${queryString}`, // URL dengan filter yang dibutuhkan
			color: TaskStatusColors[statusValue],
			count: taskStats[statusValue] ?? 0,
			isDefault: idx === 0,
			filters: filters,
		};
	});
};

export const getTaskHome = async (
	user: AuthUser,
	viewerId?: string,
): Promise<TaskHomeResponseDTO> => {
	const isViewingOtherUser = !!viewerId && viewerId !== user.id;
	const targetUserId = viewerId || user.id;

	const subordinateIds = await _getRecursiveSubordinates(user.id);
	const filteredSubordinateIds = subordinateIds.filter((id) => id !== user.id);
	const userHasSubordinates = filteredSubordinateIds.length > 0;

	const users = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "in", [...filteredSubordinateIds, user.id as UsersId])
		.execute();

	const subordinateList = users.map((u) => ({
		id: u.id,
		name: u.name,
		email: u.email,
		isCurrentUser: u.id === user.id,
		isCurrentView: u.id === targetUserId,
	}));

	const rows = await db
		.selectFrom("tasks")
		.select(["status", (eb) => eb.fn.countAll().as("count")])
		.where((eb) =>
			eb.or([
				eb("assigneeId", "=", targetUserId as UsersId),
				eb("assignerId", "=", targetUserId as UsersId),
			]),
		)
		.groupBy("status")
		.execute();

	const taskStats: Record<TaskStatus, number> = {
		[TaskStatus.PENDING]: 0,
		[TaskStatus.IN_PROGRESS]: 0,
		[TaskStatus.IN_REVIEW]: 0,
		[TaskStatus.REVISION_REQUIRED]: 0,
		[TaskStatus.COMPLETED]: 0,
		[TaskStatus.REJECTED]: 0,
	};

	for (const row of rows) {
		const status = row.status as TaskStatus;
		taskStats[status] = Number(row.count);
	}

	const myTaskTabs = generateMyTaskTabs(
		taskStats,
		user.id as UsersId,
		targetUserId as UsersId,
	);

	const subordinateTasks =
		!isViewingOtherUser && userHasSubordinates
			? await db
					.selectFrom("tasks")
					.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
					.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
					.selectAll("tasks")
					.select([
						"assigner.name as assignerName",
						"assigner.email as assignerEmail",
						"assignee.name as assigneeName",
						"assignee.email as assigneeEmail",
					])
					.where("assignerId", "=", targetUserId as UsersId)
					.where("status", "=", TaskStatus.IN_REVIEW)
					.execute()
			: null;

	return {
		isViewingOtherUser,
		viewedUserName: isViewingOtherUser
			? subordinateList.find((user) => user.id === targetUserId)?.name ||
				"Unknown"
			: null,
		showSelectSubordinateOption: userHasSubordinates,
		showAddTaskButton: !isViewingOtherUser && userHasSubordinates,
		showSubordinateTaskListSection: !isViewingOtherUser && userHasSubordinates,

		subordinateList: userHasSubordinates ? subordinateList : null,
		subordinateTasks:
			!isViewingOtherUser && userHasSubordinates
				? subordinateTasks?.map((task) => ({
						...task,
						deadline: dayjs(task.deadline)
							.locale("id")
							.format("dddd, D MMMM YYYY"),
					})) || null
				: null,
		taskStats,
		myTaskTabs,
	};
};

export const getTasksWithFilter = async (
	user: AuthUser,
	filters: { status?: TaskStatus; userId?: string } = {
		status: TaskStatus.PENDING,
	},
): Promise<TaskResponseDTO[]> => {
	const { status, userId } = filters;

	const targetUserId = userId || user.id;

	let query = db
		.selectFrom("tasks")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.selectAll("tasks")
		.select([
			"assigner.name as assignerName",
			"assigner.email as assignerEmail",
			"assignee.name as assigneeName",
			"assignee.email as assigneeEmail",
		]);

	if (status) {
		query = query.where("tasks.status", "=", status);
	}

	query = query.where((eb) =>
		eb.or([
			eb("tasks.assigneeId", "=", targetUserId as UsersId),
			eb("tasks.assignerId", "=", targetUserId as UsersId),
		]),
	);

	const tasks = await query.execute();

	const formattedTasks = tasks.map((task) => ({
		...task,
		color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
		deadline: dayjs(task.deadline).locale("id").format("dddd, D MMMM YYYY"),
	}));

	return formattedTasks;
};

export const getMyTaskListPoint = async (
	user: AuthUser,
): Promise<TaskResponseDTO[]> => {
	const currentPeriod = _generateCurrentPeriod(KpiPeriodType.YEARLY);
	const { startDate, endDate } = _getPeriodDateRange(
		currentPeriod,
		KpiPeriodType.YEARLY,
	);

	const tasks = await db
		.selectFrom("tasks")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.selectAll("tasks")
		.select([
			"assigner.name as assignerName",
			"assigner.email as assignerEmail",
			"assignee.name as assigneeName",
			"assignee.email as assigneeEmail",
		])
		.where("assigneeId", "=", user.id as UsersId)
		.where("status", "=", TaskStatus.COMPLETED)
		.where((eb) =>
			eb.between(
				"createdAt",
				toDateOnlyString(startDate),
				toDateOnlyString(endDate),
			),
		)
		.execute();

	return tasks.map((task) => ({
		...task,
		color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
		deadline: dayjs(task.deadline).locale("id").format("dddd, D MMMM YYYY"),
	}));
};

export const getTaskDetail = async (
	taskId: string,
	user: AuthUser,
): Promise<TaskDetailResponseDTO> => {
	const task = await db
		.selectFrom("tasks")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.selectAll("tasks")
		.select([
			"assigner.name as assignerName",
			"assigner.email as assignerEmail",
			"assignee.name as assigneeName",
			"assignee.email as assigneeEmail",
		])
		.where("tasks.id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!task) {
		throw new NotFoundError("Task tidak ditemukan");
	}

	const isAssigner = task.assignerId === user.id;
	const isAssignee = task.assigneeId === user.id;

	let uiActions: string[] = [];

	if (isAssignee) {
		switch (task.status) {
			case TaskStatus.PENDING:
				uiActions = ["ACCEPT_TASK", "REJECT_TASK"];
				break;
			case TaskStatus.IN_PROGRESS:
			case TaskStatus.REVISION_REQUIRED:
				uiActions = ["SUBMIT_TASK"];
				break;
			case TaskStatus.IN_REVIEW:
				uiActions = [];
				break;
			default:
				uiActions = [];
				break;
		}
	}

	if (isAssigner) {
		// task has a submission with status PENDING_REVIEW
		const hasPendingReview = await db
			.selectFrom("taskSubmissions")
			.select(["id"])
			.where("taskId", "=", task.id as TasksId)
			.where("status", "=", TaskSubmissionStatus.PENDING_REVIEW)
			.executeTakeFirst();

		if (task.status === TaskStatus.IN_REVIEW && hasPendingReview) {
			uiActions = ["MARK_AS_COMPLETE", "MARK_NEED_REVISION"];
		}

		if (task.status === TaskStatus.REJECTED) {
			uiActions = ["RE_ASSIGN"];
		}
	}

	const submissions = await db
		.selectFrom("taskSubmissions")
		.leftJoin("users as user", "user.id", "taskSubmissions.userId")
		.leftJoin("users as reviewer", "reviewer.id", "taskSubmissions.reviewedBy")
		.selectAll("taskSubmissions")
		.select(["user.name as userName", "reviewer.name as reviewerName"])
		.where("taskSubmissions.taskId", "=", taskId as TasksId)
		.execute();

	const documentUrls = JSON.parse(task.documentUrls) as DocumentUrl;
	const formattedDocumentUrls: DocumentUrl = {
		images: documentUrls.images.map(
			(image) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(image))}`,
		),
		documents: documentUrls.documents.map(
			(document) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(document))}`,
		),
	};

	const formattedSubmissions = submissions.map((submission) => {
		const documentUrls = JSON.parse(submission.documentUrls) as DocumentUrl;
		const formattedDocumentUrls: DocumentUrl = {
			images: documentUrls.images.map(
				(image) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(image))}`,
			),
			documents: documentUrls.documents.map(
				(document) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(document))}`,
			),
		};

		return {
			...submission,
			reviewDate: submission.reviewDate
				? dayjs(submission.reviewDate).locale("id").format("D MMMM YYYY")
				: null,
			submittedAt: dayjs(submission.submittedAt)
				.locale("id")
				.format("D MMMM YYYY"),
			documentUrls: JSON.stringify(formattedDocumentUrls),
		};
	});

	const formattedTask = {
		...task,
		deadline: dayjs(task.deadline).locale("id").format("dddd, D MMMM YYYY"),
		documentUrls: JSON.stringify(formattedDocumentUrls),
		submissions: formattedSubmissions,
		uiActions: uiActions,
		color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
	};

	return formattedTask;
};

export const getAssignableUsers = async (
	user: AuthUser,
): Promise<AssignableUsersResponseDTO> => {
	const subqueryActiveTaskCount = db
		.selectFrom("tasks")
		.select(["assigneeId", sql<number>`COUNT(*)`.as("activeTaskCount")])
		.where((eb) =>
			eb("status", "not in", [
				TaskStatus.COMPLETED,
				TaskStatus.REJECTED,
				TaskStatus.PENDING,
			]),
		)
		.groupBy("assigneeId")
		.as("activeTasks");

	// Ambil users yang merupakan bawahan langsung (supervised by current user)
	const users = await db
		.selectFrom("userHierarchies")
		.innerJoin("users", "users.id", "userHierarchies.userId")
		.leftJoin(subqueryActiveTaskCount, "activeTasks.assigneeId", "users.id")
		.select([
			"users.id",
			"users.name",
			"users.email",
			"activeTasks.activeTaskCount",
		])
		.where("userHierarchies.supervisorId", "=", user.id as UsersId)
		.execute();

	// Format hasil
	const formattedUsers = users.map((user) => ({
		id: user.id,
		name: user.name,
		email: user.email,
		activeTask: user.activeTaskCount ?? 0,
	}));

	return formattedUsers;
};

export const createTask = async (
	user: AuthUser,
	payload: CreateTaskDTO,
): Promise<TaskResponseDTO[]> => {
	if (payload.assigneeIds.includes(user.id)) {
		throw new BadRequestError(
			"Anda tidak dapat menugaskan task kepada diri sendiri.",
		);
	}

	const assignees = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "in", payload.assigneeIds as UsersId[])
		.execute();

	if (assignees.length !== payload.assigneeIds.length) {
		throw new BadRequestError("Beberapa user yang dipilih tidak ditemukan");
	}

	const canAssign = await _canAssignTaskToMultiple(
		user.id,
		payload.assigneeIds,
	);

	if (!canAssign) {
		throw new ForbiddenError(
			"Anda tidak dapat assign task ke beberapa user tersebut berdasarkan hierarki",
		);
	}

	const results = await db
		.insertInto("tasks")
		.values(
			payload.assigneeIds.map((assigneeId) => ({
				id: nanoid() as TasksId,
				name: payload.name,
				description: payload.description,
				deadline: payload.deadline,
				assignerId: user.id as UsersId,
				assigneeId: assigneeId as UsersId,
				documentUrls: JSON.stringify({
					documents: payload.documents || [],
					images: payload.images || [],
				}),
				status: TaskStatus.PENDING,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})),
		)
		.returningAll()
		.execute();

	return results.map((task) => ({
		...task,
		deadline: dayjs(task.deadline).locale("id").format("dddd, D MMMM YYYY"),
		assignerName: user.name,
		assignerEmail: user.email,
		assigneeName:
			assignees.find((assignee) => assignee.id === task.assigneeId)?.name ||
			"Unknown",
		assigneeEmail:
			assignees.find((assignee) => assignee.id === task.assigneeId)?.email ||
			"Unknown",
		color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
	}));
};

export const reAssignTask = async ({
	user,
	taskId,
	payload,
}: {
	taskId: string;
	payload: ReAssignTaskDTO;
	user: AuthUser;
}): Promise<TaskResponseDTO> => {
	if (payload.assigneeId === user.id) {
		throw new BadRequestError(
			"Anda tidak dapat re-assign task kepada diri sendiri.",
		);
	}

	const existAssignee = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", payload.assigneeId as UsersId)
		.executeTakeFirst();

	if (!existAssignee) {
		throw new BadRequestError("User yang dipilih tidak ditemukan");
	}

	const existTask = await db
		.selectFrom("tasks")
		.selectAll()
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!existTask) {
		throw new NotFoundError("Task tidak ditemukan");
	}

	if (existTask.assignerId !== user.id) {
		throw new ForbiddenError("Hanya assigner yang dapat mengubah task");
	}

	if (existTask.status !== TaskStatus.REJECTED) {
		throw new BadRequestError(
			"Hanya dapat melakukan re-assign pada task yang ditolak",
		);
	}

	const canAssign = await _canAssignTask(user.id, payload.assigneeId);

	if (!canAssign) {
		throw new ForbiddenError(
			"Anda tidak dapat assign task ke user tersebut berdasarkan hierarki",
		);
	}

	const documentUrlsObject = JSON.parse(existTask.documentUrls) as DocumentUrl;
	const documentUrls =
		payload.documents && payload.images
			? JSON.stringify({
					documents: payload.documents,
					images: payload.images,
				})
			: payload.documents
				? JSON.stringify({
						documents: payload.documents,
						images: documentUrlsObject.images,
					})
				: payload.images
					? JSON.stringify({
							documents: documentUrlsObject.documents,
							images: payload.images,
						})
					: JSON.stringify(documentUrlsObject);

	const updatedTask = await db
		.updateTable("tasks")
		.set({
			name: payload.name || existTask.name,
			description: payload.description || existTask.description,
			deadline: payload.deadline || existTask.deadline,
			documentUrls,
			assigneeId: payload.assigneeId as UsersId,
			status: TaskStatus.PENDING,
			rejectionReason: null,
			finalFeedback: null,
			ratingPoint: 0,
			updatedAt: new Date().toISOString(),
		})
		.where("id", "=", existTask.id as TasksId)
		.returningAll()
		.executeTakeFirstOrThrow();

	return {
		...updatedTask,
		assignerName: user.name,
		assignerEmail: user.email,
		assigneeName: existAssignee.name,
		assigneeEmail: existAssignee.email,
		color: TaskStatusColors[updatedTask.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[updatedTask.status as TaskStatus] || "Unknown",
	};
};

export const submitTaskSubmission = async ({
	user,
	taskId,
	payload,
}: {
	user: AuthUser;
	taskId: string;
	payload: SubmitTaskSubmissionDTO;
}): Promise<void> => {
	const task = await db
		.selectFrom("tasks")
		.select(["id", "assigneeId", "status"])
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!task) {
		throw new NotFoundError("Task tidak ditemukan");
	}

	if (task.assigneeId !== user.id) {
		throw new ForbiddenError(
			"Anda tidak memiliki akses untuk melakukan submit hasil kerja pada task ini",
		);
	}

	if (
		task.status !== TaskStatus.IN_PROGRESS &&
		task.status !== TaskStatus.REVISION_REQUIRED
	) {
		throw new BadRequestError(
			`Task hanya bisa di submit jika dalam status ${TaskStatus.IN_PROGRESS} atau ${TaskStatus.REVISION_REQUIRED}`,
		);
	}

	const existingPending = await db
		.selectFrom("taskSubmissions")
		.select(["id"])
		.where("taskId", "=", task.id as TasksId)
		.where("userId", "=", user.id as UsersId)
		.where("status", "=", TaskSubmissionStatus.PENDING_REVIEW)
		.executeTakeFirst();

	if (existingPending) {
		throw new BadRequestError(
			"Anda memiliki submission yang belum direview, tidak dapat submit lagi",
		);
	}

	const submissionNumber = await db
		.selectFrom("taskSubmissions")
		.select(["submissionNumber"])
		.where("taskId", "=", task.id as TasksId)
		.where("userId", "=", user.id as UsersId)
		.orderBy("submissionNumber", "desc")
		.executeTakeFirst();

	const nextSubmissionNumber = submissionNumber
		? Number(submissionNumber.submissionNumber) + 1
		: 1;

	await db.transaction().execute(async (trx) => {
		await trx
			.insertInto("taskSubmissions")
			.values({
				id: nanoid() as TaskSubmissionsId,
				taskId: task.id as TasksId,
				userId: user.id as UsersId,
				documentUrls: JSON.stringify({
					documents: payload.documents || [],
					images: payload.images || [],
				}),
				notes: payload.notes || null,
				status: TaskSubmissionStatus.PENDING_REVIEW,
				reviewedBy: null,
				submissionNumber: nextSubmissionNumber,
				submittedAt: new Date().toISOString(),
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();

		await trx
			.updateTable("tasks")
			.set({
				status: TaskStatus.IN_REVIEW,
				updatedAt: new Date().toISOString(),
			})
			.where("id", "=", task.id as TasksId)
			.executeTakeFirstOrThrow();
	});
};

export const acceptOrRejectTask = async ({
	taskId,
	payload,
	user,
}: {
	taskId: string;
	payload: AcceptOrRejectTaskDTO;
	user: AuthUser;
}): Promise<void> => {
	const task = await db
		.selectFrom("tasks")
		.select(["id", "assigneeId", "status"])
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!task) {
		throw new NotFoundError("Task tidak ditemukan");
	}

	if (task.assigneeId !== user.id) {
		throw new ForbiddenError(
			"Anda tidak memiliki akses untuk menerima/menolak task ini",
		);
	}

	if (task.status !== "PENDING") {
		throw new BadRequestError(
			`Task dengan status ${task.status} tidak dapat di reject/accept`,
		);
	}

	if (payload.action === "ACCEPT") {
		await db
			.updateTable("tasks")
			.set({ status: TaskStatus.IN_PROGRESS })
			.where("id", "=", task.id as TasksId)
			.executeTakeFirstOrThrow();
	} else {
		await db
			.updateTable("tasks")
			.set({
				status: TaskStatus.REJECTED,
				rejectionReason: payload.rejectionReason || null,
			})
			.where("id", "=", task.id as TasksId)
			.executeTakeFirstOrThrow();
	}
};

export const markTask = async ({
	user,
	taskId,
	payload,
}: {
	user: AuthUser;
	taskId: string;
	payload: MarkTaskDTO;
}): Promise<void> => {
	if (payload.action === "COMPLETED" && !payload.ratingPoint) {
		throw new BadRequestError(
			"Rating point harus diisi jika task diselesaikan",
		);
	}

	const task = await db
		.selectFrom("tasks")
		.select(["id", "assignerId", "status"])
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!task) {
		throw new NotFoundError("Task tidak ditemukan");
	}

	if (task.assignerId !== user.id) {
		throw new ForbiddenError("Hanya assigner yang dapat menyelesaikan task");
	}

	if (task.status !== TaskStatus.IN_REVIEW) {
		throw new BadRequestError(
			"Task harus dalam status IN_REVIEW sebelum dapat diselesaikan",
		);
	}

	await db.transaction().execute(async (trx) => {
		await trx
			.updateTable("tasks")
			.set({
				status: payload.action as TaskStatus,
				ratingPoint:
					payload.action === "COMPLETED" ? payload.ratingPoint || null : null,
				finalFeedback:
					payload.action === "COMPLETED" ? payload.feedback || null : null,
			})
			.where("id", "=", task.id as TasksId)
			.executeTakeFirstOrThrow();

		if (payload.action === "COMPLETED") {
			await trx
				.updateTable("taskSubmissions")
				.set({
					status: TaskSubmissionStatus.ACCEPTED,
					reviewedBy: user.id as UsersId,
					reviewDate: new Date().toISOString(),
					feedback: payload.feedback || null,
				})
				.where("taskId", "=", task.id as TasksId)
				.where("status", "=", TaskSubmissionStatus.PENDING_REVIEW)
				.executeTakeFirstOrThrow();
		} else {
			await trx
				.updateTable("taskSubmissions")
				.set({
					status: TaskSubmissionStatus.REJECTED,
					reviewedBy: user.id as UsersId,
					reviewDate: new Date().toISOString(),
					feedback: payload.feedback || null,
				})
				.where("taskId", "=", task.id as TasksId)
				.where("status", "=", TaskSubmissionStatus.PENDING_REVIEW)
				.executeTakeFirstOrThrow();
		}
	});
};
