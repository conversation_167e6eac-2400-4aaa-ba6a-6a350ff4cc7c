import { sql } from "kysely";
import { db } from "@/database/connection";
import type { UsersId } from "@/database/types/public/Users";
import { KpiPeriodType } from "@/shared/enums";

export const _generateCurrentPeriod = (
	periodType: KpiPeriodType,
	providedPeriod?: string,
): string => {
	if (providedPeriod) return providedPeriod;

	const now = new Date();
	if (periodType === KpiPeriodType.YEARLY) {
		return now.getFullYear().toString();
	}
	// Monthly format: YYYY-MM
	return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, "0")}`;
};

export const _getPeriodDateRange = (
	period: string,
	periodType: KpiPeriodType,
): { startDate: Date; endDate: Date } => {
	if (periodType === KpiPeriodType.YEARLY) {
		const year = Number.parseInt(period);
		if (Number.isNaN(year)) {
			const now = new Date();
			return {
				startDate: new Date(now.getFullYear(), 0, 1),
				endDate: new Date(now.getFullYear(), 11, 31, 23, 59, 59),
			};
		}
		return {
			startDate: new Date(year, 0, 1),
			endDate: new Date(year, 11, 31, 23, 59, 59),
		};
	}

	const [yearStr, monthStr] = period.split("-");
	const year = Number(yearStr);
	const month = Number(monthStr);

	if (Number.isNaN(year) || Number.isNaN(month) || month < 1 || month > 12) {
		const now = new Date();
		return {
			startDate: new Date(now.getFullYear(), now.getMonth(), 1),
			endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59),
		};
	}

	const startDate = new Date(year, month - 1, 1);
	const endDate = new Date(year, month, 0, 23, 59, 59);
	return { startDate, endDate };
};

export const _getDirectSubordinates = async (supervisorId: string) => {
	return db
		.selectFrom("userHierarchies")
		.select(["id", "userId", "roleId", "supervisorId"])
		.where("supervisorId", "=", supervisorId as UsersId)
		.execute();
};

export const _getRecursiveSubordinates = async (supervisorId: string) => {
	const result = await sql<{ userId: UsersId }>`
      WITH RECURSIVE subordinates AS (
        SELECT user_id, supervisor_id
        FROM "user_hierarchies" -- <-- Gunakan nama tabel sebenarnya di database
        WHERE supervisor_id = ${supervisorId} AND is_active = true

        UNION ALL

        SELECT uh.user_id, uh.supervisor_id
        FROM "user_hierarchies" uh -- <-- Gunakan nama tabel sebenarnya di database
        INNER JOIN subordinates s ON s.user_id = uh.supervisor_id -- <-- Sesuaikan JOIN KEY
        WHERE uh.is_active = true
      )
      SELECT user_id AS "userId" FROM subordinates -- <-- Alias kolom agar sesuai dengan tipe Kysely Anda
    `.execute(db);

	return result.rows.map((row) => row.userId as UsersId);
};

export const _canAssignTask = async (
	assignerId: string,
	assigneeId: string,
): Promise<boolean> => {
	// User cannot assign task to themselves
	if (assignerId === assigneeId) {
		return false;
	}

	// Get all subordinates of the assigner
	const subordinates = await _getDirectSubordinates(assignerId);

	// Check if assignee is in the subordinates list
	return subordinates.some((sub) => sub.userId === assigneeId);
};

export const _canAssignTaskToMultiple = async (
	assignerId: string,
	assigneeIds: string[],
): Promise<boolean> => {
	// Remove assigner from assignee list if present
	const filteredAssigneeIds = assigneeIds.filter((id) => id !== assignerId);

	if (filteredAssigneeIds.length === 0) {
		return false;
	}

	// Get ONLY direct subordinates (not recursive)
	const directSubordinates = await _getDirectSubordinates(assignerId);
	const directSubordinateIds = directSubordinates.map((sub) => sub.userId);

	// Check if all assignees are direct subordinates
	return filteredAssigneeIds.every((assigneeId) =>
		directSubordinateIds.includes(assigneeId as UsersId),
	);
};
