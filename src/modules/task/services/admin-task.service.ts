import { parse } from "node:querystring";
import dayjs from "dayjs";
import { sql } from "kysely";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { Tasks, TasksId } from "@/database/types/public/Tasks";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
} from "@/shared/dtos";
import {
	TASK_STATUS_LABEL_MAP,
	TaskStatus,
	TaskStatusColors,
} from "@/shared/enums";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import { signPath } from "@/shared/lib/file";
import { PATHS } from "@/shared/lib/paths";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type So<PERSON><PERSON><PERSON>ields,
} from "@/shared/utils";
import { type Task, type TaskQueryDTO, taskQueryDTO } from "../dtos";
import type { AdminCreateTaskDTO, AdminUpdateTaskDTO } from "../dtos/request";
import type { TaskResponseDTO } from "../dtos/response";
import {
	_canAssignTask,
	_canAssignTaskToMultiple,
} from "./shared-task.service";

type DocumentUrl = {
	documents: string[];
	images: string[];
};

const TASK_FILTERABLE_FIELDS: FilterableFields = {
	id: "tasks.id",
	name: "tasks.name",
	deadline: "tasks.deadline",
	ratingPoint: "tasks.ratingPoint",
	status: "tasks.status",
	assigneeName: "assignee.name",
	assigneeEmail: "assignee.email",
	assignerName: "assigner.name",
	assignerEmail: "assigner.email",
	createdAt: "tasks.createdAt",
} as const;

const TASK_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	deadline: "date",
	ratingPoint: "number",
	status: "string",
	assigneeName: "string",
	assigneeEmail: "string",
	assignerName: "string",
	assignerEmail: "string",
	createdAt: "date",
} as const;

const TASK_SORTABLE_FIELDS: SortableFields = {
	id: "tasks.id",
	name: "tasks.name",
	deadline: "tasks.deadline",
	ratingPoint: "tasks.ratingPoint",
	status: "tasks.status",
	assigneeName: "assignee.name",
	assigneeEmail: "assignee.email",
	assignerName: "assigner.name",
	assignerEmail: "assigner.email",
	createdAt: "tasks.createdAt",
} as const;

export const getAllTask = async (
	queryParams: TaskQueryDTO,
): Promise<{ data: TaskResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		name,
		deadline,
		ratingPoint,
		status,
		assigneeName,
		assigneeEmail,
		assignerName,
		assignerEmail,
		createdAt,
	} = queryParams;

	const query = db
		.selectFrom("tasks")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
		.selectAll("tasks")
		.select([
			"assignee.name as assigneeName",
			"assignee.email as assigneeEmail",
			"assigner.name as assignerName",
			"assigner.email as assignerEmail",
		]);

	const countQuery = db
		.selectFrom("tasks")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId");

	// searching
	if (search) {
	}

	// filtering
	const filters: {
		name?: string | string[];
		deadline?: string | string[];
		ratingPoint?: string | string[];
		status?: string | string[];
		assigneeName?: string | string[];
		assigneeEmail?: string | string[];
		assignerName?: string | string[];
		assignerEmail?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (deadline) filters.deadline = deadline;
	if (ratingPoint) filters.ratingPoint = ratingPoint;
	if (status) filters.status = status;
	if (assigneeName) filters.assigneeName = assigneeName;
	if (assigneeEmail) filters.assigneeEmail = assigneeEmail;
	if (assignerName) filters.assignerName = assignerName;
	if (assignerEmail) filters.assignerEmail = assignerEmail;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: TASK_FIELD_TYPES,
			filterableFields: TASK_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: TASK_SORTABLE_FIELDS,
		defaultSort: {
			field: "tasks.createdAt",
			direction: "desc",
		}
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [tasks, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedTasks = tasks.map((task) => {
		const documentUrls = JSON.parse(task.documentUrls) as DocumentUrl;
		const formattedDocumentUrls: DocumentUrl = {
			images: documentUrls.images.map(
				(image) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(image))}`,
			),
			documents: documentUrls.documents.map(
				(document) => `${Env.SERVER_URL}${signPath(PATHS.toUrl(document))}`,
			),
		};

		return {
			...task,
			deadline: dayjs(task.deadline).locale("id").format("DD MMMM YYYY"),
			documentUrls: JSON.stringify(formattedDocumentUrls),
			color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
			displayStatusLabel:
				TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
		};
	});
	return {
		data: formattedTasks,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getTask = async (taskId: string): Promise<Task> => {
	const task = await db
		.selectFrom("tasks")
		.selectAll()
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!task) {
		throw new NotFoundError("Tugas tidak ditemukan");
	}

	return task;
};

export const getUserCanAssignTaskOptions = async (): Promise<
	OptionItemDTO[]
> => {
	const users = await db
		.selectFrom("userHierarchies as uh")
		.innerJoin("users", "users.id", "uh.userId")
		.select(["users.id", "users.name", "users.email"])
		.where("uh.userId", "in", (eb) =>
			eb
				.selectFrom("userHierarchies")
				.select("supervisorId")
				.where("supervisorId", "is not", null)
				.distinct(),
		)
		.execute();

	return users.map((user) => ({
		label: `${user.name} (${user.email})`,
		value: user.id,
	}));
};

export const getUserCanReceiveTaskOptions = async (
	supervisorId: string,
): Promise<OptionItemDTO[]> => {
	const subqueryActiveTaskCount = db
		.selectFrom("tasks")
		.select(["assigneeId", sql<number>`COUNT(*)`.as("activeTaskCount")])
		.where((eb) =>
			eb("status", "not in", [
				TaskStatus.COMPLETED,
				TaskStatus.REJECTED,
				TaskStatus.PENDING,
			]),
		)
		.groupBy("assigneeId")
		.as("activeTasks");

	const users = await db
		.selectFrom("userHierarchies")
		.innerJoin("users", "users.id", "userHierarchies.userId")
		.leftJoin(subqueryActiveTaskCount, "activeTasks.assigneeId", "users.id")
		.select([
			"users.id",
			"users.name",
			"users.email",
			"activeTasks.activeTaskCount",
		])
		.where("userHierarchies.supervisorId", "=", supervisorId as UsersId)
		.execute();

	const formattedUsers = users.map((user) => ({
		label: `${user.name}(${user.email}) | Tugas Aktif: ${user.activeTaskCount ?? 0}`,
		value: user.id,
	}));

	return formattedUsers;
};

export const createTask = async (payload: AdminCreateTaskDTO) => {
	const [existAssigner, existAssignees] = await Promise.all([
		db
			.selectFrom("users")
			.selectAll()
			.where("id", "=", payload.assignerId as UsersId)
			.executeTakeFirst(),
		db
			.selectFrom("users")
			.selectAll()
			.where("id", "in", payload.assigneeIds as UsersId[])
			.execute(),
	]);

	if (!existAssigner) {
		throw new BadRequestError("Assigner yang dipilih tidak ditemukan");
	}

	if (existAssignees.length !== payload.assigneeIds.length) {
		throw new BadRequestError("Assignee yang dipilih tidak ditemukan");
	}

	const canAssign = await _canAssignTaskToMultiple(
		payload.assignerId,
		payload.assigneeIds,
	);

	if (!canAssign) {
		throw new BadRequestError(
			`${existAssigner.name} dapat assign task ke beberapa user yang dipilih berdasarkan hierarki`,
		);
	}

	const results = await db
		.insertInto("tasks")
		.values(
			payload.assigneeIds.map((assigneeId) => ({
				id: nanoid() as TasksId,
				name: payload.name,
				description: payload.description,
				deadline: payload.deadline,
				assignerId: payload.assignerId as UsersId,
				assigneeId: assigneeId as UsersId,
				documentUrls: JSON.stringify({
					documents: payload.documents || [],
					images: payload.images || [],
				}),
				status: TaskStatus.PENDING,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})),
		)
		.returningAll()
		.execute();

	return results.map((task) => ({
		...task,
		deadline: dayjs(task.deadline).locale("id").format("dddd, D MMMM YYYY"),
		assignerName: existAssigner.name,
		assignerEmail: existAssigner.email,
		assigneeName:
			existAssignees.find((assignee) => assignee.id === task.assigneeId)
				?.name || "Unknown",
		assigneeEmail:
			existAssignees.find((assignee) => assignee.id === task.assigneeId)
				?.email || "Unknown",
		color: TaskStatusColors[task.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[task.status as TaskStatus] || "Unknown",
	}));
};

export const updateTask = async (
	taskId: string,
	payload: AdminUpdateTaskDTO,
): Promise<TaskResponseDTO> => {
	const existTask = await db
		.selectFrom("tasks")
		.selectAll()
		.where("id", "=", taskId as TasksId)
		.executeTakeFirst();

	if (!existTask) {
		throw new NotFoundError("Tugas tidak ditemukan");
	}

	const existAssigner = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", (payload.assignerId as UsersId) || existTask.assignerId)
		.executeTakeFirst();

	if (!existAssigner) {
		throw new BadRequestError("Assigner yang dipilih tidak ditemukan");
	}

	const existAssignee = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", (payload.assigneeId as UsersId) || existTask.assigneeId)
		.executeTakeFirst();

	if (!existAssignee) {
		throw new BadRequestError("Assignee yang dipilih tidak ditemukan");
	}

	const canAssign = await _canAssignTask(
		existAssigner.id,
		existTask.assigneeId,
	);

	if (!canAssign) {
		throw new BadRequestError(
			`${existAssigner.name} dapat assign task ke ${existAssignee.name} berdasarkan hierarki`,
		);
	}

	const updateTaskData: Partial<Tasks> = {};
	if (payload.name) updateTaskData.name = payload.name;
	if (payload.description) updateTaskData.description = payload.description;
	if (payload.deadline) updateTaskData.deadline = payload.deadline;
	if (payload.assignerId)
		updateTaskData.assignerId = payload.assignerId as UsersId;
	if (payload.assigneeId)
		updateTaskData.assigneeId = payload.assigneeId as UsersId;

	const documentUrlsObject = JSON.parse(existTask.documentUrls) as DocumentUrl;
	const documentUrls =
		payload.documents && payload.images
			? JSON.stringify({
					documents: payload.documents,
					images: payload.images,
				})
			: payload.documents
				? JSON.stringify({
						documents: payload.documents,
						images: documentUrlsObject.images,
					})
				: payload.images
					? JSON.stringify({
							documents: documentUrlsObject.documents,
							images: payload.images,
						})
					: JSON.stringify(documentUrlsObject);
	if (existTask.documentUrls !== documentUrls) {
		updateTaskData.documentUrls = documentUrls;
	}

	updateTaskData.updatedAt = new Date().toISOString();

	let updatedTask = existTask;
	if (Object.entries(updateTask).length > 1) {
		updatedTask = await db
			.updateTable("tasks")
			.set(updateTaskData)
			.where("id", "=", taskId as TasksId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	return {
		...updatedTask,
		deadline: dayjs(updatedTask.deadline)
			.locale("id")
			.format("dddd, D MMMM YYYY"),
		assignerName: existAssigner.name,
		assignerEmail: existAssigner.email,
		assigneeName: existAssignee.name,
		assigneeEmail: existAssignee.email,
		color: TaskStatusColors[updatedTask.status as TaskStatus] || "#D6D6D6",
		displayStatusLabel:
			TASK_STATUS_LABEL_MAP[updatedTask.status as TaskStatus] || "Unknown",
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("tasks")
		.innerJoin("users as assignee", "assignee.id", "tasks.assigneeId")
		.innerJoin("users as assigner", "assigner.id", "tasks.assignerId")
		.selectAll("tasks")
		.select([
			"assignee.name as assigneeName",
			"assignee.email as assigneeEmail",
			"assigner.name as assignerName",
			"assigner.email as assignerEmail",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const {
			search,
			sort,
			name,
			deadline,
			ratingPoint,
			status,
			assigneeName,
			assigneeEmail,
			assignerName,
			assignerEmail,
			createdAt,
		} = taskQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filtering
		const filters: {
			name?: string | string[];
			deadline?: string | string[];
			ratingPoint?: string | string[];
			status?: string | string[];
			assigneeName?: string | string[];
			assigneeEmail?: string | string[];
			assignerName?: string | string[];
			assignerEmail?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (deadline) filters.deadline = deadline;
		if (ratingPoint) filters.ratingPoint = ratingPoint;
		if (status) filters.status = status;
		if (assigneeName) filters.assigneeName = assigneeName;
		if (assigneeEmail) filters.assigneeEmail = assigneeEmail;
		if (assignerName) filters.assignerName = assignerName;
		if (assignerEmail) filters.assignerEmail = assignerEmail;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: TASK_FILTERABLE_FIELDS,
			fieldTypes: TASK_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: TASK_SORTABLE_FIELDS,
			defaultSort: {
				field: "tasks.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as TasksId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where("tasks.id", "in", selection.selectedIds as TasksId[]);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
