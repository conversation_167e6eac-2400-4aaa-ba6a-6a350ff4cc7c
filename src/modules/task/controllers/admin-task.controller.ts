import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import { BadRequestError } from "@/shared/exceptions";
import type { TaskQueryDTO } from "../dtos";
import type { AdminCreateTaskDTO, AdminUpdateTaskDTO } from "../dtos/request";
import * as adminTaskService from "../services/admin-task.service";

export const getAllTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as TaskQueryDTO;
		const result = await adminTaskService.getAllTask(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const taskId = req.params.taskId as string;
		const result = await adminTaskService.getTask(taskId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getUserCanAssignTaskOptions = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await adminTaskService.getUserCanAssignTaskOptions();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getUserCanReceiveTaskOptions = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const userId = req.query.userId;

		if (!userId || typeof userId !== "string") {
			throw new BadRequestError("User id is required");
		}

		const result = await adminTaskService.getUserCanReceiveTaskOptions(userId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateTaskDTO;
		const result = await adminTaskService.createTask(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const taskId = req.params.taskId as string;
		const validatedBody = req.validatedBody as AdminUpdateTaskDTO;
		const result = await adminTaskService.updateTask(taskId, validatedBody);

		const response = {
			status: "success",
			message: "Berhasil update data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminTaskService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=roles.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
