import type { NextFunction, Request, Response } from "express";
import type { TaskStatus } from "@/shared/enums";
import type { AuthUser } from "@/shared/types";
import type {
	AcceptOrRejectTaskDTO,
	CreateTaskDTO,
	MarkTaskDTO,
	ReAssignTaskDTO,
	SubmitTaskSubmissionDTO,
} from "../dtos/request";
import * as taskService from "../services/task.service";

export const getTasksWithFilter = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const status = req.query.status as TaskStatus;
		const userId = req.query.userId as string;

		const result = await taskService.getTasksWithFilter(req.user as AuthUser, {
			status,
			userId,
		});

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				tasks: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getTaskHome = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const viewerId = req.query?.viewerId as string | undefined;
		const result = await taskService.getTaskHome(
			req.user as AuthUser,
			viewerId,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getMyTaskListPoint = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await taskService.getMyTaskListPoint(req.user as AuthUser);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				tasks: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getTaskDetail = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const taskId = req.params.taskId as string;
		const result = await taskService.getTaskDetail(
			taskId,
			req.user as AuthUser,
		);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAssignableUsers = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await taskService.getAssignableUsers(req.user as AuthUser);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: {
				users: result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as CreateTaskDTO;

		const result = await taskService.createTask(
			req.user as AuthUser,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil membuat tasks baru",
			data: {
				tasks: result,
			},
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const reAssignTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as ReAssignTaskDTO;

		const result = await taskService.reAssignTask({
			user: req.user as AuthUser,
			taskId: req.params.taskId as string,
			payload: validatedBody,
		});

		const response = {
			status: "success",
			message: "Berhasil melukan re-assign terhadap task",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const submitTaskSubmission = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as SubmitTaskSubmissionDTO;

		await taskService.submitTaskSubmission({
			user: req.user as AuthUser,
			taskId: req.params.taskId as string,
			payload: validatedBody,
		});

		const response = {
			status: "success",
			message: "Berhasil submit task submission",
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const acceptOrRejectTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AcceptOrRejectTaskDTO;

		await taskService.acceptOrRejectTask({
			user: req.user as AuthUser,
			taskId: req.params.taskId as string,
			payload: validatedBody,
		});

		const response = {
			status: "success",
			message: `Berhasil melakukan ${validatedBody.action} task`,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const markTask = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as MarkTaskDTO;

		await taskService.markTask({
			user: req.user as AuthUser,
			taskId: req.params.taskId as string,
			payload: validatedBody,
		});

		const response = {
			status: "success",
			message: `Berhasil melakukan mark ${validatedBody.action} pada task`,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
