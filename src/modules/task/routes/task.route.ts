import { Router } from "express";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
} from "@/shared/middlewares";
import * as taskController from "../controllers/task.controller";
import {
	acceptOrRejectTaskDTO,
	createTaskDTO,
	markTaskDTO,
	reAssignTaskDTO,
	submitTaskSubmissionDTO,
} from "../dtos/request";

export const taskRouter = Router();

// GET routes
taskRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	taskController.getTasksWithFilter,
);
taskRouter.get(
	"/home",
	authMiddleware(AuthStrategy.JWT_USER),
	taskController.getTaskHome,
);
taskRouter.get(
	"/assignable-users",
	authMiddleware(AuthStrategy.JWT_USER),
	taskController.getAssignableUsers,
);
taskRouter.get(
	"/points",
	authMiddleware(AuthStrategy.JWT_USER),
	taskController.getMyTaskListPoint,
);
taskRouter.get(
	"/:taskId",
	authMiddleware(AuthStrategy.JWT_USER),
	taskController.getTaskDetail,
);
// POST routes
taskRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.multiple([
		{
			name: "documents",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["*"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-doc",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			name: "images",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-images",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
	]),
	preprocessors.addFilePaths([
		{
			field: "documents",
			bodyKey: "documents",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	preprocessors.addFilePaths([
		{ field: "images", bodyKey: "images", pathProcessor: PATHS.toDatabasePath },
	]),
	preprocessors.normalizeToArrayFields(["documents", "images", "assigneeIds"]),
	validateBody(createTaskDTO),
	taskController.createTask,
);
taskRouter.post(
	"/:taskId/reassign",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.multiple([
		{
			name: "documents",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["*"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-doc",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			name: "images",
			required: false,
			maxCount: 10,
			allowedMimeTypes: [
				"image/jpeg",
				"image/jpg",
				"image/png",
				"image/gif",
				"image/webp",
			],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-images",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
	]),
	preprocessors.addFilePaths([
		{
			field: "documents",
			bodyKey: "documents",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	preprocessors.addFilePaths([
		{ field: "images", bodyKey: "images", pathProcessor: PATHS.toDatabasePath },
	]),
	preprocessors.normalizeToArrayFields(["documents", "images"]),
	validateBody(reAssignTaskDTO),
	taskController.reAssignTask,
);
taskRouter.post(
	"/:taskId/submit",
	authMiddleware(AuthStrategy.JWT_USER),
	localUploadMiddleware.multiple([
		{
			name: "documents",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["*"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-doc",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			name: "images",
			required: false,
			maxCount: 10,
			allowedMimeTypes: [
				"image/jpeg",
				"image/jpg",
				"image/png",
				"image/gif",
				"image/webp",
			],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-images",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
	]),
	preprocessors.addFilePaths([
		{
			field: "documents",
			bodyKey: "documents",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	preprocessors.addFilePaths([
		{ field: "images", bodyKey: "images", pathProcessor: PATHS.toDatabasePath },
	]),
	preprocessors.normalizeToArrayFields(["documents", "images"]),
	validateBody(submitTaskSubmissionDTO),
	taskController.submitTaskSubmission,
);
taskRouter.post(
	"/:taskId/accept-reject",
	authMiddleware(AuthStrategy.JWT_USER),
	validateBody(acceptOrRejectTaskDTO),
	taskController.acceptOrRejectTask,
);
taskRouter.post(
	"/:taskId/review",
	authMiddleware(AuthStrategy.JWT_USER),
	validateBody(markTaskDTO),
	taskController.markTask,
);
