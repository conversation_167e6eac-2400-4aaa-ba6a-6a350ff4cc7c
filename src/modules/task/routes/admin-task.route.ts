import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import { PATHS } from "@/shared/lib/paths";
import {
	authMiddleware,
	localUploadMiddleware,
	preprocessors,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as taskAdminController from "../controllers/admin-task.controller";
import { taskQueryDTO } from "../dtos";
import { adminCreateTaskDTO, adminUpdateTaskDTO } from "../dtos/request";

export const adminTaskRouter = Router();

adminTaskRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(taskQueryDTO),
	taskAdminController.getAllTask,
);

adminTaskRouter.get(
	"/assigner-users",
	taskAdminController.getUserCanAssignTaskOptions,
);

adminTaskRouter.get(
	"/assignable-users",
	taskAdminController.getUserCanReceiveTaskOptions,
);

adminTaskRouter.get(
	"/:taskId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	taskAdminController.getTask,
);

adminTaskRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	localUploadMiddleware.multiple([
		{
			name: "documents",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["*"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-doc",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			name: "images",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-images",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
	]),
	preprocessors.addFilePaths([
		{
			field: "documents",
			bodyKey: "documents",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	preprocessors.addFilePaths([
		{ field: "images", bodyKey: "images", pathProcessor: PATHS.toDatabasePath },
	]),
	preprocessors.normalizeToArrayFields(["documents", "images", "assigneeIds"]),
	validateBody(adminCreateTaskDTO),
	taskAdminController.createTask,
);

adminTaskRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	taskAdminController.bulkAction,
);

adminTaskRouter.put(
	"/:taskId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	localUploadMiddleware.multiple([
		{
			name: "documents",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["*"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-doc",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
		{
			name: "images",
			required: false,
			maxCount: 10,
			allowedMimeTypes: ["image/jpeg", "image/jpg", "image/png", "image/webp"],
			folder: `${PATHS.PRIVATE.UPLOADS}/tasks`,
			userFolder: true,
			fileNamePrefix: "task-images",
			maxFileSize: 10 * 1024 * 1024, // 10MB
		},
	]),
	preprocessors.addFilePaths([
		{
			field: "documents",
			bodyKey: "documents",
			pathProcessor: PATHS.toDatabasePath,
		},
	]),
	preprocessors.addFilePaths([
		{ field: "images", bodyKey: "images", pathProcessor: PATHS.toDatabasePath },
	]),
	preprocessors.normalizeToArrayFields(["documents", "images", "assigneeIds"]),
	validateBody(adminUpdateTaskDTO),
	taskAdminController.updateTask,
);
