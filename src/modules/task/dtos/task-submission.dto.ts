import * as z from "zod";
import { timestampDTO } from "@/shared/dtos";
import { TaskSubmissionStatus } from "@/shared/enums";
import "@/shared/lib/zod-extensions";

export const taskSubmissionDTO = z
	.object({
		id: z.coerce.string().meta({
			example: "hU7dWKoEqJ2RwaOG121FC",
			description: "Unique identifier for this specific submission record",
		}),
		taskId: z.string().meta({
			example: "V07U3oADuyaTmyZ3Mmm9M",
			description: "ID of the task this submission belongs to",
		}),
		userId: z.string().meta({
			example: "5aSgbw0NJTWL1OqarrBn-",
			description: "ID of the user who submitted this submission",
		}),
		reviewedBy: z.string().nullable().meta({
			example: "imnDsBjerxHSocXT6M5tU",
			description: "ID of the user who reviewed this submission",
		}),
		submissionNumber: z.number().meta({
			example: 1,
			description:
				"Sequential number of this submission attempt (e.g., 1st, 2nd, 3rd submission)",
		}),
		documentUrls: z.string().nullable().meta({
			example: "['/uploads/tasks/submission-v1-doc.pdf']",
			description:
				"URLs of documents/files submitted for THIS specific submission attempt",
		}),
		status: z.string().meta({
			example: "PENDING_REVIEW",
			enum: [
				TaskSubmissionStatus.ACCEPTED,
				TaskSubmissionStatus.REJECTED,
				TaskSubmissionStatus.REJECTED,
			],
			description:
				"Status of this specific submission attempt (PENDING_REVIEW, ACCEPTED, REJECTED)",
		}),
		submittedAt: z.iso.datetime().meta({
			example: "2025-01-15T10:30:00Z",
			description: "Timestamp when this submission was made",
		}),
		reviewDate: z.iso.datetime().nullable().meta({
			example: "2025-01-16T14:00:00Z",
			description: "Timestamp when this submission was reviewed",
		}),
		feedback: z.string().nullable().meta({
			example: "Overall good effort, but need to fix formatting on page 5.",
			description:
				"General feedback from the supervisor for this submission attempt.",
		}),
		notes: z.string().nullable().meta({
			example: "There is some notes for the supervisor",
			description: "Notes provided by the assignee for the supervisor",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("TaskSubmissionDTO");
