import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const markTaskDTO = z.object({
	action: z.enum(["COMPLETED", "REVISION_REQUIRED"]).meta({
		description: "Action to perform (COMPLETED or REVISION_REQUIRED)",
		example: "COMPLETED",
	}),
	ratingPoint: z.number().min(0).max(100).optional().meta({
		description: "Task rating score (0-100)",
		example: 85,
	}),
	feedback: z.string().optional().meta({
		description:
			"General feedback from the supervisor for the task/submission, if action is completed, will be saved as finalFeedback",
		example: "Overall good effort, but need to fix formatting on page 5.",
	}),
});
export type MarkTaskDTO = z.infer<typeof markTaskDTO>;
