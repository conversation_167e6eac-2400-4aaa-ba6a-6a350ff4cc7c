import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const submitTaskSubmissionDTO = z.object({
	documents: z.optional(z.array(z.string())).meta({
		description: "Array of task document URLs with string format",
		example: ["/uploads/tasks/task-doc-123.pdf"],
	}),
	images: z.optional(z.array(z.string())).meta({
		description: "Array of task image URLs with string format",
		example: ["/uploads/tasks/task-image-123.jpg"],
	}),
	notes: z.string().optional().meta({
		description: "Notes provided by the assignee for the supervisor",
		example: "There is some notes for the supervisor",
	}),
});
export type SubmitTaskSubmissionDTO = z.infer<typeof submitTaskSubmissionDTO>;
