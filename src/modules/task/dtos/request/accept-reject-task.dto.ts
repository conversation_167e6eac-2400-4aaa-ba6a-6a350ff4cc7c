import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const acceptOrRejectTaskDTO = z.object({
	action: z
		.enum(["ACCEPT", "REJECT"], {
			message: "Action harus berupa salah satu dari: ACCEPT, REJECT",
		})
		.meta({
			description: "Action to perform (ACCEPT or REJECT)",
			example: "ACCEPT",
		}),
	rejectionReason: z.string().optional().meta({
		description: "Reason for rejection (required if action is REJECT)",
		example: "Does not meet quality standards",
	}),
});
export type AcceptOrRejectTaskDTO = z.infer<typeof acceptOrRejectTaskDTO>;
