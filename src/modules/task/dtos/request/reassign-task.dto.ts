import * as z from "zod";
import "@/shared/lib/zod-extensions";

const baseTaskSchema = z.object({
	name: z.string().nonempty().meta({
		description: "Task name/title",
		example: "Complete monthly report",
	}),
	description: z.string().nonempty().meta({
		description: "Task description/details",
		example: "Prepare and submit the monthly sales report for Q1",
	}),
	deadline: z.iso.date().meta({
		description: "Task deadline in YYYY-MM-DD format",
		example: "2025-02-15",
	}),
	assigneeIds: z
		.array(z.string())
		.min(1)
		.meta({
			description: "Array of user IDs to assign the task to",
			example: ["user1", "user2"],
		}),
	documents: z.optional(z.array(z.string())).meta({
		description: "Array of task document URLs with string format",
		example: ["/uploads/tasks/task-doc-123.pdf"],
	}),
	images: z.optional(z.array(z.string())).meta({
		description: "Array of task image URLs with string format",
		example: ["/uploads/tasks/task-image-123.jpg"],
	}),
});

export const reAssignTaskDTO = baseTaskSchema
	.omit({ assigneeIds: true })
	.partial()
	.extend({
		assigneeId: z.string().meta({
			description: "ID of the user to reassign the task to",
			example: "Kqk9o9PyOOnjvTD45BXYB",
		}),
	});
export type ReAssignTaskDTO = z.infer<typeof reAssignTaskDTO>;
