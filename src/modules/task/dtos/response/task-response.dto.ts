import * as z from "zod";
import { taskDTO } from "../task.dto";

export const taskResponseDTO = taskDTO
	.extend({
		assignerName: z.string().meta({
			example: "Hoon",
		}),
		assignerEmail: z.string().meta({
			example: "<EMAIL>",
		}),
		assigneeName: z.string().meta({
			example: "John doe",
		}),
		assigneeEmail: z.string().meta({
			example: "<EMAIL>",
		}),
		color: z.string().meta({
			example: "#D6D6D6",
		}),
		displayStatusLabel: z.string().meta({
			enum: [
				"Tugas Baru",
				"<PERSON><PERSON><PERSON><PERSON>",
				"<PERSON><PERSON> Penge<PERSON>kan",
				"<PERSON><PERSON> Revisi",
				"<PERSON><PERSON><PERSON>",
				"<PERSON><PERSON><PERSON>",
			],
			example: "Selesai",
		}),
	})
	.openapi("TaskResponseDTO");
export type TaskResponseDTO = z.infer<typeof taskResponseDTO>;
