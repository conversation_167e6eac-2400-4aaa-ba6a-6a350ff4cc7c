import * as z from "zod";

export const assignableUsersResponseDTO = z.array(
	z.object({
		id: z.string().meta({
			example: "_1RPSu7WmsO5ckxDh2YK6",
			description: "Unique identity of user",
		}),
		name: z.string().meta({
			example: "<PERSON>",
			description: "User name",
		}),
		email: z.string().meta({
			example: "<EMAIL>",
			description: "User email",
		}),
		activeTask: z.number().meta({
			example: 4,
			description: "Active task for the user",
		}),
	}),
);
export type AssignableUsersResponseDTO = z.infer<
	typeof assignableUsersResponseDTO
>;
