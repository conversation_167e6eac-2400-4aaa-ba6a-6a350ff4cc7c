import * as z from "zod";
import { taskDTO } from "../task.dto";
import { taskSubmissionDTO } from "../task-submission.dto";

export const taskDetailResponseDTO = taskDTO.extend({
	assignerName: z.string().meta({
		example: "Hoon",
	}),
	assignerEmail: z.string().meta({
		example: "<EMAIL>",
	}),
	assigneeName: z.string().meta({
		example: "John doe",
	}),
	assigneeEmail: z.string().meta({
		example: "<EMAIL>",
	}),
	color: z.string().meta({
		example: "#D6D6D6",
	}),
	displayStatusLabel: z.string().meta({
		enum: [
			"Tugas Baru",
			"Dikerjakan",
			"Dalam Pengecekan",
			"Perlu Revisi",
			"Selesai",
			"Ditolak",
		],
		example: "Perlu Persetujuan Anda",
	}),
	submissions: z.array(
		taskSubmissionDTO.omit({ createdAt: true, updatedAt: true }),
	),
	uiActions: z.array(z.string()).meta({
		example: ["acceptTask, rejectTask"],
		description: `Possible value: ["acceptTask", "rejectTask"] | ["submitTask"] | ["markAsComplete", "markNeedRevision"]`,
	}),
});
export type TaskDetailResponseDTO = z.infer<typeof taskDetailResponseDTO>;
