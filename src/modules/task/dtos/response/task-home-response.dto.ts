import * as z from "zod";
import { taskDTO } from "../task.dto";

export const taskHomeResponseDTO = z.object({
	isViewingOtherUser: z.boolean().meta({
		example: false,
	}),
	viewedUserName: z.string().nullable().meta({
		example: "John Cena",
		description: "Will null if isViewingOtherUser is false",
	}),

	showSelectSubordinateOption: z.boolean().meta({
		example: true,
		description:
			"State for show/hide the select component for viewing other user",
	}),
	showAddTaskButton: z.boolean().meta({
		example: true,
		description: "State for show/hide add task button",
	}),
	showSubordinateTaskListSection: z.boolean().meta({
		example: true,
		description: "State for show/hide subordinate task list for review section",
	}),

	subordinateList: z
		.array(
			z.object({
				id: z.string().meta({
					example: "iOBn-Zez5pwumx_jgVDv3",
					description: "Unique identity of user",
				}),
				name: z.string().meta({
					example: "<PERSON> doe",
					description: "user name",
				}),
				email: z.string().meta({
					example: "<EMAIL>",
					description: "user email",
				}),
				isCurrentUser: z.boolean().meta({
					example: true,
					description: "Indicate this user is current logged-in user",
				}),
				isCurrentView: z.boolean().meta({
					example: true,
					description: "Indicate this current viewing for user",
				}),
			}),
		)
		.nullable(),
	subordinateTasks: z
		.array(
			taskDTO.extend({
				assignerName: z.string().meta({
					example: "Hoon",
				}),
				assignerEmail: z.string().meta({
					example: "<EMAIL>",
				}),
				assigneeName: z.string().meta({
					example: "John doe",
				}),
				assigneeEmail: z.string().meta({
					example: "<EMAIL>",
				}),
			}),
		)
		.nullable(),
	taskStats: z.object({
		PENDING: z.number().meta({
			example: 0,
			description: "Total task with pending status",
		}),
		IN_PROGRESS: z.number().meta({
			example: 0,
			description: "Total task with in_progress status",
		}),
		IN_REVIEW: z.number().meta({
			example: 0,
			description: "Total task with in_review status",
		}),
		REVISION_REQUIRED: z.number().meta({
			example: 0,
			description: "Total task with revision_required status",
		}),
		COMPLETED: z.number().meta({
			example: 0,
			description: "Total task with completed status",
		}),
		REJECTED: z.number().meta({
			example: 0,
			description: "Total task with rejected status",
		}),
	}),
	myTaskTabs: z.array(
		z.object({
			tabId: z.string().meta({
				example: "HBaLfiQwz1",
			}),
			tabTitle: z.string().meta({
				example: "Selesai",
			}),
			tabSource: z.string().meta({
				example: "/api/v1/tasks?status=COMPLETED",
			}),
			color: z.string().meta({
				example: "#000000",
			}),
			count: z.number().meta({
				example: 5,
			}),
			isDefault: z.boolean().meta({
				example: false,
			}),
			filters: z.object({
				status: z.string().meta({
					example: "COMPLETED",
				}),
			}),
		}),
	),
});
export type TaskHomeResponseDTO = z.infer<typeof taskHomeResponseDTO>;
