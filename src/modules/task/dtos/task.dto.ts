import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import { TaskStatus } from "@/shared/enums";
import "@/shared/lib/zod-extensions";
import { registry } from "@/shared/docs/openapi-registry";

export const taskDTO = z
	.object({
		id: z.coerce.string().meta({
			example: "xEHrmF2czmf7joyscp5J2",
			description: "Unique identifier for the task",
		}),
		name: z.string().meta({
			example: "Task 1",
			description: "Task name/title",
		}),
		assignerId: z.string().meta({
			example: "bXn1LnLm8GNqn2OKSE4db",
			description: "ID of user who assigned the task",
		}),
		assigneeId: z.string().meta({
			example: "zKcA_9qLYn3JVufR4Vg_x",
			description: "ID of user who is assigned the task",
		}),
		deadline: z.iso.datetime().meta({
			example: "2025-02-15",
			description: "Task deadline (DATEONLY format)",
		}),
		description: z.string().meta({
			example: "Prepare and submit the monthly sales report for Q1",
			description: "Task description/details",
		}),
		documentUrls: z.string().nullable().meta({
			example: "['/uploads/tasks/task-doc-123.pdf']",
			description: "Array of task document URLs with string format",
		}),
		ratingPoint: z.number().min(0).max(100).nullable().meta({
			example: 85,
			description: "Task rating score (0-100)",
		}),
		rejectionReason: z.string().nullable().meta({
			example: "There is some reason",
			description: "Reason provided by the supervisor for rejecting the task",
		}),
		finalFeedback: z.string().nullable().meta({
			example: "Overall good effort, but need to fix formatting on page 5.",
			description: "General feedback from the supervisor for the task",
		}),
		status: z.string().meta({
			enum: [
				TaskStatus.COMPLETED,
				TaskStatus.REJECTED,
				TaskStatus.PENDING,
				TaskStatus.IN_PROGRESS,
				TaskStatus.IN_REVIEW,
				TaskStatus.REVISION_REQUIRED,
			],
			example: TaskStatus.PENDING,
			description:
				"Overall task status (PENDING, IN_PROGRESS, IN_REVIEW, REVISION_REQUIRED, COMPLETED, REJECTED)",
		}),
	})
	.extend({ ...timestampDTO.shape });
export type Task = z.infer<typeof taskDTO>;

registry.register("Task", taskDTO);

export const taskQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	deadline: filterFieldDTO.optional(),
	ratingPoint: filterFieldDTO.optional(),
	status: filterFieldDTO.optional(),
	assigneeName: filterFieldDTO.optional(),
	assigneeEmail: filterFieldDTO.optional(),
	assignerName: filterFieldDTO.optional(),
	assignerEmail: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type TaskQueryDTO = z.infer<typeof taskQueryDTO>;
