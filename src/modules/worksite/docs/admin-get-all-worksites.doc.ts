import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { worksiteResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Worksite [Admin]",
	method: "get",
	path: "/api/v1/admin/worksites",
	tags: ["worksite"],
	description:
		"Get list of work sites with pagination and filtering (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(worksiteResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
