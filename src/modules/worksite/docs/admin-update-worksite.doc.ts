import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateWorksiteDTO } from "../dtos/request";
import { worksiteResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Worksite [Admin]",
	method: "put",
	path: "/api/v1/admin/worksites/{worksiteId}",
	tags: ["worksite"],
	description: "Update worksite for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			worksiteId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateWorksiteDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: worksiteResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
