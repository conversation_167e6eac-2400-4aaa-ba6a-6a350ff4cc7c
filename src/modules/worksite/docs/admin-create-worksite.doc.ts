import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateWorksiteDTO } from "../dtos/request";
import { worksiteResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Worksite [Admin]",
	method: "post",
	path: "/api/v1/admin/worksites",
	tags: ["worksite"],
	description: "Create a new worksite with attendance-rule data for admin",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminCreateWorksiteDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: worksiteResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
