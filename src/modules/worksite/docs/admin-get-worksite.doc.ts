import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { worksiteDTO } from "../dtos";

registry.registerPath({
	summary: "Get Worksite [Admin]",
	method: "get",
	path: "/api/v1/admin/worksites/{worksiteId}",
	tags: ["worksite"],
	description: "Get single worksite for admin",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			worksiteId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: worksiteDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
