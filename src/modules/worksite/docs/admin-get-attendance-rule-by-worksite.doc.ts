import * as z from "zod";
import { attendanceRuleDTO } from "@/modules/attendance/dtos";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get Attendance Rule By Worksite [Admin]",
	method: "get",
	path: "/api/v1/admin/worksites/{worksiteId}/attendance-rule",
	tags: ["worksite"],
	description: "Get attendance rule detail by id",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			worksiteId: z.string(),
		}),
	},
	parameters: [],
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: attendanceRuleDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
