import { parse } from "node:querystring";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AttendanceRulesId } from "@/database/types/public/AttendanceRules";
import type { Worksites, WorksitesId } from "@/database/types/public/Worksites";
import type { AttendanceRuleDTO } from "@/modules/attendance/dtos";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
} from "@/shared/dtos";
import { NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import {
	type WorksiteDTO,
	type WorksiteQueryDTO,
	worksiteQueryDTO,
} from "../dtos";
import type {
	AdminCreateWorksiteDTO,
	AdminUpdateWorksiteDTO,
} from "../dtos/request";
import type { WorksiteResponseDTO } from "../dtos/response";

const WORKSITE_FILTERABLE_FIELDS: FilterableFields = {
	id: "worksites.id",
	name: "worksites.name",
	createdAt: "worksites.createdAt",
} as const;

const WORKSITE_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	createdAt: "date",
} as const;

const WORKSITE_SORTABLE_FIELDS: SortableFields = {
	id: "worksites.id",
	name: "worksites.name",
	createdAt: "worksites.createdAt",
} as const;

export const getAllWorksite = async (
	queryParams: WorksiteQueryDTO,
): Promise<{ data: WorksiteResponseDTO[]; meta: ResponseMetaDTO }> => {
	const { pageIndex, pageSize, search, sort, name, createdAt } = queryParams;

	const query = db.selectFrom("worksites").selectAll();

	const countQuery = db.selectFrom("worksites");

	// searching
	if (search) {
	}

	// filtering
	const filters: { name?: string | string[]; createdAt?: string | string[] } =
		{};
	if (name) filters.name = name;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			fieldTypes: WORKSITE_FIELD_TYPES,
			filterableFields: WORKSITE_FILTERABLE_FIELDS,
		},
	);

	// sorting
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: WORKSITE_SORTABLE_FIELDS,
		defaultSort: {
			field: "worksites.createdAt",
			direction: "desc",
		},
	});

	// pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [worksites, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	return {
		data: worksites,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getWorksite = async (worksiteId: string): Promise<WorksiteDTO> => {
	const worksite = await db
		.selectFrom("worksites")
		.selectAll()
		.where("id", "=", worksiteId as WorksitesId)
		.executeTakeFirst();

	if (!worksite) {
		throw new NotFoundError("Lokasi kerja tidak ditemukan");
	}

	return worksite;
};

export const getWorksiteOptions = async (): Promise<OptionItemDTO[]> => {
	const worksites = await db
		.selectFrom("worksites")
		.select(["id as value", "name as label"])
		.execute();

	return worksites;
};

export const getAttendanceRuleByWorksite = async (
	worksiteId: string,
): Promise<AttendanceRuleDTO> => {
	const attendanceRule = await db
		.selectFrom("attendanceRules")
		.selectAll()
		.where("worksiteId", "=", worksiteId as WorksitesId)
		.executeTakeFirst();

	if (!attendanceRule) {
		throw new NotFoundError("Konfigurasi attendance rule tidak ditemukan");
	}

	return attendanceRule;
};

export const createWorksite = async (
	payload: AdminCreateWorksiteDTO,
): Promise<WorksiteResponseDTO> => {
	const { name, address, type, description, ...attendanceRulePayload } =
		payload;

	const result = await db.transaction().execute(async (trx) => {
		const newWorksite = await trx
			.insertInto("worksites")
			.values({
				id: nanoid() as WorksitesId,
				name,
				address,
				type,
				description,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.returningAll()
			.executeTakeFirstOrThrow();

		await trx
			.insertInto("attendanceRules")
			.values({
				id: nanoid() as AttendanceRulesId,
				worksiteId: newWorksite.id as WorksitesId,
				...attendanceRulePayload,
				checkInEndTime: "23:59:59",
				breakEndTime: "23:59:59",
				returnEndTime: "23:59:59",
				checkOutEndTime: "23:59:59",
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();

		return newWorksite;
	});

	return result;
};

export const updateWorksite = async (
	worksiteId: string,
	payload: AdminUpdateWorksiteDTO,
): Promise<WorksiteResponseDTO> => {
	const existWorksite = await getWorksite(worksiteId);

	const updateWorksiteData: Partial<Worksites> = {};

	if (payload.name && payload.name !== existWorksite.name) {
		updateWorksiteData.name = payload.name;
	}
	if (payload.address && payload.address !== existWorksite.address) {
		updateWorksiteData.address = payload.address;
	}
	if (payload.type && payload.type !== existWorksite.type) {
		updateWorksiteData.type = payload.type;
	}
	if (
		payload.description &&
		payload.description !== existWorksite.description
	) {
		updateWorksiteData.description = payload.description;
	}

	updateWorksiteData.updatedAt = new Date().toISOString();

	let updatedWorksite = existWorksite;
	if (Object.keys(updateWorksiteData).length > 1) {
		updatedWorksite = await db
			.updateTable("worksites")
			.set(updateWorksiteData)
			.where("id", "=", worksiteId as WorksitesId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	return updatedWorksite;
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db.selectFrom("worksites").selectAll();

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, createdAt } = worksiteQueryDTO.parse(queryObj);

		// search
		if (search) {
		}

		// filters
		const filters: {
			name?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: WORKSITE_FILTERABLE_FIELDS,
			fieldTypes: WORKSITE_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: WORKSITE_SORTABLE_FIELDS,
			defaultSort: {
				field: "worksites.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as WorksitesId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"worksites.id",
			"in",
			selection.selectedIds as WorksitesId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
