import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminWorksiteController from "../controllers/worksite.admin.controller";
import { worksiteQueryDTO } from "../dtos";
import {
	adminCreateWorksiteDTO,
	adminUpdateWorksiteDTO,
} from "../dtos/request";

export const adminWorksiteRouter = Router();

adminWorksiteRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(worksiteQueryDTO),
	adminWorksiteController.getAllWorksite,
);

adminWorksiteRouter.get("/options", adminWorksiteController.getWorksiteOptions);

adminWorksiteRouter.get(
	"/:worksiteId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminWorksiteController.getWorksite,
);

adminWorksiteRouter.get(
	"/:worksiteId/attendance-rule",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminWorksiteController.getAttendanceRuleByWorksite,
);

adminWorksiteRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateWorksiteDTO),
	adminWorksiteController.createWorksite,
);

adminWorksiteRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminWorksiteController.bulkAction,
);

adminWorksiteRouter.post(
	"/:worksiteId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateWorksiteDTO),
	adminWorksiteController.updateWorksite,
);
