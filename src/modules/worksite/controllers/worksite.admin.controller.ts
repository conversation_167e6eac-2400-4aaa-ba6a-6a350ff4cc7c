import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { WorksiteQueryDTO } from "../dtos";
import type {
	AdminCreateWorksiteDTO,
	AdminUpdateWorksiteDTO,
} from "../dtos/request";
import * as adminWorksiteService from "../services/admin-worksite.service";

export const getAllWorksite = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as WorksiteQueryDTO;

		const result = await adminWorksiteService.getAllWorksite(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result.data,
			meta: result.meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getWorksite = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const worksiteId = req.params.worksiteId as string;

		const result = await adminWorksiteService.getWorksite(worksiteId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getWorksiteOptions = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await adminWorksiteService.getWorksiteOptions();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAttendanceRuleByWorksite = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const worksiteId = req.params.worksiteId as string;
		const result =
			await adminWorksiteService.getAttendanceRuleByWorksite(worksiteId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data.",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createWorksite = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateWorksiteDTO;
		const result = await adminWorksiteService.createWorksite(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateWorksite = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const worksiteId = req.params.worksiteId as string;
		const validatedBody = req.validatedBody as AdminUpdateWorksiteDTO;
		const result = await adminWorksiteService.updateWorksite(
			worksiteId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil membuat data baru",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await adminWorksiteService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=worksites.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
