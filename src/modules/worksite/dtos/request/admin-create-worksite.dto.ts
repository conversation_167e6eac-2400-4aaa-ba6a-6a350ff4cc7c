import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import * as z from "zod";
import "@/shared/lib/zod-extensions";

dayjs.extend(customParseFormat);

// const TIME_FORMAT = "HH:mm:ss";
const INDONESIA_TIMEZONES = [
	"Asia/Jakarta",
	"Asia/Pontianak",
	"Asia/Makassar",
	"Asia/Jayapura",
];

export const adminCreateWorksiteDTO = z.object({
	// data for worsite, in update only use this
	name: z.string().nonempty().meta({
		description: "Name of the work site",
		example: "Jakarta Office",
	}),
	type: z.string().nonempty().meta({
		description: "Type of work site",
		example: "Office",
	}),
	address: z.string().meta({
		description: "Full address of the work site",
		example: "Jl. Sudirman No. 123, Jakarta Pusat",
	}),
	description: z.string().nonempty().optional().meta({
		description: "Optional description of the work site",
		example: "Main office building",
	}),

	// data for attendance rule
	latitude: z.number().min(-90).max(90).meta({
		description: "Latitude coordinate of the work site",
		example: -6.2088,
	}),
	longitude: z.number().min(-180).max(180).meta({
		description: "Longitude coordinate of the work site",
		example: 106.8456,
	}),
	radiusInMeter: z.number().min(10).meta({
		description: "Allowed radius in meters for attendance check-in",
		example: 100,
	}),
	timezone: z.enum(INDONESIA_TIMEZONES).meta({
		enum: INDONESIA_TIMEZONES,
		description: "Timezone for the work site",
		example: "Asia/Jakarta",
	}),
	checkInStartTime: z.iso.time().meta({
		example: "06:00:00",
		description: "Start time for check-in with format HH:mm:ss",
	}),
	checkInToleranceMinutes: z.number().min(0).meta({
		example: 30,
		description: "Tolerance in minutes for check-in with",
	}),

	checkOutStartTime: z.iso.time().meta({
		example: "17:00:00",
		description: "Start time for check-out with format HH:mm:ss",
	}),
	checkOutToleranceMinutes: z.number().min(0).meta({
		example: 30,
		description: "Tolerance in minutes for check-out",
	}),

	breakStartTime: z.iso.time().meta({
		example: "12:00:00",
		description: "Start time for break with format HH:mm:ss",
	}),
	breakToleranceMinutes: z.number().min(0).meta({
		example: 30,
		description: "Tolerance in minutes for break",
	}),

	returnStartTime: z.iso.time().meta({
		example: "13:00:00",
		description: "Start time for return with format HH:mm:ss",
	}),
	returnToleranceMinutes: z.number().min(0).meta({
		example: 30,
		description: "Tolerance in minutes for return",
	}),
});
// .refine(
// 	(data) => {
// 		return dayjs(data.checkInStartTime, TIME_FORMAT).isBefore(
// 			dayjs(data.checkInEndTime, TIME_FORMAT),
// 		);
// 	},

// 	{
// 		message: "Jam mulai check-in harus sebelum jam selesai check-in",
// 		path: ["checkInStartTime"],
// 	},
// )
// .refine(
// 	(data) =>
// 		dayjs(data.checkOutStartTime, TIME_FORMAT).isBefore(
// 			dayjs(data.checkOutEndTime, TIME_FORMAT),
// 		),
// 	{
// 		message: "Jam mulai check-out harus sebelum jam selesai check-out",
// 		path: ["checkOutStartTime"],
// 	},
// )
// .refine(
// 	(data) =>
// 		dayjs(data.breakStartTime, TIME_FORMAT).isBefore(
// 			dayjs(data.breakEndTime, TIME_FORMAT),
// 		),
// 	{
// 		message: "Jam mulai istirahat harus sebelum jam selesai istirahat",
// 		path: ["breakStartTime"],
// 	},
// )
// .refine(
// 	(data) =>
// 		dayjs(data.returnStartTime, TIME_FORMAT).isBefore(
// 			dayjs(data.returnEndTime, TIME_FORMAT),
// 		),
// 	{
// 		message: "Jam mulai kembali harus sebelum jam selesai kembali",
// 		path: ["returnStartTime"],
// 	},
// );
export type AdminCreateWorksiteDTO = z.infer<typeof adminCreateWorksiteDTO>;
