import z from "zod";

export const adminUpdateWorksiteDTO = z
	.object({
		name: z.string().nonempty().meta({
			description: "Name of the work site",
			example: "Jakarta Office",
		}),
		type: z.string().nonempty().meta({
			description: "Type of work site",
			example: "Office",
		}),
		address: z.string().meta({
			description: "Full address of the work site",
			example: "Jl. Sudirman No. 123, Jakarta Pusat",
		}),
		description: z.string().nonempty().optional().meta({
			description: "Optional description of the work site",
			example: "Main office building",
		}),
	})
	.partial();
export type AdminUpdateWorksiteDTO = z.infer<typeof adminUpdateWorksiteDTO>;
