import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const worksiteDTO = z
	.object({
		id: z.string().meta({
			description: "Work site ID",
			example: "Qah4cccEK0MsspUJ9ywAy",
		}),
		name: z.string().meta({
			description: "Name of the work site",
			example: "Jakarta Office",
		}),
		type: z.string().meta({
			description: "Type of work site",
			example: "Office",
		}),
		address: z.string().meta({
			description: "Full address of the work site",
			example: "Jl. Sudirman No. 123, Jakarta Pusat",
		}),
		description: z.string().nullable().meta({
			description: "Optional description of the work site",
			example: "Main office building",
		}),
		createdAt: z.string().meta({
			description: "Creation timestamp",
			example: "2025-01-15T08:30:15.000Z",
		}),
		updatedAt: z.string().meta({
			description: "Last update timestamp",
			example: "2025-01-15T08:30:15.000Z",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("Worksite");
export type WorksiteDTO = z.infer<typeof worksiteDTO>;

export const worksiteQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type WorksiteQueryDTO = z.infer<typeof worksiteQueryDTO>;
