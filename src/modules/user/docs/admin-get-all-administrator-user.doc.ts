import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { administratorUserQueryDTO } from "../dtos";
import { administratorUserResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All Administrator User [Admin]",
	method: "get",
	path: "/api/v1/admin/administrator-users",
	tags: ["user"],
	description:
		"Get list of administrator user with paginating, filtering, sorting, and searching",
	security: [{ bearerAuth: [] }],
	request: {
		query: administratorUserQueryDTO,
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(administratorUserResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
