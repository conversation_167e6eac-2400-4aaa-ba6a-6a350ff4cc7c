import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateAdministratorUserDTO } from "../dtos/request";
import { administratorUserResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update Administrator User [Admin]",
	method: "put",
	path: "/api/v1/admin/administrator-users/{administratorUserId}",
	tags: ["user"],
	description: "Update administrator user (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			administratorUserId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateAdministratorUserDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: administratorUserResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
