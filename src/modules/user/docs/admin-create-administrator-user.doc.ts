import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminCreateAdministratorUserDTO } from "../dtos/request";
import { administratorUserResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Create New Administrator User [Admin]",
	method: "post",
	path: "/api/v1/admin/administrator-users",
	tags: ["user"],
	description: "Create a new administrator user account (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminCreateAdministratorUserDTO,
				},
			},
		},
	},
	responses: {
		201: {
			description: "created",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: administratorUserResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
