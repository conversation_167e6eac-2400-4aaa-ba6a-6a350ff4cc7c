import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { userDTO } from "../dtos";

registry.registerPath({
	summary: "Get User [Admin]",
	method: "get",
	path: "/api/v1/admin/users/{userId}",
	tags: ["user"],
	description: "Get single user",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			userId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: userDTO.extend({
							roleId: z.string(),
							supervisorId: z.string().nullable(),
							worksiteId: z.string(),
						}),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
