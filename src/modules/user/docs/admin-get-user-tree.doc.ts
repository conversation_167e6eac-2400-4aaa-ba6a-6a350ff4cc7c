import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { getTreeNodeResponseDTO } from "@/shared/dtos";

registry.registerPath({
	summary: "Get User Tree [Admin]",
	method: "get",
	path: "/api/v1/admin/users/tree",
	tags: ["user"],
	description: "Get user tree nodes",
	security: [{ bearerAuth: [] }],
	request: {},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: getTreeNodeResponseDTO,
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
