import z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { basePaginationResponseDTO } from "@/shared/dtos";
import { userQueryDTO } from "../dtos";
import { userResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get All User [Admin]",
	method: "get",
	path: "/api/v1/admin/users",
	tags: ["user"],
	description:
		"Get list of user with paginating, filtering, sorting, and searching",
	security: [{ bearerAuth: [] }],
	request: {
		query: userQueryDTO,
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: basePaginationResponseDTO.extend({
						data: z.array(userResponseDTO),
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
