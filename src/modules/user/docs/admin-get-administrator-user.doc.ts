import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { administratorUserResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Get Administrator User [Admin]",
	method: "get",
	path: "/api/v1/admin/administrator-users/{administratorUserId}",
	tags: ["user"],
	description: "Get single administrator user",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			administratorUserId: z.string(),
		}),
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: administratorUserResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
