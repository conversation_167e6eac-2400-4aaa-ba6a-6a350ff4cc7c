import * as z from "zod";
import { errorResponses } from "@/shared/docs/components/responses";
import { registry } from "@/shared/docs/openapi-registry";
import { baseResponseDTO } from "@/shared/dtos";
import { adminUpdateUserDTO } from "../dtos/request";
import { userResponseDTO } from "../dtos/response";

registry.registerPath({
	summary: "Update User [Admin]",
	method: "put",
	path: "/api/v1/admin/users/{userId}",
	tags: ["user"],
	description: "Update user (Admin only)",
	security: [{ bearerAuth: [] }],
	request: {
		params: z.object({
			userId: z.string(),
		}),
		body: {
			description: "payload",
			content: {
				"application/json": {
					schema: adminUpdateUserDTO,
				},
			},
		},
	},
	responses: {
		200: {
			description: "success",
			content: {
				"application/json": {
					schema: baseResponseDTO.extend({
						data: userResponseDTO,
					}),
				},
			},
		},
		400: errorResponses.BadRequest,
		401: errorResponses.Unauthorized,
		403: errorResponses.Forbidden,
		422: errorResponses.UnProcessableEntity,
		429: errorResponses.TooManyRequests,
		500: errorResponses.InternalServerError,
	},
});
