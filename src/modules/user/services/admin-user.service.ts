import { parse } from "node:querystring";
import bcrypt from "bcryptjs";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type { AccountsId } from "@/database/types/public/Accounts";
import type { KpiRecordsId } from "@/database/types/public/KpiRecords";
import type {
	LeaveUsages,
	LeaveUsagesId,
} from "@/database/types/public/LeaveUsages";
import type { RolesId } from "@/database/types/public/Roles";
import type {
	UserHierarchies,
	UserHierarchiesId,
} from "@/database/types/public/UserHierarchies";
import type { Users, UsersId } from "@/database/types/public/Users";
import type { WorksitesId } from "@/database/types/public/Worksites";
import { Env } from "@/shared/config/env.config";
import type {
	BulkActionDTO,
	OptionItemDTO,
	ResponseMetaDTO,
	TreeNodeDTO,
} from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import { PATHS } from "@/shared/lib/paths";
import {
	applyFilters,
	applySorting,
	type FieldTypes,
	type FilterableFields,
	type SortableFields,
} from "@/shared/utils";
import { type UserDTO, type UserQueryDTO, userQueryDTO } from "../dtos";
import type { AdminCreateUserDTO, AdminUpdateUserDTO } from "../dtos/request";
import type { UserResponseDTO } from "../dtos/response";

const USER_FILTERABLE_FIELDS: FilterableFields = {
	id: "users.id",
	name: "users.name",
	email: "users.email",
	createdAt: "users.createdAt",
	roleName: "roles.name",
	supervisorName: "supervisor.name",
} as const;

const USER_FIELD_TYPES: FieldTypes = {
	id: "string",
	name: "string",
	email: "string",
	createdAt: "date",
	roleName: "string",
	supervisorName: "string",
} as const;

const USER_SORTABLE_FIELDS: SortableFields = {
	id: "users.id",
	name: "users.name",
	email: "users.email",
	createdAt: "users.createdAt",
	updatedAt: "users.updatedAt",
	roleName: "roles.name",
	supervisorName: "supervisor.name",
} as const;

export const getAllUser = async (
	queryParams: UserQueryDTO,
): Promise<{ data: UserResponseDTO[]; meta: ResponseMetaDTO }> => {
	const {
		pageIndex,
		pageSize,
		search,
		sort,
		name,
		email,
		roleName,
		supervisorName,
		createdAt,
	} = queryParams;

	let query = db
		.selectFrom("users")
		.innerJoin("usersRoles", "usersRoles.userId", "users.id")
		.innerJoin("roles", "roles.id", "usersRoles.roleId")
		.innerJoin("usersWorksites", "usersWorksites.userId", "users.id")
		.innerJoin("worksites", "worksites.id", "usersWorksites.worksiteId")
		.innerJoin("userHierarchies", "userHierarchies.userId", "users.id")
		.leftJoin(
			"users as supervisor",
			"supervisor.id",
			"userHierarchies.supervisorId",
		)
		.selectAll("users")
		.select(["roles.id as roleId", "roles.name as roleName"])
		.select(["worksites.id as worksiteId", "worksites.name as worksiteName"])
		.select([
			"userHierarchies.supervisorId",
			"supervisor.name as supervisorName",
			"supervisor.email as supervisorEmail",
		]);

	let countQuery = db
		.selectFrom("users")
		.innerJoin("usersRoles", "usersRoles.userId", "users.id")
		.innerJoin("roles", "roles.id", "usersRoles.roleId")
		.innerJoin("usersWorksites", "usersWorksites.userId", "users.id")
		.innerJoin("worksites", "worksites.id", "usersWorksites.worksiteId")
		.innerJoin("userHierarchies", "userHierarchies.userId", "users.id")
		.leftJoin(
			"users as supervisor",
			"supervisor.id",
			"userHierarchies.supervisorId",
		);

	// search
	if (search) {
		query = query.where((eb) =>
			eb.or([
				eb("users.name", "ilike", `%${search}%`),
				eb("users.email", "ilike", `%${search}%`),
			]),
		);
		countQuery = countQuery.where((eb) =>
			eb.or([
				eb("users.name", "ilike", `%${search}%`),
				eb("users.email", "ilike", `%${search}%`),
			]),
		);
	}

	// filters
	const filters: {
		name?: string | string[];
		email?: string | string[];
		roleName?: string | string[];
		supervisorName?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (email) filters.email = email;
	if (roleName) filters.roleName = roleName;
	if (supervisorName) filters.supervisorName = supervisorName;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			filterableFields: USER_FILTERABLE_FIELDS,
			fieldTypes: USER_FIELD_TYPES,
		},
	);

	// Apply sorting menggunakan generic function
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: USER_SORTABLE_FIELDS,
		defaultSort: {
			field: "users.createdAt",
			direction: "desc",
		},
	});

	// Pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [users, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedUsers = users.map((user) => ({
		...user,
		image: `${Env.SERVER_URL}${PATHS.toUrl(user.image)}`,
	}));

	return {
		data: formattedUsers,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getUserTree = async (): Promise<TreeNodeDTO[]> => {
	const raw = await db
		.selectFrom("userHierarchies")
		.where("userHierarchies.isActive", "=", true)
		.innerJoin("users", "users.id", "userHierarchies.userId")
		.innerJoin("roles", "roles.id", "userHierarchies.roleId")
		.select([
			"users.id as userId",
			"users.name as name",
			"users.email as email",
			"roles.name as roleName",
			"userHierarchies.supervisorId",
		])
		.execute();

	const nodesMap: Record<string, TreeNodeDTO & { userId: string }> = {};

	for (const row of raw) {
		nodesMap[row.userId] = {
			userId: row.userId,
			name: row.name,
			attributes: {
				email: row.email,
				roleName: row.roleName,
			},
			children: [],
		};
	}

	const roots: (TreeNodeDTO & { userId: string })[] = [];

	for (const row of raw) {
		const current = nodesMap[row.userId] as TreeNodeDTO & { userId: string };
		if (row.supervisorId && nodesMap[row.supervisorId]) {
			nodesMap?.[row.supervisorId]?.children?.push(current);
		} else {
			roots.push(current);
		}
	}

	// Remove internal userId
	const stripId = (node: TreeNodeDTO & { userId?: string }): TreeNodeDTO =>
		({
			name: node.name,
			attributes: node.attributes,
			children: (node.children as (TreeNodeDTO & { userId?: string })[])?.map(
				stripId,
			),
		}) as TreeNodeDTO;

	return roots.map(stripId);
};

export const getUser = async (
	userId: UsersId,
): Promise<
	UserDTO & { roleId: string; supervisorId: string | null; worksiteId: string }
> => {
	const existUser = await db
		.selectFrom("users")
		.innerJoin("usersRoles", "usersRoles.userId", "users.id")
		.innerJoin("userHierarchies", "userHierarchies.userId", "users.id")
		.innerJoin("usersWorksites", "usersWorksites.userId", "users.id")
		.selectAll("users")
		.select([
			"usersRoles.roleId",
			"userHierarchies.supervisorId",
			"usersWorksites.worksiteId",
		])
		.where("users.id", "=", userId)
		.executeTakeFirst();

	if (!existUser) {
		throw new NotFoundError("User tidak ditemukan");
	}

	return existUser;
};

export const getUserOptions = async (): Promise<OptionItemDTO[]> => {
	const users = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.execute();

	const options = users.map((user) => ({
		label: `${user.name} - ${user.email}`,
		value: user.id,
	}));

	return options;
};

export const createUser = async (
	payload: AdminCreateUserDTO,
): Promise<UserResponseDTO> => {
	const existUserCredential = await db
		.selectFrom("users")
		.select(["id"])
		.where((eb) =>
			eb.or([
				eb("email", "=", payload.email.toLowerCase()),
				eb("nik", "=", payload.nik.toLocaleLowerCase()),
				eb("mobileNumber", "=", payload.mobileNumber),
			]),
		)
		.executeTakeFirst();

	if (existUserCredential) {
		throw new BadRequestError(
			"Crendential sudah digunakan pengguna lain. Gunakan email, nomer hp dan NIK yang belum terdaftar di sistem.",
		);
	}

	const [existRole, existSupervisor, existWorksite] = await Promise.all([
		db
			.selectFrom("roles")
			.select(["id", "name"])
			.where("id", "=", payload.roleId as RolesId)
			.executeTakeFirst(),
		payload.supervisorId
			? db
					.selectFrom("users")
					.select(["id", "name", "email"])
					.where("id", "=", payload.supervisorId as UsersId)
					.executeTakeFirst()
			: Promise.resolve(null),
		db
			.selectFrom("worksites")
			.select(["id", "name"])
			.where("id", "=", payload.worksiteId as WorksitesId)
			.executeTakeFirst(),
	]);

	if (!existRole) {
		throw new BadRequestError("Jabatan tidak ditemukan");
	}

	if (!existWorksite) {
		throw new BadRequestError("Lokasi kerja tidak ditemukan");
	}

	if (payload.supervisorId && !existSupervisor) {
		throw new BadRequestError("Supervisor tidak ditemukan");
	}

	const result = await db.transaction().execute(async (trx) => {
		const newUser = await trx
			.insertInto("users")
			.values({
				id: nanoid() as UsersId,
				nik: payload.nik,
				mobileNumber: payload.mobileNumber,
				name: payload.name,
				email: payload.email.toLowerCase(),
				emailVerified: true,
				image: Env.DEFAULT_USER_IMAGE,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.returningAll()
			.executeTakeFirstOrThrow();

		await trx
			.insertInto("accounts")
			.values({
				id: nanoid() as AccountsId,
				userId: newUser.id,
				accountId: newUser.id,
				providerId: "credential",
				password: bcrypt.hashSync(
					payload.password || Env.DEFAULT_USER_PASSWORD,
				),
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();

		await trx
			.insertInto("usersRoles")
			.values({
				roleId: payload.roleId as RolesId,
				userId: newUser.id,
			})
			.executeTakeFirstOrThrow();

		const activeLeavePolicies = await trx
			.selectFrom("leavePolicies")
			.selectAll()
			.execute();

		const leaveUsagesPayload: LeaveUsages[] = activeLeavePolicies.map(
			(policy) => ({
				id: nanoid() as LeaveUsagesId,
				userId: newUser.id,
				leavePolicyId: policy.id,
				used: 0,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			}),
		);

		await trx.insertInto("leaveUsages").values(leaveUsagesPayload).execute();

		await trx
			.insertInto("userHierarchies")
			.values({
				id: nanoid() as UserHierarchiesId,
				userId: newUser.id,
				roleId: payload.roleId as RolesId,
				supervisorId: existSupervisor ? existSupervisor.id : null,
				isActive: true,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();

		await trx
			.insertInto("usersWorksites")
			.values({
				userId: newUser.id,
				worksiteId: payload.worksiteId as WorksitesId,
			})
			.executeTakeFirstOrThrow();

		await trx
			.insertInto("kpiRecords")
			.values({
				id: nanoid() as KpiRecordsId,
				userId: newUser.id as UsersId,
				period: new Date().getFullYear().toString(),
				taskAverageScore: 0,
				attendancePercentage: 0,
				violationAverageScore: 0,
				finalKpiScore: 0,
				totalTasks: 0,
				totalWorkDays: 0,
				totalAttendanceDays: 0,
				totalViolations: 0,
				createdAt: new Date().toISOString(),
				updatedAt: new Date().toISOString(),
			})
			.executeTakeFirstOrThrow();

		return {
			...newUser,
			image: `${Env.SERVER_URL}${PATHS.toUrl(newUser.image)}`,
			roleId: existRole.id,
			roleName: existRole.name,
			supervisorId: existSupervisor?.id || null,
			supervisorName: existSupervisor?.name || null,
			supervisorEmail: existSupervisor?.email || null,
			worksiteId: existWorksite.id,
			worksiteName: existWorksite.name,
		};
	});

	return result;
};

export const updateUser = async (
	userId: UsersId,
	payload: AdminUpdateUserDTO,
): Promise<UserResponseDTO> => {
	const existingUser = await db
		.selectFrom("users")
		.selectAll()
		.where("id", "=", userId)
		.executeTakeFirst();

	if (!existingUser) {
		throw new BadRequestError("User tidak ditemukan");
	}

	// validate credential conflicts
	if (payload.email || payload.nik || payload.mobileNumber) {
		const conflictConditions = [];

		if (payload.email) {
			conflictConditions.push(
				db
					.selectFrom("users")
					.select(["id"])
					.where("email", "=", payload.email.toLowerCase())
					.where("id", "!=", userId),
			);
		}

		if (payload.nik) {
			conflictConditions.push(
				db
					.selectFrom("users")
					.select(["id"])
					.where("nik", "=", payload.nik.toLowerCase())
					.where("id", "!=", userId),
			);
		}

		if (payload.mobileNumber) {
			conflictConditions.push(
				db
					.selectFrom("users")
					.select(["id"])
					.where("mobileNumber", "=", payload.mobileNumber)
					.where("id", "!=", userId),
			);
		}

		const conflicts = await Promise.all(
			conflictConditions.map((condition) => condition.execute()),
		);
		const hasConflict = conflicts.some((result) => result?.length > 0);

		if (hasConflict) {
			throw new BadRequestError(
				"Credential sudah digunakan pengguna lain. Gunakan email, nomer hp dan NIK yang belum terdaftar di sistem.",
			);
		}
	}

	// Validate role dan supervisor if exist
	let existRole = null;
	let existSupervisor = null;
	let existWorksite = null;

	if (payload.roleId) {
		existRole = await db
			.selectFrom("roles")
			.select(["id", "name"])
			.where("id", "=", payload.roleId as RolesId)
			.executeTakeFirst();

		if (!existRole) {
			throw new BadRequestError("Role tidak ditemukan");
		}
	}

	if (payload.supervisorId) {
		existSupervisor = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.supervisorId as UsersId)
			.executeTakeFirst();

		if (!existSupervisor) {
			throw new BadRequestError("Supervisor tidak ditemukan");
		}
	}

	if (payload.worksiteId) {
		existWorksite = await db
			.selectFrom("worksites")
			.select(["id", "name"])
			.where("id", "=", payload.worksiteId as WorksitesId)
			.executeTakeFirst();

		if (!existWorksite) {
			throw new BadRequestError("Lokasi kerja tidak ditemukan");
		}
	}

	const result = await db.transaction().execute(async (trx) => {
		// Update users table
		const userUpdateData: Partial<Users> = {};

		if (payload.name) userUpdateData.name = payload.name;
		if (payload.email) userUpdateData.email = payload.email.toLowerCase();
		if (payload.nik) userUpdateData.nik = payload.nik;
		if (payload.mobileNumber)
			userUpdateData.mobileNumber = payload.mobileNumber;

		userUpdateData.updatedAt = new Date().toISOString();

		let updatedUser = existingUser;
		if (Object.keys(userUpdateData).length > 1) {
			updatedUser = await trx
				.updateTable("users")
				.set(userUpdateData)
				.where("id", "=", userId)
				.returningAll()
				.executeTakeFirstOrThrow();
		}

		// Update password if exist
		if (payload.password) {
			await trx
				.updateTable("accounts")
				.set({
					password: bcrypt.hashSync(payload.password),
					updatedAt: new Date().toISOString(),
				})
				.where("userId", "=", userId)
				.where("providerId", "=", "credential")
				.execute();
		}

		// Update role if changed
		if (payload.roleId) {
			await trx
				.updateTable("usersRoles")
				.set({ roleId: payload.roleId as RolesId })
				.where("userId", "=", userId)
				.execute();
		}

		// Update user hierarchy jika ada perubahan role atau supervisor
		if (payload.roleId || payload.supervisorId !== undefined) {
			const hierarchyUpdateData: Partial<UserHierarchies> = {};

			if (payload.roleId) {
				hierarchyUpdateData.roleId = payload.roleId as RolesId;
			}

			if (payload.supervisorId !== undefined) {
				hierarchyUpdateData.supervisorId = payload.supervisorId
					? (payload.supervisorId as UsersId)
					: null;
			}

			hierarchyUpdateData.updatedAt = new Date().toISOString();

			await trx
				.updateTable("userHierarchies")
				.set(hierarchyUpdateData)
				.where("userId", "=", userId)
				.where("isActive", "=", true)
				.execute();
		}

		if (payload.worksiteId) {
			await trx
				.updateTable("usersWorksites")
				.set({ worksiteId: payload.worksiteId as WorksitesId })
				.where("userId", "=", userId)
				.execute();
		}

		const role =
			existRole ||
			(await trx
				.selectFrom("roles")
				.innerJoin("usersRoles", "roles.id", "usersRoles.roleId")
				.select(["roles.name", "roles.id"])
				.where("usersRoles.userId", "=", userId)
				.executeTakeFirstOrThrow());

		const supervisor =
			existSupervisor ||
			(await trx
				.selectFrom("users")
				.innerJoin(
					"userHierarchies",
					"users.id",
					"userHierarchies.supervisorId",
				)
				.select(["users.id", "users.name", "users.email"])
				.where("userHierarchies.userId", "=", userId)
				.where("userHierarchies.isActive", "=", true)
				.executeTakeFirst());

		const worksite =
			existWorksite ||
			(await trx
				.selectFrom("worksites")
				.innerJoin(
					"usersWorksites",
					"worksites.id",
					"usersWorksites.worksiteId",
				)
				.select(["worksites.id", "worksites.name"])
				.where("usersWorksites.userId", "=", userId)
				.executeTakeFirstOrThrow());

		return {
			...updatedUser,
			image: `${Env.SERVER_URL}${PATHS.toUrl(updatedUser.image)}`,
			roleId: role.id,
			roleName: role.name,
			supervisorId: supervisor?.id || null,
			supervisorName: supervisor?.name || null,
			supervisorEmail: supervisor?.email || null,
			worksiteId: worksite.id,
			worksiteName: worksite.name,
		};
	});

	return result;
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("users")
		.innerJoin("usersRoles", "usersRoles.userId", "users.id")
		.innerJoin("roles", "roles.id", "usersRoles.roleId")
		.innerJoin("usersWorksites", "usersWorksites.userId", "users.id")
		.innerJoin("worksites", "worksites.id", "usersWorksites.worksiteId")
		.innerJoin("userHierarchies", "userHierarchies.userId", "users.id")
		.leftJoin(
			"users as supervisor",
			"supervisor.id",
			"userHierarchies.supervisorId",
		)
		.selectAll("users")
		.select(["roles.id as roleId", "roles.name as roleName"])
		.select(["worksites.id as worksiteId", "worksites.name as worksiteName"])
		.select([
			"userHierarchies.supervisorId",
			"supervisor.name as supervisorName",
			"supervisor.email as supervisorEmail",
		]);

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, email, createdAt } =
			userQueryDTO.parse(queryObj);

		// search
		if (search) {
			query = query.where((eb) =>
				eb.or([
					eb("users.name", "ilike", `%${search}%`),
					eb("users.email", "ilike", `%${search}%`),
				]),
			);
		}

		// filters
		const filters: {
			name?: string | string[];
			email?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (email) filters.email = email;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: USER_FILTERABLE_FIELDS,
			fieldTypes: USER_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: USER_SORTABLE_FIELDS,
			defaultSort: {
				field: "users.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as UsersId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const users = await sortedQuery.execute();

			const csv = await createCsv(users);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where("users.id", "in", selection.selectedIds as UsersId[]);
		const users = await query.execute();

		if (action === "export") {
			const csv = await createCsv(users);

			return csv;
		}
	}

	return null;
};
