import { parse } from "node:querystring";
import bcrypt from "bcryptjs";
import { nanoid } from "nanoid";
import { db } from "@/database/connection";
import type {
	AdministratorUsers,
	AdministratorUsersId,
} from "@/database/types/public/AdministratorUsers";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import type { BulkActionDTO, ResponseMetaDTO } from "@/shared/dtos";
import { BadRequestError, NotFoundError } from "@/shared/exceptions";
import { createCsv } from "@/shared/lib/csv";
import { PATHS } from "@/shared/lib/paths";
import { applyFilters, applySorting } from "@/shared/utils";
import {
	type AdministratorUserQueryDTO,
	administratorUserQueryDTO,
} from "../dtos";
import type {
	AdminCreateAdministratorUserDTO,
	AdminUpdateAdministratorUserDTO,
} from "../dtos/request";
import type { AdministratorUserResponseDTO } from "../dtos/response";

const ADMIN_USER_FILTERABLE_FIELDS = {
	id: "administratorUsers.id",
	name: "administratorUsers.name",
	email: "administratorUsers.email",
	createdAt: "administratorUsers.createdAt",
	userName: "users.name",
} as const;

const ADMIN_USER_FIELD_TYPES = {
	id: "string",
	name: "string",
	email: "string",
	createdAt: "date",
	userName: "string",
} as const;

const ADMIN_USER_SORTABLE_FIELDS = {
	id: "administratorUsers.id",
	name: "administratorUsers.name",
	email: "administratorUsers.email",
	createdAt: "administratorUsers.createdAt",
	userName: "users.name",
} as const;

export const getAllAdministratorUsers = async (
	queryParams: AdministratorUserQueryDTO,
): Promise<{ data: AdministratorUserResponseDTO[]; meta: ResponseMetaDTO }> => {
	const { pageIndex, pageSize, search, sort, name, email, createdAt } =
		queryParams;

	let query = db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId")
		.selectAll("administratorUsers")
		.select(["users.name as userName", "users.email as userEmail"]);

	let countQuery = db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId");

	// search
	if (search) {
		query = query.where((eb) =>
			eb.or([
				eb("administratorUsers.name", "ilike", `%${search}%`),
				eb("administratorUsers.email", "ilike", `%${search}%`),
			]),
		);
		countQuery = countQuery.where((eb) =>
			eb.or([
				eb("administratorUsers.name", "ilike", `%${search}%`),
				eb("administratorUsers.email", "ilike", `%${search}%`),
			]),
		);
	}

	// filters
	const filters: {
		name?: string | string[];
		email?: string | string[];
		createdAt?: string | string[];
	} = {};
	if (name) filters.name = name;
	if (email) filters.email = email;
	if (createdAt) filters.createdAt = createdAt;

	const { query: filteredQuery, countQuery: filteredCountQuery } = applyFilters(
		{
			query,
			countQuery,
			filters,
			filterableFields: ADMIN_USER_FILTERABLE_FIELDS,
			fieldTypes: ADMIN_USER_FIELD_TYPES,
		},
	);

	// Apply sorting menggunakan generic function
	const sortedQuery = applySorting({
		query: filteredQuery,
		sort,
		sortableFields: ADMIN_USER_SORTABLE_FIELDS,
		defaultSort: {
			field: "administratorUsers.createdAt",
			direction: "desc",
		},
	});

	// Pagination
	const paged = sortedQuery
		.limit(pageSize)
		.offset(pageIndex * pageSize) as typeof query;

	const [administratorUsers, { totalItems }] = await Promise.all([
		paged.execute(),
		(filteredCountQuery as typeof countQuery)
			.select((eb) => eb.fn.countAll<number>().as("totalItems"))
			.executeTakeFirstOrThrow(),
	]);

	const totalPages = Math.ceil(totalItems / pageSize);

	const formattedAdministratorUsers = administratorUsers.map(
		({ password, ...adminUser }) => ({
			...adminUser,
			image: `${Env.SERVER_URL}${PATHS.toUrl(adminUser.image)}`,
		}),
	);

	return {
		data: formattedAdministratorUsers,
		meta: {
			pagination: {
				pageIndex,
				pageSize,
				totalItems: Number(totalItems),
				totalPages,
				hasNextPage: pageIndex < totalPages - 1,
				hasPreviousPage: pageIndex > 0,
			},
		},
	};
};

export const getAdministratorUser = async (
	administratorId: string,
): Promise<AdministratorUserResponseDTO> => {
	const existAdministratorUser = await db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId")
		.selectAll("administratorUsers")
		.select(["users.name as userName", "users.email as userEmail"])
		.where(
			"administratorUsers.id",
			"=",
			administratorId as AdministratorUsersId,
		)
		.executeTakeFirst();

	if (!existAdministratorUser) {
		throw new NotFoundError("Administrator User tidak ditemukan");
	}

	const { password: _, ...adminUser } = existAdministratorUser;

	return {
		...adminUser,
		image: `${Env.SERVER_URL}${PATHS.toUrl(adminUser.image)}`,
	};
};

export const createAdministratorUser = async (
	payload: AdminCreateAdministratorUserDTO,
): Promise<AdministratorUserResponseDTO> => {
	const existUser = await db
		.selectFrom("users")
		.select(["id", "name", "email"])
		.where("id", "=", payload.userId as UsersId)
		.executeTakeFirst();

	if (!existUser) {
		throw new BadRequestError(
			"User yang dipilih untuk membuat akun administrator tidak ditemukan",
		);
	}

	const newAdminUser = await db
		.insertInto("administratorUsers")
		.values({
			id: nanoid() as AdministratorUsersId,
			name: payload.name,
			email: payload.email,
			userId: existUser.id,
			password: bcrypt.hashSync(payload.password || Env.DEFAULT_USER_PASSWORD),
			emailVerified: true,
			image: Env.DEFAULT_USER_IMAGE,
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		})
		.returning([
			"id",
			"name",
			"email",
			"userId",
			"emailVerified",
			"image",
			"createdAt",
			"updatedAt",
		])
		.executeTakeFirstOrThrow();

	return {
		...newAdminUser,
		image: `${Env.SERVER_URL}${PATHS.toUrl(newAdminUser.image)}`,
		userName: existUser.name || "",
		userEmail: existUser.email || "",
	};
};

export const updateAdministratorUser = async (
	administratorUserId: string,
	payload: AdminUpdateAdministratorUserDTO,
): Promise<AdministratorUserResponseDTO> => {
	const existAdministratorUser = await db
		.selectFrom("administratorUsers")
		.selectAll()
		.where("id", "=", administratorUserId as AdministratorUsersId)
		.executeTakeFirst();

	if (!existAdministratorUser) {
		throw new BadRequestError("Administrator User tidak ditemukan");
	}

	// validate credential conflicts
	if (payload.email) {
		const conflictConditions = [];

		if (payload.email) {
			conflictConditions.push(
				db
					.selectFrom("administratorUsers")
					.select(["id"])
					.where("email", "=", payload.email.toLowerCase())
					.where("id", "!=", administratorUserId as AdministratorUsersId),
			);
		}

		const conflicts = await Promise.all(
			conflictConditions.map((condition) => condition.execute()),
		);
		const hasConflict = conflicts.some((result) => result?.length > 0);

		if (hasConflict) {
			throw new BadRequestError(
				"Credential sudah digunakan pengguna lain. Gunakan email yang belum terdaftar di sistem.",
			);
		}
	}

	let existUser = null;

	if (payload.userId) {
		existUser = await db
			.selectFrom("users")
			.select(["id", "name", "email"])
			.where("id", "=", payload.userId as UsersId)
			.executeTakeFirst();

		if (!existUser) {
			throw new BadRequestError("User yang dipilih tidak ditemukan");
		}
	}

	// Update users table
	const userAdminUpdateData: Partial<AdministratorUsers> = {};

	if (payload.name) userAdminUpdateData.name = payload.name;
	if (payload.email) userAdminUpdateData.email = payload.email.toLowerCase();
	if (payload.password)
		userAdminUpdateData.password = bcrypt.hashSync(payload.password);
	if (payload.userId) userAdminUpdateData.userId = payload.userId as UsersId;

	userAdminUpdateData.updatedAt = new Date().toISOString();

	let updatedAdminUser = existAdministratorUser;
	if (Object.keys(userAdminUpdateData).length > 1) {
		updatedAdminUser = await db
			.updateTable("administratorUsers")
			.set(userAdminUpdateData)
			.where("id", "=", administratorUserId as AdministratorUsersId)
			.returningAll()
			.executeTakeFirstOrThrow();
	}

	const finalUser =
		existUser ||
		(await db
			.selectFrom("administratorUsers")
			.innerJoin("users", "users.id", "administratorUsers.userId")
			.select(["users.id", "users.name", "users.email"])
			.where(
				"administratorUsers.id",
				"=",
				administratorUserId as AdministratorUsersId,
			)
			.executeTakeFirst());

	return {
		...updatedAdminUser,
		image: `${Env.SERVER_URL}${PATHS.toUrl(updatedAdminUser.image)}`,
		userName: finalUser?.name || "",
		userEmail: finalUser?.email || "",
	};
};

export const bulkAction = async (payload: BulkActionDTO) => {
	const { action, queryString, selection } = payload;
	let query = db
		.selectFrom("administratorUsers")
		.innerJoin("users", "users.id", "administratorUsers.userId")
		.selectAll("administratorUsers")
		.select("users.name as userName");

	if (selection.isSelectAll) {
		const queryObj = parse(queryString as string);

		const { search, sort, name, email, createdAt } =
			administratorUserQueryDTO.parse(queryObj);

		// search
		if (search) {
			query = query.where((eb) =>
				eb.or([
					eb("administratorUsers.name", "ilike", `%${search}%`),
					eb("administratorUsers.email", "ilike", `%${search}%`),
				]),
			);
		}

		// filters
		const filters: {
			name?: string | string[];
			email?: string | string[];
			createdAt?: string | string[];
		} = {};
		if (name) filters.name = name;
		if (email) filters.email = email;
		if (createdAt) filters.createdAt = createdAt;

		const { query: filteredQuery } = applyFilters({
			query,
			filters,
			filterableFields: ADMIN_USER_FILTERABLE_FIELDS,
			fieldTypes: ADMIN_USER_FIELD_TYPES,
		});

		// Apply sorting menggunakan generic function
		let sortedQuery = applySorting({
			query: filteredQuery,
			sort,
			sortableFields: ADMIN_USER_SORTABLE_FIELDS,
			defaultSort: {
				field: "administratorUsers.createdAt",
				direction: "desc",
			},
		});

		// Apply exclusions
		if (selection.excludedIds?.length) {
			sortedQuery = sortedQuery.where(
				"id",
				"not in",
				selection.excludedIds as UsersId[],
			);
		}

		// Execute bulk action
		if (action === "export") {
			const rows = await sortedQuery.execute();

			const csv = await createCsv(rows);

			return csv;
		}

		return null;
	}

	if (selection?.selectedIds?.length) {
		query = query.where(
			"administratorUsers.id",
			"in",
			selection.selectedIds as AdministratorUsersId[],
		);
		const rows = await query.execute();

		if (action === "export") {
			const csv = await createCsv(rows);

			return csv;
		}
	}

	return null;
};
