import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";
import { registry } from "@/shared/docs/openapi-registry";

export const administratorUserDTO = z
	.object({
		id: z.string().meta({
			example: "8xYW-nl9KZ_bgf0QiiUtL",
			description: "Unique identifier for the admin user",
		}),
		name: z.string().meta({
			example: "IT Admin",
			description: "Name of the admin user",
		}),
		email: z.string().meta({
			example: "<EMAIL>",
			description: "Username of administrator user",
		}),
		password: z.string().meta({
			example: "Password1",
			description: "Password",
		}),
		image: z.string().meta({
			example: "/uploads/avatars/user-avatar-123.jpg",
			description: "User profile image URL",
		}),
		emailVerified: z.boolean().meta({
			example: true,
			description: "Whether email is verified",
		}),
		userId: z.string().meta({
			example: "JwYEtjGl2A9xWaqRxjTXf",
			description:
				"Id of the primary account (to link the admin account to an actual individual).",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("AdministratorUser");

registry.register("AdministratorUser", administratorUserDTO);

export const administratorUserQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	email: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type AdministratorUserQueryDTO = z.infer<
	typeof administratorUserQueryDTO
>;
