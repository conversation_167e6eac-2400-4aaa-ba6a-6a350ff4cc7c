import * as z from "zod";

export const userWorksiteDTO = z
	.object({
		userId: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "User ID assigned to work site",
		}),
		worksiteId: z.string().meta({
			example: "ws_Qah4cccEK0MsspUJ9ywAy",
			description: "Work site ID",
		}),
		isMain: z.boolean().meta({
			example: true,
			description: "Whether this is the main work site for the user",
		}),
	})
	.openapi("UserWorkSite");
