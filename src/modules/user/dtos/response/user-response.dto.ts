import z from "zod";
import { userDTO } from "../user.dto";
import "@/shared/lib/zod-extensions";

export const userResponseDTO = userDTO
	.extend({
		roleId: z.string(),
		roleName: z.string(),
		supervisorId: z.string().nullable(),
		supervisorName: z.string().nullable(),
		supervisorEmail: z.string().nullable(),
		worksiteId: z.string(),
		worksiteName: z.string(),
	})
	.openapi("UserResponseDTO");
export type UserResponseDTO = z.infer<typeof userResponseDTO>;
