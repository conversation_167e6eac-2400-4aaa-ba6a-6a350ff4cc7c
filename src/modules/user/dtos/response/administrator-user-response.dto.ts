import z from "zod";
import { administratorUserDTO } from "../administrator-user.dto";
import "@/shared/lib/zod-extensions";

export const administratorUserResponseDTO = administratorUserDTO
	.omit({ password: true })
	.extend({
		userName: z.string(),
		userEmail: z.string(),
	})
	.openapi("AdministratorUserResponseDTO");
export type AdministratorUserResponseDTO = z.infer<
	typeof administratorUserResponseDTO
>;
