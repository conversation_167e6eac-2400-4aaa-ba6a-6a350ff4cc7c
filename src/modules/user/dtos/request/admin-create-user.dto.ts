import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const adminCreateUserDTO = z.object({
	roleId: z.string().nonempty().meta({
		description: "Role ID assigned to user",
		example: "dJYbsuY0iO52io5rQoBrE",
	}),
	supervisorId: z.string().nonempty().nullable().meta({
		description: "Supervisor ID if roles has list of supervisors",
		example: "14piZkkeYzqskZvP_skSJ",
	}),
	name: z.string().nonempty().meta({
		description: "Full name of the user",
		example: "<PERSON>",
	}),
	email: z.email({ message: "Format email tidak valid" }).meta({
		description: "Email address of the user",
		example: "<EMAIL>",
	}),
	nik: z.string().nonempty().meta({
		description: "National Identity Number (NIK)",
		example: "1234567890123456",
	}),
	mobileNumber: z.string().nonempty().meta({
		description: "Mobile phone number",
		example: "08123456789",
	}),
	password: z
		.optional(
			z.string().min(6).meta({
				description: "User password (optional for use default password)",
				example: "securePassword123",
			}),
		)
		.meta({
			description: "Optional password field",
		}),
	worksiteId: z.string().nonempty().meta({
		description: "Worksite ID assigned to user",
		example: "dJYbsuY0iO52io5rQoBrE",
	}),
});
export type AdminCreateUserDTO = z.infer<typeof adminCreateUserDTO>;
