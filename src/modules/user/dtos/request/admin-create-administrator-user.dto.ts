import * as z from "zod";
import "@/shared/lib/zod-extensions";

export const adminCreateAdministratorUserDTO = z.object({
	userId: z.string().nonempty().meta({
		example: "HpIYxfeqOdO0lVqr8S9dZ",
		description: "Id user related to this account",
	}),
	name: z.string().nonempty().meta({
		description: "Full name of the user",
		example: "John Doe",
	}),
	email: z.email().meta({
		description: "Email address of the user",
		example: "<EMAIL>",
	}),
	password: z
		.optional(
			z.string().meta({
				description: "User password (optional for use default password)",
				example: "securePassword123",
			}),
		)
		.meta({
			description: "Optional password field",
		}),
});
export type AdminCreateAdministratorUserDTO = z.infer<
	typeof adminCreateAdministratorUserDTO
>;
