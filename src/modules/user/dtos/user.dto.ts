import * as z from "zod";
import { filterFieldDTO, tableQueryDTO, timestampDTO } from "@/shared/dtos";
import "@/shared/lib/zod-extensions";

export const userDTO = z
	.object({
		id: z.string().meta({
			example: "user_bXn1LnLm8GNqn2OKSE4db",
			description: "Unique identifier for the user",
		}),
		name: z.string().meta({
			example: "John Doe",
			description: "Full name of the user",
		}),
		email: z.email().meta({
			example: "<EMAIL>",
			description: "Email address of the user",
		}),
		nik: z.string().meta({
			example: "EMP001",
			description: "Employee ID/NIK",
		}),
		mobileNumber: z.string().meta({
			example: "08123456789",
			description: "Mobile phone number",
		}),
		emailVerified: z.boolean().meta({
			example: true,
			description: "Whether email is verified",
		}),
		image: z.string().meta({
			example: "/uploads/avatars/user-avatar-123.jpg",
			description: "User profile image URL",
		}),
	})
	.extend({ ...timestampDTO.shape })
	.openapi("User");
export type UserDTO = z.infer<typeof userDTO>;

export const userQueryDTO = tableQueryDTO.extend({
	name: filterFieldDTO.optional(),
	email: filterFieldDTO.optional(),
	roleName: filterFieldDTO.optional(),
	supervisorName: filterFieldDTO.optional(),
	createdAt: filterFieldDTO.optional(),
});
export type UserQueryDTO = z.infer<typeof userQueryDTO>;
