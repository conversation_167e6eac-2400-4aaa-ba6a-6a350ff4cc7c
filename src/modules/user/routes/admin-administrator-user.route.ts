import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as adminAdministratorUserController from "../controllers/admin-administrator-user.controller";
import { administratorUserQueryDTO } from "../dtos";
import {
	adminCreateAdministratorUserDTO,
	adminUpdateAdministratorUserDTO,
} from "../dtos/request";

export const adminAdministratorUserRouter = Router();

adminAdministratorUserRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(administratorUserQueryDTO),
	adminAdministratorUserController.getAllAdministratorUser,
);

adminAdministratorUserRouter.get(
	"/:administratorUserId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	adminAdministratorUserController.getAdministratorUser,
);

adminAdministratorUserRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateAdministratorUserDTO),
	adminAdministratorUserController.createAdministratorUser,
);

adminAdministratorUserRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	adminAdministratorUserController.bulkAction,
);

adminAdministratorUserRouter.put(
	"/:administratorUserId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateAdministratorUserDTO),
	adminAdministratorUserController.updateAdministratorUser,
);
