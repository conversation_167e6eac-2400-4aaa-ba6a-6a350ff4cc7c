import { Router } from "express";
import { bulkActionDTO } from "@/shared/dtos";
import { AuthStrategy } from "@/shared/enums";
import {
	authMiddleware,
	validateBody,
	validateQuery,
} from "@/shared/middlewares";
import * as userAdminController from "../controllers/admin-user.controller";
import { userQueryDTO } from "../dtos";
import { adminCreateUserDTO, adminUpdateUserDTO } from "../dtos/request";

export const adminUserRouter = Router();

adminUserRouter.get(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateQuery(userQueryDTO),
	userAdminController.getAllUser,
);

adminUserRouter.get(
	"/tree",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	userAdminController.getUserTree,
);

adminUserRouter.get("/options", userAdminController.getUserOptions);

adminUserRouter.get(
	"/:userId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	userAdminController.getUser,
);

adminUserRouter.post(
	"/",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminCreateUserDTO),
	userAdminController.createUser,
);

adminUserRouter.post(
	"/bulk-action",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(bulkActionDTO),
	userAdminController.bulkAction,
);

adminUserRouter.put(
	"/:userId",
	authMiddleware(AuthStrategy.JWT_ADMIN),
	validateBody(adminUpdateUserDTO),
	userAdminController.updateUser,
);
