import type { NextFunction, Request, Response } from "express";
import type { BulkActionDTO } from "@/shared/dtos";
import type { AdministratorUserQueryDTO } from "../dtos";
import type {
	AdminCreateAdministratorUserDTO,
	AdminUpdateAdministratorUserDTO,
} from "../dtos/request";
import * as adminAdministratorUserService from "../services/admin-administrator-user.service";

export const getAllAdministratorUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as AdministratorUserQueryDTO;

		const { data, meta } =
			await adminAdministratorUserService.getAllAdministratorUsers(
				validatedQuery,
			);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data,
			meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getAdministratorUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const administratorUserId = req.params.administratorUserId as string;

		const result =
			await adminAdministratorUserService.getAdministratorUser(
				administratorUserId,
			);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createAdministratorUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateAdministratorUserDTO;

		const result =
			await adminAdministratorUserService.createAdministratorUser(
				validatedBody,
			);

		const response = {
			status: "success",
			message: "Berhasil membuat administrator user baru",
			data: {
				...result,
			},
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateAdministratorUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const administratorUserId = req.params.administratorUserId as string;
		const validatedBody = req.validatedBody as AdminUpdateAdministratorUserDTO;

		const result = await adminAdministratorUserService.updateAdministratorUser(
			administratorUserId,
			validatedBody,
		);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: {
				...result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result =
			await adminAdministratorUserService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader(
				"Content-Disposition",
				"attachment; filename=administratorUsers.csv",
			);
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
