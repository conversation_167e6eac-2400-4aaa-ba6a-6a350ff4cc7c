import type { NextFunction, Request, Response } from "express";
import type { UsersId } from "@/database/types/public/Users";
import type { BulkActionDTO, GetTreeNodeResponseDTO } from "@/shared/dtos";
import type { UserQueryDTO } from "../dtos";
import type { AdminCreateUserDTO, AdminUpdateUserDTO } from "../dtos/request";
import * as userAdminService from "../services/admin-user.service";

export const getAllUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedQuery = req.validatedQuery as UserQueryDTO;

		const { data, meta } = await userAdminService.getAllUser(validatedQuery);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data,
			meta,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getUserTree = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await userAdminService.getUserTree();

		const response: GetTreeNodeResponseDTO = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getUserOptions = async (
	_req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const result = await userAdminService.getUserOptions();

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const getUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const userId = req.params.userId as UsersId;

		const result = await userAdminService.getUser(userId);

		const response = {
			status: "success",
			message: "Berhasil mengambil data",
			data: result,
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const createUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as AdminCreateUserDTO;

		const result = await userAdminService.createUser(validatedBody);

		const response = {
			status: "success",
			message: "Berhasil membuat user baru",
			data: {
				...result,
			},
		};
		res.status(201).json(response);
	} catch (error) {
		next(error);
	}
};

export const updateUser = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const userId = req.params.userId as UsersId;
		const validatedBody = req.validatedBody as AdminUpdateUserDTO;

		const result = await userAdminService.updateUser(userId, validatedBody);

		const response = {
			status: "success",
			message: "Berhasil update data",
			data: {
				...result,
			},
		};
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

export const bulkAction = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	try {
		const validatedBody = req.validatedBody as BulkActionDTO;

		const result = await userAdminService.bulkAction(validatedBody);

		if (validatedBody.action === "export") {
			res.setHeader("Content-Disposition", "attachment; filename=users.csv");
			res.setHeader("Content-Type", "text/csv");
			res.status(200).send(result);
			return;
		}

		res.send();
	} catch (error) {
		next(error);
	}
};
