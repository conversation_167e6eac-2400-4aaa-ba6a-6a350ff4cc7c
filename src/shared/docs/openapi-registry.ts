import "@/shared/lib/zod-extensions"; // Import the new file first
import { OpenAPIRegistry } from "@asteasolutions/zod-to-openapi";
import { bulkActionDTO, timestampDTO } from "../dtos/common.dto";
import {
	baseErrorResponseDTO,
	basePaginationResponseDTO,
	baseResponseDTO,
} from "../dtos/util.dto";

export const registry = new OpenAPIRegistry();

registry.register("BaseResponseDTO", baseResponseDTO);
registry.register("BaseErrorResponseDTO", baseErrorResponseDTO);
registry.register("BasePaginationResponseDTO", basePaginationResponseDTO);
registry.register("TimestampDTO", timestampDTO);
registry.register("BulkActionDTO", bulkActionDTO);
