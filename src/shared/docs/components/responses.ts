import * as z from "zod";

export const errorResponses = {
	BadRequest: {
		description: "Bad Request. Missing or invalid parameters.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: {
							status: "failed",
							message: "Bad Request. Missing or invalid parameters.",
						},
					}),
			},
		},
	},
	Unauthorized: {
		description: "Unauthorized. Authentication required.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: {
							status: "failed",
							message: "Not Found. Resource not found.",
						},
					}),
			},
		},
	},
	Forbidden: {
		description: "Forbidden. You do not have permission.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: {
							status: "failed",
							message: "Unauthorized. Authentication required.",
						},
					}),
			},
		},
	},
	UnProcessableEntity: {
		description: "UnProcessable Entity. Some validation error.",
		content: {
			"application/json": {
				schema: z
					.object({
						status: z.literal("failed"),
						message: z.string(),
						errors: z.record(z.string(), z.array(z.string())),
					})
					.meta({
						example: {
							status: "failed",
							message: "Validation error",
							errors: {
								key: ["Required"],
								key2: ["Required", "Must be a string"],
							},
						},
					}),
			},
		},
	},
	NotFound: {
		description: "Not Found. Resource not found.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: {
							status: "failed",
							message: "Forbidden. You do not have permission.",
						},
					}),
			},
		},
	},
	TooManyRequests: {
		description: "Too Many Requests. Rate limit exceeded.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: {
							status: "failed",
							message: "Too Many Requests. Rate limit exceeded.",
						},
					}),
			},
		},
	},
	InternalServerError: {
		description: "Internal Server Error.",
		content: {
			"application/json": {
				schema: z
					.object({ status: z.literal("failed"), message: z.string() })
					.meta({
						example: { status: "failed", message: "Internal Server Error." },
					}),
			},
		},
	},
};
