import * as z from "zod";

export const paginationQueryParams = z
	.object({
		page: z.string().optional().meta({
			description: "Page number for pagination (starting from 1)",
			example: "2",
			default: "1",
		}),
		limit: z.string().optional().meta({
			description: "Number of items per page",
			example: "25",
			default: "10",
		}),
		search: z.string().optional().meta({
			description: "Search term to filter results",
			example: "Keyword",
			default: "",
		}),
		sort: z
			.string()
			.regex(/^[a-zA-Z0-9_]+(\.[a-zA-Z0-9_]+)?\.(asc|desc)$/)
			.optional()
			.meta({
				description:
					"Sort field and direction (e.g., 'createdAt.desc', 'name.asc')",
				example: "name.asc",
				default: "createdAt.desc",
			}),
		all: z.enum(["true", "false"]).optional().meta({
			description: "Return all records (ignores pagination)",
			example: "false",
			default: "false",
		}),
		// Dynamic field filters kayak status, type, createdAt, dst bisa dimasukkan pakai .passthrough()
	})
	.passthrough();
