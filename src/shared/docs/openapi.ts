import fs from "node:fs";
import path from "node:path";
import { pathToFileURL } from "node:url";
import { OpenApiGeneratorV3 } from "@asteasolutions/zod-to-openapi";
import { PATHS } from "@/shared/lib/paths";
import { registry } from "./openapi-registry";

async function loadDocsRecursively(dirPath: string) {
	const entries = fs.readdirSync(dirPath, { withFileTypes: true });

	for (const entry of entries) {
		const fullPath = path.join(dirPath, entry.name);

		if (entry.isDirectory()) {
			// Rekursif ke dalam folder
			await loadDocsRecursively(fullPath);
		} else if (
			entry.isFile() &&
			(entry.name.endsWith(".doc.ts") || entry.name.endsWith(".doc.js"))
		) {
			const fileUrl = pathToFileURL(fullPath).href;
			await import(fileUrl);
		}
	}
}

async function loadFeatureDocs() {
	const featuresPath = path.resolve(PATHS.MODULES);
	const featureFolders = fs
		.readdirSync(featuresPath, { withFileTypes: true })
		.filter((dirent) => dirent.isDirectory())
		.map((dirent) => dirent.name);

	for (const featureName of featureFolders) {
		const featureDocPath = path.join(featuresPath, featureName, "docs");

		if (fs.existsSync(featureDocPath)) {
			await loadDocsRecursively(featureDocPath);
		}
	}
}

registry.registerComponent("securitySchemes", "bearerAuth", {
	type: "http",
	scheme: "bearer",
	bearerFormat: "JWT",
	description: "JWT token authentication",
});

export async function generateOpenAPIDocument() {
	await loadFeatureDocs();
	const generator = new OpenApiGeneratorV3(registry.definitions);

	return generator.generateDocument({
		openapi: "3.0.3",
		info: {
			title: "My Private API",
			description: "API documentation for private internal use",
			version: "1.0.0",
		},
		servers: [
			{
				url: process.env.PRODUCTION_URL as string,
				description: "Production server",
			},
			{ url: process.env.STAGING_URL as string, description: "Staging server" },
		],
	});
}
