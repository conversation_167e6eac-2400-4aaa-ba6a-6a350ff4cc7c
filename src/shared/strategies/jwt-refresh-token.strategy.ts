import type { Request } from "express";
import passport from "passport";
import { ExtractJwt, Strategy as JwtStrategy, type StrategyOptionsWithoutRequest } from "passport-jwt";
import { Env } from "@/shared/config/env.config";
import type { AuthTokenPayload } from "@/shared/types";

const cookieExtractor = (req: Request) => {
	let jwt = null;

	if (req?.cookies) {
		jwt = req.cookies?.refreshToken || null;
	}

	return jwt;
};

const jwtOptions: StrategyOptionsWithoutRequest = {
	jwtFromRequest: ExtractJwt.fromExtractors([ExtractJwt.fromAuthHeaderAsBearerToken(), cookieExtractor]),
	secretOrKey: Env.REFRESH_TOKEN_SECRET,
	ignoreExpiration: false,
};

export const jwtStrategy = new JwtStrategy(jwtOptions, async (payload: AuthTokenPayload, done) => {
	try {
		return done(null, payload);
	} catch (err) {
		return done(err, false);
	}
});

// Register the strategy with Passport
passport.use("jwt-refresh-token", jwtStrategy);
