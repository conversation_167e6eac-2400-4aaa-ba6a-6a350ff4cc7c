import dayjs from "dayjs";
import type { Request } from "express";
import passport from "passport";
import {
	ExtractJwt,
	Strategy as JwtStrategy,
	type StrategyOptionsWithoutRequest,
} from "passport-jwt";
import { db } from "@/database/connection";
import type { SessionsId } from "@/database/types/public/Sessions";
import type { UsersId } from "@/database/types/public/Users";
import { Env } from "@/shared/config/env.config";
import { CACHE_KEYS, CACHE_TTL, cacheHelpers } from "@/shared/lib/redis";
import type { AuthTokenPayload, AuthUser } from "@/shared/types";

const cookieExtractor = (req: Request) => {
	let jwt = null;

	if (req?.cookies) {
		jwt = req.cookies?.accessToken || null;
	}

	return jwt;
};

const jwtOptions: StrategyOptionsWithoutRequest = {
	jwtFromRequest: ExtractJwt.fromExtractors([
		ExtractJwt.fromAuthHeaderAs<PERSON>earerToken(),
		cookieExtractor,
	]),
	secretOrKey: Env.ACCESS_TOKEN_SECRET,
	ignoreExpiration: false,
};

export const jwtUserStrategy = new JwtStrategy(
	jwtOptions,
	async (payload: AuthTokenPayload, done) => {
		try {
			if (payload.ownerType !== "user") return done(null, false);

			const session = await db
				.selectFrom("sessions")
				.select(["id", "expiresAt"])
				.where("id", "=", payload.sessionId as SessionsId)
				.executeTakeFirst();

			if (!session || dayjs().isAfter(dayjs(session.expiresAt))) {
				return done(null, false);
			}

			const cacheKey = CACHE_KEYS.userSession(payload.id);
			let authUser: AuthUser | null =
				await cacheHelpers.get<AuthUser>(cacheKey);

			if (!authUser) {
				// If not in cache, fetch from database
				const rows = await db
					.selectFrom("users")
					.innerJoin("usersRoles", "usersRoles.userId", "users.id")
					.innerJoin("roles", "roles.id", "usersRoles.roleId")
					.select([
						"users.id",
						"users.name",
						"users.email",
						"users.image",
						"roles.id as roleId",
						"roles.name as roleName",
						"roles.parentId as parentRoleId",
					])
					.where("users.id", "=", payload.id as UsersId)
					.execute();

				if (rows.length === 0 || !rows[0]) {
					return done(null, false);
				}

				const { id, name, email, image } = rows[0];
				const roles = rows.map((row) => ({
					id: row.roleId,
					name: row.roleName,
					parentId: row.parentRoleId ?? null,
				}));

				authUser = {
					id,
					name,
					email,
					image,
					roles,
				};

				await cacheHelpers.set(cacheKey, authUser, CACHE_TTL.USER_SESSION);
			}

			return done(null, { user: authUser, tokenPayload: payload });
		} catch (err) {
			return done(err, false);
		}
	},
);

// Register the strategy with Passport
passport.use("jwt-user", jwtUserStrategy);
