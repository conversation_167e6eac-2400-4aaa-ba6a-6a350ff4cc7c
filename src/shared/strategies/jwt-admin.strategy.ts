import dayjs from "dayjs";
import type { Request } from "express";
import passport from "passport";
import {
	ExtractJwt,
	Strategy as JwtStrategy,
	type StrategyOptionsWithoutRequest,
} from "passport-jwt";
import { db } from "@/database/connection";
import type { AdministratorUsersId } from "@/database/types/public/AdministratorUsers";
import type { SessionsId } from "@/database/types/public/Sessions";
import { CACHE_KEYS, CACHE_TTL, cacheHelpers } from "@/shared/lib/redis";
import type { AuthTokenPayload, AuthUser } from "@/shared/types";
import { Env } from "../config/env.config";

const cookieExtractor = (req: Request) => {
	let jwt = null;

	if (req?.cookies) {
		jwt = req.cookies?.accessToken || null;
	}

	return jwt;
};

const jwtOptions: StrategyOptionsWithoutRequest = {
	jwtFromRequest: ExtractJwt.fromExtractors([
		ExtractJwt.fromAuthHeader<PERSON><PERSON>earer<PERSON>oken(),
		cookieExtractor,
	]),
	secretOrKey: Env.ACCESS_TOKEN_SECRET,
	ignoreExpiration: false,
};

export const jwtAdminStrategy = new JwtStrategy(
	jwtOptions,
	async (payload: AuthTokenPayload, done) => {
		try {
			if (payload.ownerType !== "admin") return done(null, false);

			const session = await db
				.selectFrom("sessions")
				.select(["id", "expiresAt"])
				.where("id", "=", payload.sessionId as SessionsId)
				.executeTakeFirst();

			if (!session || dayjs().isAfter(dayjs(session.expiresAt))) {
				return done(null, false);
			}

			const cacheKey = CACHE_KEYS.adminSession(payload.id);
			let authUser = await cacheHelpers.get<AuthUser>(cacheKey);

			if (!authUser) {
				// Join ke roles
				const row = await db
					.selectFrom("administratorUsers")
					.select(["id", "name", "email", "image"])
					.where(
						"administratorUsers.id",
						"=",
						payload.id as AdministratorUsersId,
					)
					.executeTakeFirst();

				if (!row) return done(null, false);

				authUser = {
					id: row.id,
					name: row.name,
					email: row.email,
					image: row.image ?? null,
					roles: [],
				};

				await cacheHelpers.set(cacheKey, authUser, CACHE_TTL.ADMIN_SESSION);
			}

			return done(null, { user: authUser, tokenPayload: payload });
		} catch (err) {
			return done(err, false);
		}
	},
);

// Register the strategy with Passport
passport.use("jwt-admin", jwtAdminStrategy);
