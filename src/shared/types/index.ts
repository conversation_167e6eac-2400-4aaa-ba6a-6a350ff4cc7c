export interface AuthUserWithTokenPayload {
	user: AuthUser;
	tokenPayload: AuthTokenPayload;
}

export type AuthUser = {
	id: string; // id user or administrator_user
	name: string;
	email: string;
	image: string;
	roles: { id: string; name: string; parentId: string | null }[];
};

export type OwnerType = "user" | "admin";

export interface AuthTokenPayload {
	id: string;
	ownerType: OwnerType;
	sessionId: string;
}

export interface ForgotPasswordTokenPayload {
	userId: string;
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
