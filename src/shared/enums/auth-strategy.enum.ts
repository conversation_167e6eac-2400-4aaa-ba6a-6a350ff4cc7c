/**
 * Enum for available Passport authentication strategies.
 *
 * @description
 * This enum defines all available authentication strategies that can be used
 * with the authMiddleware and optionalAuthMiddleware functions.
 *
 * @example
 * import { AuthStrategy } from '@/app/enums';
 *
 * // Type-safe usage with autocomplete
 * app.use('/api/user', authMiddleware(AuthStrategy.JWT_USER));
 * app.use('/api/admin', authMiddleware(AuthStrategy.JWT_ADMIN));
 */
export enum AuthStrategy {
	/**
	 * JWT strategy for regular user authentication.
	 * Uses UserModel for authentication and authorization.
	 */
	JWT_USER = "jwt-user",

	/**
	 * JWT strategy for admin user authentication.
	 * Uses AdminUserModel for authentication and authorization.
	 */
	JWT_ADMIN = "jwt-admin",

	/**
	 * Jwt strategy for refreshing access-token
	 */
	JWT_REFRESH_TOKEN = "jwt-refresh-token",

	/**
	 * Google OAuth strategy for social authentication.
	 * Future implementation for Google OAuth integration.
	 */
	// GOOGLE = "google",

	/**
	 * Facebook OAuth strategy for social authentication.
	 * Future implementation for Facebook OAuth integration.
	 */
	// FACEBOOK = "facebook",
}

/**
 * Type alias for AuthStrategy enum values.
 * Can be used as a union type for function parameters.
 */
export type AuthStrategyType = `${AuthStrategy}`;

/**
 * Array of all available auth strategies for validation purposes.
 */
export const AUTH_STRATEGIES = Object.values(AuthStrategy) as AuthStrategyType[];
