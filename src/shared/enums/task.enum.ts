export enum TaskStatus {
	PENDING = "PENDING", // #D6D6D6;
	IN_PROGRESS = "IN_PROGRESS", // #C3E6FF
	IN_REVIEW = "IN_REVIEW", // #FFEEB0;
	REVISION_REQUIRED = "REVISION_REQUIRED", //  #FFB8B8;
	COMPLETED = "COMPLETED", // #C9EACB
	REJECTED = "REJECTED", // #FFB8B8
}

export const TaskStatusColors: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: "#D6D6D6",
  [TaskStatus.IN_PROGRESS]: "#C3E6FF",
  [TaskStatus.IN_REVIEW]: "#FFEEB0",
  [TaskStatus.REVISION_REQUIRED]: "#FFB8B8",
  [TaskStatus.COMPLETED]: "#C9EACB",
  [TaskStatus.REJECTED]: "#FFB8B8",
};

export enum TaskSubmissionStatus {
	PENDING_REVIEW = "PENDING_REVIEW", //
	ACCEPTED = "ACCEPTED", //
	REJECTED = "REJECTED", //
}

export const TASK_STATUS_LABEL_MAP: Record<TaskStatus, string> = {
	[TaskStatus.PENDING]: "Tugas Baru",
	[TaskStatus.IN_PROGRESS]: "Dikerjakan",
	[TaskStatus.IN_REVIEW]: "Dalam Pengecekan",
	[TaskStatus.REVISION_REQUIRED]: "Perlu Revisi",
	[TaskStatus.COMPLETED]: "Selesai",
	[TaskStatus.REJECTED]: "Ditolak",
};