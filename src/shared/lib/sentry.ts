import * as Sentry from "@sentry/node";
import { nodeProfilingIntegration } from "@sentry/profiling-node";
import { Env } from "@/shared/config/env.config";
import logger from "./logger";

/**
 * Initialize Sentry with dual configuration support
 */
export function initSentry(): void {
	// Skip initialization if no DSN provided
	if (!Env.SENTRY_DSN) {
		logger.info("Sentry DSN not provided, skipping Sentry initialization");
		return;
	}

	try {
		Sentry.init({
			// DSN - Data Source Name (from Sentry Cloud or Self-hosted)
			dsn: Env.SENTRY_DSN as string,

			// Environment (development, staging, production)
			environment: Env.SENTRY_ENVIRONMENT,

			// Release version (optional, for tracking deployments)
			release: Env.SENTRY_RELEASE,

			// Performance Monitoring
			tracesSampleRate: Env.SENTRY_TRACES_SAMPLE_RATE, // 0.1 = 10% of transactions
			profilesSampleRate: Env.SENTRY_PROFILES_SAMPLE_RATE, // 0.1 = 10% of transactions

			// Integrations
			integrations: [
				// Node.js profiling integration
				nodeProfilingIntegration(),

				// HTTP integration for tracking HTTP requests
				Sentry.httpIntegration(),

				// Express integration for tracking Express.js requests
				Sentry.expressIntegration(),

				// Console integration for capturing console.error
				Sentry.consoleIntegration({
					levels: ["error"],
				}),

				// Local variables integration for better debugging
				Sentry.localVariablesIntegration({
					captureAllExceptions: false,
					maxExceptionsPerSecond: 5,
				}),
			],

			// Error filtering
			beforeSend(event, hint) {
				// Filter out known non-critical errors
				const error = hint.originalException;

				if (error instanceof Error) {
					// Skip common non-critical errors
					const skipErrors = ["ECONNRESET", "ENOTFOUND", "ECONNREFUSED", "ERR_HTTP_HEADERS_SENT", "Request aborted"];

					if (skipErrors.some((skipError) => error.message.includes(skipError))) {
						return null; // Don't send to Sentry
					}
				}

				// Log locally for debugging
				logger.error("Sentry captured error:", {
					message: event.message,
					level: event.level,
					fingerprint: event.fingerprint,
				});

				return event;
			},

			// Transaction filtering
			beforeSendTransaction(event) {
				// Skip very fast transactions (< 100ms) to reduce noise
				if (event.start_timestamp && event.timestamp) {
					const duration = (event.timestamp - event.start_timestamp) * 1000;
					if (duration < 100) {
						return null;
					}
				}

				return event;
			},

			// Additional options
			debug: Env.NODE_ENV === "development",
			attachStacktrace: true,
			sendDefaultPii: false, // Don't send personally identifiable information
			maxBreadcrumbs: 50,
			maxValueLength: 1000,

			// Server name (useful for multi-server deployments)
			serverName: process.env.SERVER_NAME || "attendance-api",

			// Tags for better organization
			initialScope: {
				tags: {
					component: "attendance-api",
					version: process.env.npm_package_version || "unknown",
				},
			},
		});

		logger.info("✅ Sentry initialized successfully", {
			environment: Env.SENTRY_ENVIRONMENT,
			tracesSampleRate: Env.SENTRY_TRACES_SAMPLE_RATE,
			profilesSampleRate: Env.SENTRY_PROFILES_SAMPLE_RATE,
		});
	} catch (error) {
		logger.error("❌ Failed to initialize Sentry:", error);
	}
}

/**
 * Capture exception with additional context
 */
export function captureException(error: Error, context?: Record<string, any>): void {
	Sentry.withScope((scope) => {
		if (context) {
			scope.setContext("additional", context);
		}
		Sentry.captureException(error);
	});
}

/**
 * Capture message with level
 */
export function captureMessage(
	message: string,
	level: Sentry.SeverityLevel = "info",
	context?: Record<string, any>,
): void {
	Sentry.withScope((scope) => {
		if (context) {
			scope.setContext("additional", context);
		}
		Sentry.captureMessage(message, level);
	});
}

/**
 * Set user context for error tracking
 */
export function setUserContext(user: { id: string; email?: string; username?: string }): void {
	Sentry.setUser(user);
}

/**
 * Clear user context (e.g., on logout)
 */
export function clearUserContext(): void {
	Sentry.setUser(null);
}

/**
 * Add breadcrumb for debugging
 */
export function addBreadcrumb(
	message: string,
	category: string,
	level: Sentry.SeverityLevel = "info",
	data: Record<string, any> = {},
): void {
	Sentry.addBreadcrumb({
		message,
		category,
		level,
		// data
		data,
		timestamp: Date.now() / 1000,
	});
}

/**
 * Get Sentry Express handlers for middleware
 */
export const sentryHandlers = {
	// Request handler (must be first middleware)
	requestHandler: Sentry.expressIntegration(),

	// Error handler (must be last middleware, before your own error handlers)
	errorHandler: (error: any, _req: any, _res: any, next: any) => {
		// Only handle 5xx errors
		if (error.status >= 500) {
			Sentry.captureException(error);
		}
		next(error);
	},
};

/**
 * Flush Sentry events (useful for graceful shutdown)
 */
export async function flushSentry(timeout = 2000): Promise<boolean> {
	try {
		return await Sentry.flush(timeout);
	} catch (error) {
		logger.error("Failed to flush Sentry events:", error);
		return false;
	}
}

// Export Sentry for direct access if needed
export { Sentry };
