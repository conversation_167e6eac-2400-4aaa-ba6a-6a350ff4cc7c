import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import { Env } from "@/shared/config/env.config";

const { combine, timestamp, printf, colorize, errors } = winston.format;

// Development format (colored, human-readable)
const devFormat = printf(({ level, message, timestamp, stack }) => {
	let msg = `${timestamp} [${level}]: ${message}`;
	if (stack) msg += `\n${stack}`;
	return msg;
});

// Production format (structured JSON)
const prodFormat = printf(({ level, message, timestamp, ...metadata }) => {
	return JSON.stringify({
		timestamp,
		level,
		message,
		...metadata,
	});
});

// Base logger configuration
const logger = winston.createLogger({
	level: Env.NODE_ENV === "production" ? "info" : "debug",
	format: combine(
		timestamp({ format: "ISO8601" }),
		errors({ stack: true }),
		Env.NODE_ENV === "production" ? prodFormat : devFormat,
	),
	// transports: [new winston.transports.Console()],
});

// Environment-specific configurations
if (Env.NODE_ENV !== "production") {
	logger.add(
		new winston.transports.Console({
			format: combine(colorize(), devFormat),
		}),
	);
}

if (Env.NODE_ENV === "production") {
	// File rotation in production
	logger.add(
		new DailyRotateFile({
			filename: "application-%DATE%.log",
			datePattern: "YYYY-MM-DD",
			zippedArchive: true, // Kompresi file log lama
			maxSize: "20m", // Rotasi saat file mencapai 20MB
			maxFiles: "14d", // Simpan log selama 14 hari
			level: "info", // Level lebih tinggi untuk production
			dirname: "./logs",
			auditFile: "./logs/application-audit.json",
		}),
	);

	logger.add(
		new DailyRotateFile({
			filename: "error-%DATE%.log",
			datePattern: "YYYY-MM-DD",
			zippedArchive: true,
			maxSize: "20m",
			maxFiles: "14d",
			level: "error", // Hanya log error
			dirname: "./logs",
			auditFile: "./logs/error-audit.json",
		}),
	);

	// Silence debug logs in production
	logger.transports.forEach((t) => {
		if (t.level === "debug") t.silent = true;
	});
}

export default logger;
