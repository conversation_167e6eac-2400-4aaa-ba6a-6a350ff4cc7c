export function parseFilterParam(
	raw: string | undefined,
): (string | number)[] | { min?: number; max?: number } | string {
	if (!raw) return "";
	const parts = raw.split(",");

	if (parts.length === 2 && parts.every((p) => /^\d+(\.\d+)?$/.test(p))) {
		// Treat as range
		if (parts[0]?.trim() && parts[1]?.trim()) {
			return {
				min: Number.parseFloat(parts[0]),
				max: Number.parseFloat(parts[1]),
			};
		}
		return "";
	}

	if (parts.length > 1) {
		return parts;
	}

	return raw;
}


