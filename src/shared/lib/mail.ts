import { Resend } from "resend";
import { Env } from "@/shared/config/env.config";
import type { TEMPLATE_MAIL_KEY } from "../constants";

const resend = new Resend(Env.RESEND_API_KEY);

export const sendEmail = async ({
	from = "<EMAIL>",
	to,
	subject,
	html,
}: {
	from?: string;
	to: string;
	subject: string;
	html: string;
}) => {
	const { error } = await resend.emails.send({
		from,
		to,
		subject,
		html,
	});

	if (error) {
		console.error(error);
	}
};

export type TemplateMailKey = keyof typeof TEMPLATE_MAIL_KEY;

export function getMailTemplate(templateName: TemplateMailKey, variables: Record<string, any>): string {
	const templates: Record<TemplateMailKey, string> = {
		EMAIL_VERIFICATION: `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
          }
          .otp {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin: 20px 0;
          }
          .footer {
            font-size: 12px;
            color: #888;
            text-align: center;
            margin-top: 20px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Verify Your Email</h2>
          </div>
          <p>Hi,</p>
          <p>Thank you for signing up! Please use the following OTP to verify your email address:</p>
          <div class="otp">${variables?.otp}</div>
          <p>This OTP is valid for the next 5 minutes. If you did not request this, please ignore this email or contact support if you have concerns.</p>
          <p>Thank you,</p>
          <p>Rekening Kita Bersama</p>
          <div class="footer">
            <p>&copy; ${new Date().getFullYear()} Rekening Kita Bersama. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
		RESET_PASSWORD: `
      <!DOCTYPE html>
      <html lang="id">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Reset Kata Sandi Anda</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            background-color: #f4f6f8;
            margin: 0;
            padding: 0;
            color: #333;
          }
          .container {
            max-width: 600px;
            margin: 40px auto;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
          }
          .header {
            background-color: #4f46e5;
            color: white;
            padding: 24px;
            text-align: center;
          }
          .content {
            padding: 32px 24px;
            font-size: 16px;
          }
          .button-wrapper {
            text-align: center;
            margin: 30px 0;
          }
          .button {
            display: inline-block;
            padding: 14px 28px;
            background-color: #4f46e5;
            color: white !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
          }
          .button:visited,
          .button:hover,
          .button:active {
            color: white !important;
          }
          .fallback-link {
            margin-top: 24px;
            font-size: 14px;
            word-break: break-word;
          }
          .footer {
            padding: 24px;
            text-align: center;
            font-size: 12px;
            color: #888;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Reset Kata Sandi Anda</h2>
          </div>
          <div class="content">
            <p>Halo <strong>${variables.name || "Pengguna"}</strong>,</p>
            <p>Kami menerima permintaan untuk mereset kata sandi akun Anda. Silakan klik tombol di bawah ini untuk mengatur ulang kata sandi Anda:</p>

            <div class="button-wrapper">
              <a href="${variables.link}" class="button" target="_blank" rel="noopener noreferrer">Reset Kata Sandi</a>
            </div>

            <p>Jika tombol di atas tidak berfungsi, Anda dapat menggunakan tautan berikut:</p>
            <div class="fallback-link">
              <a href="${variables.link}" target="_blank" rel="noopener noreferrer">${variables.link}</a>
            </div>

            <p>Tautan ini berlaku selama 1 jam. Jika Anda tidak meminta reset kata sandi, abaikan email ini.</p>

            <p>Terima kasih,<br />
            Tim ${variables.companyName || "Kami"}</p>
          </div>
          <div class="footer">
            © ${variables.year || new Date().getFullYear()} ${variables.companyName || "PMCK-Company"}. Seluruh hak cipta dilindungi.
          </div>
        </div>
      </body>
      </html>
    `,
	};
	const selectedTemplate = templates[templateName as TemplateMailKey] || templates.EMAIL_VERIFICATION;

	return selectedTemplate;
}
