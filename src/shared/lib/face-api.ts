import "@tensorflow/tfjs-node";
import fs from "node:fs/promises";
import * as faceapi from "@vladmandic/face-api";
import { Canvas, createCanvas, Image, ImageData } from "canvas";
import sharp from "sharp";
import { BadRequestError } from "@/shared/exceptions";
import { PATHS } from "@/shared/lib/paths";

let initialized = false;

/**
 * Initialize face-api and load models
 */
export async function initializeFaceApi(): Promise<void> {
	if (initialized) return;

	// Monkey patch for face-api to work in Node.js
	// @ts-ignore
	faceapi.env.monkeyPatch({ Canvas, Image, ImageData });

	const modelsPath = PATHS.FACE_API_MODELS;
	await faceapi.nets.ssdMobilenetv1.loadFromDisk(modelsPath);
	await faceapi.nets.faceLandmark68Net.loadFromDisk(modelsPath);
	await faceapi.nets.faceRecognitionNet.loadFromDisk(modelsPath);

	initialized = true;
	console.log("✅ Face recognition models loaded successfully");
}

/**
 * Extract face descriptor from an image path
 */
// export async function extractFaceDescriptor(
// 	imagePath: string,
// ): Promise<string> {
// 	if (!initialized) {
// 		throw new Error(
// 			"Face recognition service not initialized. Call initializeFaceApi() first.",
// 		);
// 	}

// 	const imageBuffer = await fs.readFile(imagePath);
// 	const img = new Image();
// 	img.src = imageBuffer;

// 	const detections = await faceapi
// 		.detectAllFaces(
// 			img,
// 			new faceapi.SsdMobilenetv1Options({ minConfidence: 0.8 }),
// 		)
// 		.withFaceLandmarks()
// 		.withFaceDescriptors();

// 	if (detections.length === 0) {
// 		throw new BadRequestError(
// 			"Tidak ada wajah terdeteksi, mohon untuk mengirimkan gambar dengan wajah yang terlihat jelas.",
// 		);
// 	}

// 	if (detections.length > 1) {
// 		throw new BadRequestError(
// 			"Harus ada tepat 1 wajah di gambar, mohon untuk mengirimkan gambar dengan wajah anda yang terlihat jelas.",
// 		);
// 	}

// 	if (!detections?.[0]?.descriptor) {
// 		throw new BadRequestError("Wajah tidak terdeteksi");
// 	}

// 	return JSON.stringify(Array.from(detections[0].descriptor));
// }

export async function extractFaceDescriptor(
	imagePath: string,
): Promise<string> {
	if (!initialized) {
		throw new Error(
			"Face recognition service not initialized. Call initializeFaceApi() first.",
		);
	}

	// Step 1: Read and auto-rotate image via EXIF
	const imageBuffer = await fs.readFile(imagePath);

	// Step 2: Validate portrait orientation
	const metadata = await sharp(imageBuffer).metadata();
	if (metadata.width && metadata.height) {
		if (metadata.width > metadata.height) {
			throw new BadRequestError(
				"Harap kirimkan gambar dalam orientasi potret (portrait).",
			);
		}
	}

	// Step 3: Resize if width > 800px
	let finalBuffer = imageBuffer;
	if (metadata.width && metadata.width > 800) {
		finalBuffer = await sharp(imageBuffer).resize({ width: 800 }).toBuffer();
	}

	// Step 4: Load into Image
	const img = new Image();
	img.src = finalBuffer;

	// Step 5: Convert to canvas for face-api
	const canvas = createCanvas(img.width, img.height);
	const ctx = canvas.getContext("2d");
	ctx.drawImage(img, 0, 0);

	// Step 6: Detect face
	const detections = await faceapi
		.detectAllFaces(canvas, new faceapi.SsdMobilenetv1Options())
		.withFaceLandmarks()
		.withFaceDescriptors();

	// Step 7: Handle result
	if (detections.length === 0) {
		throw new BadRequestError(
			"Tidak ada wajah terdeteksi, mohon untuk mengirimkan gambar dengan wajah yang terlihat jelas.",
		);
	}

	if (detections.length > 1) {
		throw new BadRequestError(
			"Harus ada tepat 1 wajah di gambar, mohon untuk mengirimkan gambar dengan wajah anda yang terlihat jelas.",
		);
	}

	if (!detections?.[0]?.descriptor) {
		throw new BadRequestError("Wajah tidak terdeteksi");
	}

	return JSON.stringify(Array.from(detections[0].descriptor));
}

/**
 * Compare using FaceApi built-in FaceMatcher
 */
export function compareFaceDescriptorFaceApi(
	sampleDescriptors: string[],
	faceDescriptor: string,
): number {
	try {
		const float32ArraySample = sampleDescriptors.map(
			(sample) => new Float32Array(JSON.parse(sample)),
		);
		const float32ArrayInput = new Float32Array(JSON.parse(faceDescriptor));

		const faceMatcher = new faceapi.FaceMatcher(float32ArraySample);
		const bestMatch = faceMatcher.findBestMatch(float32ArrayInput);

		return bestMatch.distance;
	} catch (error) {
		console.error("Error comparing faces (FaceMatcher):", error);
		return Number.POSITIVE_INFINITY;
	}
}
