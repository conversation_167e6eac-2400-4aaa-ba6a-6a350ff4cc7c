import fs from "node:fs";
import path from "node:path";

const rootDir = process.cwd();
const storageDir = path.join(rootDir, "storage");

export const PATHS = {
	// Base directories
	ROOT: rootDir,
	STORAGE: storageDir,
	PUBLIC: {
		BASE: path.join(storageDir, "public"),
		UPLOADS: path.join(storageDir, "public/uploads"),
	},
	PRIVATE: {
		BASE: path.join(storageDir, "private"),
		UPLOADS: path.join(storageDir, "private/uploads"),
	},
	FACE_API_MODELS: path.join(rootDir, "models/face-api"),
	MODULES: path.join(rootDir, "src/modules"),

	// Helper methods
	toDatabasePath: (fullPath: string) => {
		if (!fullPath) return;

		const relativePath = path.relative(storageDir, fullPath);
		return relativePath.replace(/\\/g, "/"); // Normalize to forward slashes
	},

	toDiskPath: (dbPath: string) => {
		return path.join(storageDir, dbPath);
	},

	toPublicUrl: (dbPath: string) => {
		if (!dbPath.startsWith("public/")) {
			throw new Error("Only public files can generate URL");
		}
		// Remove 'public/' prefix to get clean URL
		// "public/uploads/users/xxx/file.jpg" → "/uploads/users/xxx/file.jpg"
		// "public/assets/style.css" → "/assets/style.css"
		return `/${dbPath.replace("public/", "")}`;
	},

	toPrivateUrl: (dbPath: string) => {
		if (!dbPath.startsWith("private/")) {
			throw new Error("Only private files can generate signed URL");
		}
		// Remove 'private/' prefix and add '/secure-files' prefix
		// "private/uploads/users/xxx/file.jpg" → "/secure-files/uploads/users/xxx/file.jpg"
		return `/secure-files/${dbPath.replace("private/", "")}`;
	},

	// Helper to determine if path is public or private
	isPublicPath: (dbPath: string) => {
		return dbPath.startsWith("public/");
	},

	isPrivatePath: (dbPath: string) => {
		return dbPath.startsWith("private/");
	},

	// Generate appropriate URL based on file type
	toUrl: (dbPath: string) => {
		if (PATHS.isPublicPath(dbPath)) {
			return PATHS.toPublicUrl(dbPath);
		}
		if (PATHS.isPrivatePath(dbPath)) {
			return PATHS.toPrivateUrl(dbPath);
		}
		throw new Error("Invalid file path format");
	},
};

// Buat folder struktur jika belum ada
function ensureDirectories() {
	const dirs = [PATHS.PUBLIC.UPLOADS, PATHS.PRIVATE.UPLOADS];

	for (const dir of dirs) {
		if (!fs.existsSync(dir)) {
			fs.mkdirSync(dir, { recursive: true });
		}
	}
}

ensureDirectories();
