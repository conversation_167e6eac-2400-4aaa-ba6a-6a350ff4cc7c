import jwt from "jsonwebtoken";
import type { StringValue } from "ms";

/**
 * Interface for token generation options
 * @interface TokenOptions
 * @extends {jwt.SignOptions}
 */
interface TokenOptions extends jwt.SignOptions {
  /** Secret key used for token signing */
  secret: string;
  /** Token expiration time in string format (e.g., '1h', '7d') */
  expiresIn: StringValue;
}

/**
 * Generates a JWT token
 * @template T
 * @param {T} payload - Data to be encoded in the token
 * @param {TokenOptions} options - Token generation options
 * @returns {string} Generated JWT token
 */
export const generateToken = <T extends object>(payload: T, options: TokenOptions): string => {
  const { secret, ...signOptions } = options;
  return jwt.sign(payload, secret, signOptions);
};

/**import { useId } from "react";
import type { Column } from "@tanstack/react-table";
import { Check, PlusCircle } from "lucide-react";

import { cn } from "@/lib/utils";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
	CommandSeparator,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";

interface DataTableFacetedFilterProps<TData, TValue> {
	column?: Column<TData, TValue>;
	title?: string;
	type: "checkbox" | "date" | "range";
	options?: {
		label: string;
		value: string;
		icon?: React.ComponentType<{ className?: string }>;
	}[];
}

export function DataTableFacetedFilter<TData, TValue>({
	column,
	title,
	options,
	type,
}: DataTableFacetedFilterProps<TData, TValue>) {
	const id = useId();
	const facets = column?.getFacetedUniqueValues();
	const selectedValues = new Set(column?.getFilterValue() as string[]);

	const renderCheckboxFilter = () => {
		if (!options?.length) {
			throw new Error("Checkbox filter must have an options");
		}

		return (
			<Command>
				<CommandInput placeholder={title} />
				<CommandList>
					<CommandEmpty>No results found.</CommandEmpty>
					<CommandGroup>
						{options.map((option) => {
							const isSelected = selectedValues.has(option.value);
							return (
								<CommandItem
									key={option.value}
									onSelect={() => {
										if (isSelected) {
											selectedValues.delete(option.value);
										} else {
											selectedValues.add(option.value);
										}
										const filterValues = Array.from(selectedValues);
										column?.setFilterValue(
											filterValues.length ? filterValues : undefined,
										);
									}}
								>
									<div
										className={cn(
											"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
											isSelected
												? "bg-primary text-primary-foreground"
												: "opacity-50 [&_svg]:invisible",
										)}
									>
										<Check />
									</div>
									{option.icon && (
										<option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
									)}
									<span>{option.label}</span>
									{facets?.get(option.value) && (
										<span className="ml-auto flex h-4 w-4 items-center justify-center font-mono text-xs">
											{facets.get(option.value)}
										</span>
									)}
								</CommandItem>
							);
						})}
					</CommandGroup>
					{selectedValues.size > 0 && (
						<>
							<CommandSeparator />
							<CommandGroup>
								<CommandItem
									onSelect={() => column?.setFilterValue(undefined)}
									className="justify-center text-center"
								>
									Clear filters
								</CommandItem>
							</CommandGroup>
						</>
					)}
				</CommandList>
			</Command>
		);
	};

	const renderRangeFilter = () => {
		const currentValue = (column?.getFilterValue() as [string, string]) || [
			"",
			"",
		];

		return (
			<div className="p-4">
				<label htmlFor={`${id}-min`} className="block text-sm font-medium">
					Min:
				</label>
				<Input
					type="number"
					defaultValue={currentValue[0]}
					onChange={(e) => {
						column?.setFilterValue((old: [string, string]) => [
							e.target.value,
							old?.[1],
						]);
					}}
				/>
				<label htmlFor={`${id}-min`} className="block text-sm font-medium mt-2">
					Max:
				</label>
				<Input
					type="number"
					defaultValue={currentValue[1]}
					onChange={(e) => {
						column?.setFilterValue((old: [string, string]) => [
							old?.[0],
							e.target.value,
						]);
					}}
				/>
			</div>
		);
	};

	const renderDateFilter = () => {
		const currentValue = (column?.getFilterValue() as [string, string]) || [
			"",
			"",
		];

		return (
			<div className="p-4">
				<label htmlFor={`${id}-date`} className="block text-sm font-medium">
					From:
				</label>
				<Input
					type="date"
					defaultValue={currentValue[0]}
					onChange={(e) => {
						column?.setFilterValue((old: [string, string]) => [
							e.target.value,
							old?.[1],
						]);
					}}
				/>
				<label htmlFor="" className="block text-sm font-medium mt-2">
					To:
				</label>
				<Input
					type="date"
					defaultValue={currentValue[1]}
					onChange={(e) => {
						column?.setFilterValue((old: [string, string]) => [
							old?.[0],
							e.target.value,
						]);
					}}
				/>
			</div>
		);
	};

	return (
		<Popover>
			<PopoverTrigger asChild>
				<Button variant="outline" size="sm" className="h-8 border-dashed">
					<PlusCircle />
					{title}
					{type === "checkbox" &&
						options?.length &&
						selectedValues?.size > 0 && (
							<>
								<Separator orientation="vertical" className="mx-2 h-4" />
								<Badge
									variant="secondary"
									className="rounded-sm px-1 font-normal lg:hidden"
								>
									{selectedValues.size}
								</Badge>
								<div className="hidden space-x-1 lg:flex">
									{selectedValues.size > 1 ? (
										<Badge
											variant="secondary"
											className="rounded-sm px-1 font-normal"
										>
											{selectedValues.size} selected
										</Badge>
									) : (
										options
											.filter((option) => selectedValues.has(option.value))
											.map((option) => (
												<Badge
													variant="secondary"
													key={option.value}
													className="rounded-sm px-1 font-normal"
												>
													{option.label}
												</Badge>
											))
									)}
								</div>
							</>
						)}
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[200px] p-0" align="start">
				{type === "checkbox" && renderCheckboxFilter()}
				{type === "date" && renderDateFilter()}
				{type === "range" && renderRangeFilter()}
			</PopoverContent>
		</Popover>
	);
}
 * Verifies a JWT token
 * @template T
 * @param {string} token - JWT token to verify
 * @param {string} secret - Secret key used to verify the token
 * @param {jwt.VerifyOptions} [options] - Verification options
 * @returns {T | null} Decoded token payload or null if verification fails
 */
export const verifyToken = <T extends object>(
  token: string,
  secret: string,
  options?: jwt.VerifyOptions
): T | null => {
  try {
    return jwt.verify(token, secret, options) as T;
  } catch (error) {
    console.error("Token verification failed:", error);
    return null;
  }
};

/**
 * Decodes a JWT token without verification
 * @template T
 * @param {string} token - JWT token to decode
 * @param {jwt.DecodeOptions} [options] - Decoding options
 * @returns {T | null} Decoded token payload or null if decoding fails
 */
export const decodeToken = <T extends object>(token: string, options?: jwt.DecodeOptions): T | null => {
  try {
    return jwt.decode(token, options) as T;
  } catch (error) {
    console.error("Token decoding failed:", error);
    return null;
  }
};
