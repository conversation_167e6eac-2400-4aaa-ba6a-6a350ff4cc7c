import crypto from "node:crypto";

// Comprehensive file type mapping
export const fileTypeLabels: Record<string, string> = {
	// Images
	"image/jpeg": "JPEG",
	"image/jpg": "JPEG",
	"image/png": "PNG",
	"image/gif": "GIF",
	"image/webp": "WebP",
	"image/svg+xml": "SVG",
	"image/bmp": "BMP",
	"image/tiff": "TIFF",

	// Videos
	"video/mp4": "MP4",
	"video/x-msvideo": "AVI",
	"video/mpeg": "MPEG",
	"video/quicktime": "MOV",
	"video/webm": "WebM",
	"video/x-matroska": "MKV",

	// Documents
	"application/pdf": "PDF",
	"application/msword": "DOC",
	"application/vnd.openxmlformats-officedocument.wordprocessingml.document":
		"DOCX",
	"application/vnd.ms-excel": "XLS",
	"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "XLSX",
	"application/vnd.ms-powerpoint": "PPT",
	"application/vnd.openxmlformats-officedocument.presentationml.presentation":
		"PPTX",

	// Archives
	"application/zip": "ZIP",
	"application/x-rar-compressed": "RAR",
	"application/x-7z-compressed": "7Z",

	// Audio
	"audio/mpeg": "MP3",
	"audio/wav": "WAV",
	"audio/ogg": "OGG",
	"audio/midi": "MIDI",

	// Other
	"text/plain": "TXT",
	"text/csv": "CSV",
	"application/json": "JSON",
	"text/html": "HTML",
};

/**
 * Converts MIME types to human-readable format
 * @param allowedMimeTypes Array of allowed MIME types
 * @returns Comma-separated string of file type labels
 */
export const getAllowedFileTypeLabels = (
	allowedMimeTypes: string[],
): string => {
	return allowedMimeTypes
		.map((type) => fileTypeLabels[type] || type)
		.filter((label) => label) // Remove any undefined values
		.join(", ");
};

const SIGN_SECRET = process.env.SECURE_FILE_SECRET || "supersecret"; 

export function signPath(filePath: string, expiresInSec: number = 60 * 5) {
	const expires = Math.floor(Date.now() / 1000) + expiresInSec; // 5 menit default
	const unsignedUrl = `${filePath}?expires=${expires}`;
	const signature = createHmac(unsignedUrl);
	return `${unsignedUrl}&signature=${signature}`;
}

export function createHmac(data: string) {
	return crypto.createHmac("sha256", SIGN_SECRET).update(data).digest("hex");
}

export function verifySignature(
	fullPath: string,
	expires: string,
	signature: string,
) {
	const now = Math.floor(Date.now() / 1000);
	if (Number(expires) < now) return false;

	const expectedSig = createHmac(`${fullPath}?expires=${expires}`);
	console.log(expectedSig, 'EXPECTED');
	console.log(signature, 'SIGN');
	return crypto.timingSafeEqual(
		Buffer.from(expectedSig),
		Buffer.from(signature),
	);
}
