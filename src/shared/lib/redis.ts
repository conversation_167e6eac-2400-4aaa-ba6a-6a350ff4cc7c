import Redis from "ioredis";
import { Env } from "@/shared/config/env.config";

export const cacheClient = new Redis({
	host: Env.REDIS_HOST,
	port: Env.REDIS_PORT,
	password: Env.REDIS_PASSWORD,
});

cacheClient.on("error", (error) => {
	console.error("Redis connection error:", error);
});

cacheClient.on("connect", () => {
	console.log("✅ Connected to redis.");
});

cacheClient.on("reconnecting", (time: number) => {
	console.log(`Redis reconnecting in ${time} ms...`);
});

cacheClient.on("end", () => {
	console.log("Redis connection closed.");
});

// Cache TTL constants (in seconds)
export const CACHE_TTL = {
	ADMIN_SESSION: 300,
	USER_SESSION: 300, // 5 minutes
	FACE_VECTORS: 3600, // 1 hour
	WORK_SITE: 1800, // 30 minutes
	ATTENDANCE_CONFIG: 60, // 1 minute
	HOLIDAY_CHECK: 3600, // 1 hour
	LEAVE_CHECK: 300, // 5 minutes
	ATTENDANCE_RULES: undefined, // 30 minutes
	KPI_POINTS: undefined, // Never expire
	INCENTIVE: undefined, // Never expire
} as const;

// Cache key generators
export const CACHE_KEYS = {
	userSession: (userId: string) => `user_session:${userId}`,
	adminSession: (adminId: string) => `admin_session:${adminId}`,
	faceVectors: (userId: string) => `face_vectors:${userId}`,
	worksite: (userId: string) => `work_site:${userId}`,
	attendanceConfig: (userId: string, date: string) =>
		`attendance_config:${userId}:${date}`,
	holidayCheck: (date: string) => `holiday:${date}`,
	leaveCheck: (userId: string, date: string) => `leave:${userId}:${date}`,
	attendanceRule: (worksiteId: string) => `attendance_rule:${worksiteId}`,
	kpiPoints: (userId: string, period: string) => `kpi:${userId}:${period}`,
	incentive: (userId: string, incentiveType: string, period: string) =>
		`incentive:${userId}:${incentiveType}:${period}`,
} as const;

// Cache helper functions
export const cacheHelpers = {
	async get<T>(key: string): Promise<T | null> {
		try {
			const data = await cacheClient.get(key);
			return data ? JSON.parse(data) : null;
		} catch (error) {
			console.error(`Cache get error for key ${key}:`, error);
			return null;
		}
	},

	async set<T>(key: string, value: T, ttl?: number): Promise<void> {
		try {
			if (typeof ttl === "number") {
				await cacheClient.setex(key, ttl, JSON.stringify(value));
			} else {
				await cacheClient.set(key, JSON.stringify(value)); // no TTL = never expires
			}
		} catch (error) {
			console.error(`Cache set error for key ${key}:`, error);
		}
	},

	async del(key: string): Promise<void> {
		try {
			await cacheClient.del(key);
		} catch (error) {
			console.error(`Cache delete error for key ${key}:`, error);
		}
	},

	async invalidatePattern(pattern: string): Promise<void> {
		try {
			const keys = await cacheClient.keys(pattern);
			if (keys.length > 0) {
				await cacheClient.del(...keys);
			}
		} catch (error) {
			console.error(`Cache invalidate pattern error for ${pattern}:`, error);
		}
	},
};
