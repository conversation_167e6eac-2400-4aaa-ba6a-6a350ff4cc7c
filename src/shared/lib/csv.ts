import { format } from "fast-csv";

export const createCsv = <T>(records: T[]): Promise<string> => {
	return new Promise((resolve, reject) => {
		const rows: string[] = [];

		// Pastikan ada data untuk diexport
		if (records.length > 0) {
			// Menentukan header dengan mengambil key dari objek pertama
			const headers = Object.keys((records as Record<string, any>)?.[0]);

			// Format objek records menjadi array of arrays untuk setiap baris data CSV
			const formattedRecords = records.map((record) => {
				return headers.map((header) => {
					const value = record?.[header as keyof T];

					if (value instanceof Date) {
						return value.toISOString();
					}

					if (typeof value === "object") {
						return JSON.stringify(value);
					}

					return value;
				});
			});

			const writeStream = format({ headers: true })
				.on("data", (chunk) => rows.push(chunk.toString()))
				.on("end", () => resolve(rows.join("")))
				.on("error", reject);

			writeStream.write(headers);
			formattedRecords.forEach((record) => {
				writeStream.write(record);
			});

			writeStream.end();
		} else {
			reject("No data to export");
		}
	});
};
