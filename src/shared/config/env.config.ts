import path from "node:path";
import { config } from "dotenv";
import * as z from "zod";

const NODE_ENV = process.env.NODE_ENV ?? "development";

// Tentukan file env sesuai NODE_ENV
const envFilePath = path.resolve(process.cwd(), `.env.${NODE_ENV}`);

// Load file env
config({ path: envFilePath });

// Schema untuk validasi environment variables
const envSchema = z.object({
	// APP
	FRONTEND_URL: z.string().nonempty(),
	SERVER_URL: z.string().nonempty(),
	PRODUCTION_URL: z.string().nonempty(),
	STAGING_URL: z.string().nonempty(),
	NODE_ENV: z
		.enum(["development", "production", "test"])
		.default("development"),
	PORT: z.string().default("9000").transform(Number),
	COMPANY_MAIL: z.string().nonempty(),

	PRESIDENT_DIRECTOR_ID: z.string().nonempty(),
	PRESIDENT_DIRECTOR_ROLE_ID: z.string().nonempty(),
	HR_ROLE_ID: z.string().nonempty(),
	SYSTEM_ADMINISTRATOR_ROLE_ID: z.string().nonempty(),
	ALL_PERMISSION_ID: z.string().nonempty(),

	DEFAULT_USER_IMAGE: z.string().nonempty(),
	DEFAULT_USER_PASSWORD: z.string().nonempty(),
	DEFAULT_ADMIN_PASSWORD: z.string().nonempty(),

	// DATABASE
	DATABASE_URI: z.string().nonempty(),
	DB_USERNAME: z.string().nonempty(),
	DB_PASSWORD: z.string().nonempty(),
	DB_NAME: z.string().nonempty(),
	DB_HOST: z.string().nonempty(),
	DB_PORT: z.string().default("5432").transform(Number),

	// MAIL
	RESEND_API_KEY: z.string().nonempty(),

	// CACHE
	REDIS_HOST: z.string().nonempty(),
	REDIS_PASSWORD: z.string().nonempty(),
	REDIS_PORT: z.string().nonempty().transform(Number),

	// AMQP
	AMQP_URI: z.string().nonempty(),

	// TOKENIZE
	ACCESS_TOKEN_SECRET: z.string().nonempty(),
	REFRESH_TOKEN_SECRET: z.string().nonempty(),
	FORGOT_PASSWORD_TOKEN_SECRET: z.string().nonempty(),
	ACCESS_TOKEN_EXPIRATION: z.string().nonempty(),
	REFRESH_TOKEN_EXPIRATION: z.string().nonempty(),
	FORGOT_PASSWORD_TOKEN_EXPIRATION: z.string().nonempty(),
	COOKIE_ACCESS_TOKEN_EXPIRATION: z.string().transform(Number),
	COOKIE_REFRESH_TOKEN_EXPIRATION: z.string().transform(Number),

	// SENTRY CONFIGURATION
	SENTRY_DSN: z.string(),
	SENTRY_ENVIRONMENT: z.string().default("development"),
	SENTRY_RELEASE: z.string(),
	SENTRY_TRACES_SAMPLE_RATE: z.coerce.number().min(0).max(1).default(0.1),
	SENTRY_PROFILES_SAMPLE_RATE: z.coerce.number().min(0).max(1).default(0.1),

	// SECURITY & CORS
	ALLOWED_ORIGINS: z.string().optional(),
	TRUST_PROXY: z.string().default("1").transform(Number),
	CSP_ENABLED: z
		.string()
		.default("true")
		.transform((val) => val === "true"),

	// RATE LIMITING
	RATE_LIMIT_WINDOW_MS: z.string().default("60000").transform(Number),
	RATE_LIMIT_MAX_REQUESTS: z.string().default("60").transform(Number),
	RATE_LIMIT_MAX_REQUESTS_DEV: z.string().default("100").transform(Number),

	// FILE
	SECURE_FILE_SECRET: z.string().nonempty(),
});

// Type untuk environment variables yang sudah divalidasi
type EnvVariables = z.infer<typeof envSchema>;

// Fungsi untuk memuat dan memvalidasi environment variables
function loadEnv(): EnvVariables {
	try {
		const env = envSchema.parse(process.env);
		return env;
	} catch (error) {
		if (error instanceof z.ZodError) {
			console.error("❌ Invalid environment variables:");
			error.issues.forEach((err) => {
				console.error(`  - ${err.path.join(".")}: ${err.message}`);
			});
			process.exit(1);
		}
		throw error;
	}
}

// Memuat environment variables yang sudah divalidasi
export const Env = loadEnv();
