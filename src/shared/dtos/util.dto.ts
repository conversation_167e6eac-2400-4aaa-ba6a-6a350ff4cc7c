import * as z from "zod";

export type SortItem = { field: string; dir: "asc" | "desc" };

export const responseMetaDTO = z.object({
	pagination: z
		.object({
			pageIndex: z.number().min(1).meta({
				example: 1,
				description: "Current page number",
				minimum: 1,
			}),
			pageSize: z.number().min(1).meta({
				example: 10,
				description: "Number of items per page",
				minimum: 1,
			}),
			totalItems: z.number().min(0).meta({
				example: 100,
				description: "Total number of items available",
				minimum: 0,
			}),
			totalPages: z.number().min(0).meta({
				example: 10,
				description: "Total number of pages available",
				minimum: 0,
			}),
			hasNextPage: z.boolean(),
			hasPreviousPage: z.boolean(),
		})
		.meta({ description: "Pagination information" }),
});
export type ResponseMetaDTO = z.infer<typeof responseMetaDTO>;

export function createSortByParam(allowedFields: string[]) {
	const directions = ["asc", "desc"];
	const examples = allowedFields.flatMap((f) =>
		directions.map((dir) => `${f}.${dir}`),
	);

	return z
		.string()
		.optional()
		.meta({
			description: `Comma-separated sorting (e.g. ${examples.slice(0, 2).join(",")}). Allowed fields: ${allowedFields.join(", ")}`,
			example: examples.slice(0, 2).join(","),
		})
		.transform((val) => {
			if (!val) return [];

			return val.split(",").map((s) => {
				const [field, dir] = s.split(".");
				return {
					field,
					dir: dir === "desc" ? "desc" : "asc",
				} as SortItem;
			});
		});
}

export const makeValidationErrorFields = <T extends z.ZodRawShape>(shape: T) =>
	z.object(
		Object.fromEntries(
			Object.entries(shape).map(([key]) => [
				key,
				z.array(z.string()).optional(),
			]),
		) as Record<keyof T, z.ZodOptional<z.ZodArray<z.ZodString>>>,
	);

export const baseResponseDTO = z.object({
	status: z.literal("success").meta({
		example: "success",
		description: "Result status of the API call",
	}),
	message: z.string().meta({
		example: "Operation completed successfully.",
		description: "Descriptive message for the API result",
	}),
	data: z.any().meta({
		description: "Optional data object returned by the API",
	}),
});

export const basePaginationResponseDTO = z.object({
	status: z.literal("success").meta({
		example: "success",
		description: "Result status of the API call",
	}),
	message: z.string().meta({
		example: "Operation completed successfully.",
		description: "Descriptive message for the API result",
	}),
	data: z.array(z.any()).meta({
		description: "Array of paginated items returned by the API",
	}),
	meta: responseMetaDTO.meta({
		description: "Metadata information",
	}),
});

export const baseErrorResponseDTO = z.object({
	status: z.literal("failed").meta({
		example: "failed",
		description: "Result status indicating failure",
	}),
	message: z.string().meta({
		example: "Validation failed",
		description: "Error message",
	}),
	errors: z.record(z.string(), z.any()).optional().meta({
		description: "Detailed validation errors, if any",
	}),
});
