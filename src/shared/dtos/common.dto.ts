import "@/shared/lib/zod-extensions";
import * as z from "zod";
import { baseResponseDTO } from "./util.dto";

export const timestampDTO = z.object({
	createdAt: z.iso.datetime().meta({
		description: "Creation timestamp",
		example: "2025-01-15T08:30:15.000Z",
	}),
	updatedAt: z.iso.datetime().meta({
		description: "Last update timestamp",
		example: "2025-01-15T08:30:15.000Z",
	}),
});
export type TimestampDTO = z.infer<typeof timestampDTO>;

export const optionItemDTO = z.object({
	label: z.string().meta({}),
	value: z.union([z.string(), z.number()]).meta({}),
});
export type OptionItemDTO = z.infer<typeof optionItemDTO>;

export const bulkActionDTO = z.object({
	action: z.enum(["delete", "export", "update"]),
	selection: z.object({
		isSelectAll: z.boolean(),
		selectedIds: z.array(z.string()).optional(),
		excludedIds: z.array(z.string()).optional(),
	}),
	queryString: z.string().optional(),
});
export type BulkActionDTO = z.infer<typeof bulkActionDTO>;

export const tableQueryDTO = z.object({
	pageIndex: z.coerce.number().min(0).default(0).meta({ example: 0 }),
	pageSize: z.coerce.number().min(1).max(100).default(10).meta({ example: 10 }),
	search: z.string().optional().meta({ example: "john" }),
	sort: z.string().optional().meta({ example: "name.asc" }),
});

export const filterValueSchema = z.union([
	z.string(), // key
	z
		.string()
		.regex(/^(eq|in|lte|gte|ilike):.+$/), // value
]);

export const filterFieldDTO = z.union([
	filterValueSchema,
	z.array(filterValueSchema),
]);

export const treeNodeDTO = z
	.object({
		name: z.string().meta({
			example: "John Doe",
			description: "User full name",
		}),
		attributes: z.record(z.string(), z.any()),
		children: z.array(z.record(z.string(), z.any())).optional(),
	})
	.openapi("TreeNodeDTO");
export type TreeNodeDTO = z.infer<typeof treeNodeDTO>;

export const getTreeNodeResponseDTO = baseResponseDTO
	.extend({
		data: z.array(treeNodeDTO),
	})
	.openapi("GetTreeNodeResponseDTO");
export type GetTreeNodeResponseDTO = z.infer<typeof getTreeNodeResponseDTO>;
