import dayjs from "dayjs";
import type { SelectQueryBuilder } from "kysely";
import { BadRequestError } from "../exceptions";
import { parseOperator } from "./parse-operator";

const ALLOWED_OPERATORS = {
	string: ["eq", "in", "ilike"] as const,
	boolean: ["eq", "in"] as const,
	date: ["eq", "gte", "lte"] as const,
	number: ["eq", "in", "gte", "lte"] as const,
} as const;

export type FieldType = keyof typeof ALLOWED_OPERATORS;
export type FilterableFields = Record<string, string>; 
export type FieldTypes = Record<string, FieldType>;
export type SortableFields = FilterableFields;

interface FilterConfig {
	field: string;
	values: string | string[];
}

// Generic function untuk validasi dan parsing filter
export function validateAndParseFilter(
	field: string,
	values: string | string[],
	filterableFields: FilterableFields,
	fieldTypes: FieldTypes,
): FilterConfig {
	// Validasi field
	if (!(field in filterableFields)) {
		throw new Error(
			`Invalid filter field: ${field}. Allowed fields: ${Object.keys(filterableFields).join(", ")}`,
		);
	}

	const fieldType = fieldTypes[field];
	if (!fieldType) {
		throw new Error(`Field type not defined for field: ${field}`);
	}

	const allowedOps = ALLOWED_OPERATORS[fieldType];

	// Validasi operator untuk setiap value
	const valueArray = Array.isArray(values) ? values : [values];

	for (const value of valueArray) {
		const { op } = parseOperator(value);
		if (!allowedOps.includes(op as any)) {
			throw new Error(
				`Operator '${op}' is not allowed for field '${field}' of type '${fieldType}'. Allowed operators: ${allowedOps.join(", ")}`,
			);
		}
	}

	return {
		field,
		values,
	};
}

// Generic function untuk mengkonversi value berdasarkan tipe
function convertValue(value: string, fieldType: FieldType): any {
	switch (fieldType) {
		case "boolean":
			return value === "true";
		case "date":
			return dayjs(value).isValid() ? dayjs(value).toDate() : dayjs().toDate();
		case "number":
			return Number(value);
		default:
			return value;
	}
}

// Generic function untuk apply filter
export function applyFilter<DB, _TB extends keyof DB>(
	query: any,
	filterConfig: FilterConfig,
	filterableFields: FilterableFields,
	fieldTypes: FieldTypes,
	// _eb?: ExpressionBuilder<DB, TB>,
): any {
	const { field, values } = filterConfig;
	const dbField = filterableFields[field];
	const fieldType = fieldTypes[field];
	const valueArray = Array.isArray(values) ? values : [values];

	let filteredQuery = query;

	for (const value of valueArray) {
		const { op, value: filterValue } = parseOperator(value);

		switch (op) {
			case "eq": {
				const eqValue = convertValue(
					filterValue as string,
					fieldType as FieldType,
				);
				if (fieldType === "string") {
					filteredQuery = filteredQuery.where(dbField, "ilike", `%${eqValue}%`);
				} else {
					filteredQuery = filteredQuery.where(dbField, "=", eqValue);
				}
				break;
			}

			case "in": {
				const inValues = (filterValue as string[]).map((v) =>
					convertValue(v, fieldType as FieldType),
				);
				filteredQuery = filteredQuery.where(dbField, "in", inValues);
				break;
			}

			case "ilike":
				filteredQuery = filteredQuery.where(
					dbField,
					"ilike",
					`%${filterValue}%`,
				);
				break;

			case "gte": {
				const gteValue = convertValue(
					filterValue as string,
					fieldType as FieldType,
				);
				filteredQuery = filteredQuery.where(dbField, ">=", gteValue);
				break;
			}

			case "lte": {
				const lteValue = convertValue(
					filterValue as string,
					fieldType as FieldType,
				);
				filteredQuery = filteredQuery.where(dbField, "<=", lteValue);
				break;
			}
		}
	}

	return filteredQuery;
}

// Generic function untuk apply multiple filters
export function applyFilters<DB, TB extends keyof DB>({
	query,
	countQuery,
	filters,
	filterableFields,
	fieldTypes,
	// eb,
}: {
	query: SelectQueryBuilder<DB, TB, any>;
	countQuery?: SelectQueryBuilder<DB, TB, any>;
	filters: Record<string, string | string[]>;
	filterableFields: FilterableFields;
	fieldTypes: FieldTypes;
	// eb?: ExpressionBuilder<DB, TB>;
}): {
	query: SelectQueryBuilder<DB, TB, any>;
	countQuery?: SelectQueryBuilder<DB, TB, any> | undefined;
} {
	let filteredQuery = query;
	let filteredCountQuery = countQuery;

	for (const [field, values] of Object.entries(filters)) {
		if (values) {
			try {
				const filterConfig = validateAndParseFilter(
					field,
					values,
					filterableFields,
					fieldTypes,
				);
				filteredQuery = applyFilter(
					filteredQuery,
					filterConfig,
					filterableFields,
					fieldTypes,
					// eb,
				);
				if (filteredCountQuery) {
					filteredCountQuery = applyFilter(
						filteredCountQuery,
						filterConfig,
						filterableFields,
						fieldTypes,
						// eb,
					);
				}
			} catch (error) {
				throw new BadRequestError(
					`Filter validation failed for field '${field}': ${(error as any)?.message}`,
				);
			}
		}
	}

	return {
		query: filteredQuery,
		countQuery: filteredCountQuery ?? undefined,
	};
}

// Generic function untuk validasi sort fields
export function validateAndParseSortFields(
	sort: string,
	sortableFields: FilterableFields,
): Array<{ field: string; direction: "asc" | "desc" }> {
	const sortFields: Array<{ field: string; direction: "asc" | "desc" }> = [];

	const pairs = sort.split(",");

	for (const pair of pairs) {
		const [field, dir] = pair.split(":") as [string, string];

		// Validasi field
		if (!(field in sortableFields)) {
			throw new BadRequestError(
				`Invalid sort field: ${field}. Allowed fields: ${Object.keys(sortableFields).join(", ")}`,
			);
		}

		// Validasi direction
		const direction = dir === "desc" ? "desc" : "asc";

		sortFields.push({
			field: sortableFields[field] as string,
			direction,
		});
	}

	return sortFields;
}

// Generic function untuk apply sorting
export function applySorting<DB, TB extends keyof DB>({
	query,
	sort,
	sortableFields,
	defaultSort = {
		field: "createdAt",
		direction: "desc",
	},
}: {
	query: SelectQueryBuilder<DB, TB, any>;
	sort: string | undefined;
	sortableFields: FilterableFields;
	defaultSort?: { field: string; direction: "asc" | "desc" };
}): SelectQueryBuilder<DB, TB, any> {
	let sortedQuery = query;

	if (sort) {
		const sortFields = validateAndParseSortFields(sort, sortableFields);
		sortFields.forEach(({ field, direction }) => {
			sortedQuery = sortedQuery.orderBy(field as any, direction);
		});
	} else {
		const defaultField = sortableFields[defaultSort.field] || defaultSort.field;
		sortedQuery = sortedQuery.orderBy(
			defaultField as any,
			defaultSort.direction,
		);
	}

	return sortedQuery;
}

// Generic function untuk create validation refine
export function createValidationRefine(
	filterableFields: FilterableFields,
	fieldTypes: FieldTypes,
	sortableFields: FilterableFields,
) {
	return (data: any) => {
		// Validasi sort
		if (data.sort) {
			try {
				validateAndParseSortFields(data.sort, sortableFields);
			} catch (error) {
				throw new Error(`Sort validation failed: ${(error as any)?.message}`);
			}
		}

		// Validasi filter
		const filterFields = Object.keys(filterableFields);
		for (const field of filterFields) {
			if (data[field]) {
				try {
					validateAndParseFilter(
						field,
						data[field],
						filterableFields,
						fieldTypes,
					);
				} catch (error) {
					throw new Error(
						`Filter validation failed: ${(error as any)?.message}`,
					);
				}
			}
		}

		return true;
	};
}

// Generic utility untuk generate options (untuk frontend)
export function getFilterOptions(
	filterableFields: FilterableFields,
	fieldTypes: FieldTypes,
	sortableFields: FilterableFields,
) {
	return {
		sortableFields: Object.keys(sortableFields),
		filterableFields: Object.keys(filterableFields),
		fieldTypes: fieldTypes,
		allowedOperators: ALLOWED_OPERATORS,
	};
}
