export function parseOperator(raw: string): {
	op: "eq" | "in" | "gte" | "lte" | "ilike";
	value: string | string[];
} {
	if (raw.startsWith("gte:")) return { op: "gte", value: raw.slice(4) };
	if (raw.startsWith("lte:")) return { op: "lte", value: raw.slice(4) };
	if (raw.startsWith("eq:")) return { op: "eq", value: raw.slice(3) };
	if (raw.startsWith("ilike:")) return { op: "ilike", value: raw.slice(6) };
	if (raw.startsWith("in:")) return { op: "in", value: raw.slice(3).split(",") };
	return { op: "eq", value: raw }; // default = eq
}
