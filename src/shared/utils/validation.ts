import type { ZodError } from "zod";
import type { Locale } from "../constants";

const translations: Record<
	Locale,
	{
		at: string;
		prefix: string;
		separator: string;
	}
> = {
	id: {
		at: "pada",
		prefix: "Validasi gagal",
		separator: "; ",
	},
	en: {
		at: "at",
		prefix: "Validation error",
		separator: "; ",
	},
	fr: {
		at: "à",
		prefix: "Erreur de validation",
		separator: "; ",
	},
	de: {
		at: "bei",
		prefix: "Validierungsfehler",
		separator: "; ",
	},
	es: {
		at: "en",
		prefix: "Error de validación",
		separator: "; ",
	},
	ja: {
		at: "の",
		prefix: "バリデーションエラー",
		separator: "；",
	},
	ko: {
		at: "의",
		prefix: "유효성 검사 오류",
		separator: "; ",
	},
	zh: {
		at: "在",
		prefix: "验证错误",
		separator: "；",
	},
	pt: {
		at: "em",
		prefix: "Erro de validação",
		separator: "; ",
	},
	ru: {
		at: "в",
		prefix: "Ошибка проверки",
		separator: "; ",
	},
	ar: {
		at: "في",
		prefix: "خطأ في التحقق",
		separator: "؛ ",
	},
	th: {
		at: "ที่",
		prefix: "ข้อผิดพลาดในการตรวจสอบ",
		separator: "; ",
	},
	vi: {
		at: "tại",
		prefix: "Lỗi xác thực",
		separator: "; ",
	},
};

/**
 * Format ZodError to a readable localized string.
 * Contoh (id): Validasi gagal: Harus berupa angka positif pada "id"; Email tidak valid pada "email"
 */
export function formatZodError(error: ZodError, locale: Locale = "en"): string {
	const t = translations[locale] || translations.en;

	const formatted = error.issues
		.map((err) => {
			const path = err.path.join(".");
			return `${err.message} ${t.at} "${path}"`;
		})
		.join(t.separator);

	return `${t.prefix}: ${formatted}`;
}
