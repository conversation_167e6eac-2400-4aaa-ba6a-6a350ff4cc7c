import type { NextFunction, Request, Response } from "express";
import passport from "passport";
import type { AuthStrategyType } from "@/shared/enums";
import type { AuthUserWithTokenPayload } from "../types";

export function optionalAuthMiddleware(strategyName: AuthStrategyType) {
	return (req: Request, res: Response, next: NextFunction) => {
		passport.authenticate(strategyName, { session: false }, (_err: any, data: AuthUserWithTokenPayload) => {
			if (data) {
				req.user = data.user;
				req.tokenPayload = data.tokenPayload;
			}

			return next();
		})(req, res, next);
	};
}
