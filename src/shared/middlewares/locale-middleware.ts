import type { NextFunction, Request, Response } from "express";
import * as z from "zod";
import type { Locale } from "../constants";
import { localeMap } from "../constants";

async function loadLocale(locale: string): Promise<void> {
	try {
		const localeModule = await import(`zod/v4/locales/${locale}.js`);
		z.config(localeModule.default());
		console.log(`Using ${locale} internalization`);
	} catch {
		console.log("Using callback en internalization");
		z.config(z.locales.en());
	}
}

export const localeMiddleware = async (
	req: Request,
	_res: Response,
	next: NextFunction,
): Promise<void> => {
	const requestedLocale =
		// req.headers["accept-language"]?.split(",")[0]?.split("-")[0] ||
		// (req.query.lang as string) ||
		"id";

	const localeKey = (
		requestedLocale in localeMap ? requestedLocale : "id"
	) as Locale;

	const zodLocale = localeMap[localeKey];

	try {
		await loadLocale(zodLocale);
		req.locale = localeKey;
	} catch {
		req.locale = "id";
	}

	next();
};
