import type { NextFunction, Request, Response } from "express";
import passport from "passport";
import type { AuthStrategyType } from "@/shared/enums";
import { UnauthorizedError } from "@/shared/exceptions";
import { setUserContext } from "@/shared/lib/sentry";
import type { AuthUserWithTokenPayload } from "@/shared/types";

/**
 * Authentication middleware factory that enforces JWT token validation using specified strategy.
 *
 * @param strategyName - The authentication strategy to use from AuthStrategy enum
 * @returns Express middleware function
 *
 * @description
 * This middleware factory creates authentication middleware that:
 * - Validates JWT token from Authorization header using the specified strategy
 * - Sets user context for Sentry error tracking
 * - Assigns authenticated user to request
 * - Supports multiple authentication strategies with type safety and autocomplete
 *
 * @throws {UnauthorizedError}
 * - When JWT token is invalid or missing
 * - When user authentication fails for the specified strategy
 *
 * @example
 * import { AuthStrategy } from '@/app/enums/auth-strategy.enum';
 *
 * // For regular user authentication (with autocomplete)
 * app.use('/api/user', authMiddleware(AuthStrategy.JWT_USER));
 *
 * // For admin authentication (with autocomplete)
 * app.use('/api/admin', authMiddleware(AuthStrategy.JWT_ADMIN));
 *
 * // For Google OAuth authentication (future implementation)
 * app.use('/api/oauth', authMiddleware(AuthStrategy.GOOGLE));
 */
export function authMiddleware(strategyName: AuthStrategyType) {
	return (req: Request, res: Response, next: NextFunction) => {
		passport.authenticate(strategyName, { session: false }, (err: any, data: AuthUserWithTokenPayload) => {
			if (err || !data.user) {
				return next(new UnauthorizedError("Unauthorized"));
			}

			req.user = data.user;
			req.tokenPayload = data.tokenPayload;

			// Set user context after all validations pass
			setUserContext({
				id: data.user.id,
				email: data.user.email,
				username: data.user.name,
			});

			return next();
		})(req, res, next);
	};
}
