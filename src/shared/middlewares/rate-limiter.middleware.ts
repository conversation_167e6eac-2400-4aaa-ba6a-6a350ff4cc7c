import { rateLimit } from "express-rate-limit";
import { RedisStore } from "rate-limit-redis";
import { Env } from "@/shared/config/env.config";
import { cacheClient } from "../lib/redis"; // Ini adalah instance ioredis

export const apiRateLimiter = rateLimit({
	store: new RedisStore({
		// ioredis compatible: RedisStore hanya butuh fungsi sendCommand
		// @ts-ignore
		sendCommand: (...args: string[]) => cacheClient.call(...args),
	}),

	keyGenerator: (req) => {
		const forwarded = req.headers["x-forwarded-for"] as string;
		return forwarded ? forwarded.split(",")[0] as string : req.socket.remoteAddress ?? "unknown";
	},

	windowMs: Env.RATE_LIMIT_WINDOW_MS, // e.g. 15 * 60 * 1000
	max: Env.NODE_ENV === "production" ? Env.RATE_LIMIT_MAX_REQUESTS : Env.RATE_LIMIT_MAX_REQUESTS_DEV,

	message: "Te<PERSON>al<PERSON> banyak permintaan, coba lagi nanti.",
	standardHeaders: "draft-7",
	legacyHeaders: false,

	handler: (req, res, _next) => {
		console.warn(`Rate limit exceeded for IP: ${req.ip}`);
		res.status(429).send("Too many requests, please try again later.");
	},
});
