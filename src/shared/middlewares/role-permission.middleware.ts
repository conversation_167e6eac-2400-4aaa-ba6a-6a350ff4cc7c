// import { ForbiddenError } from "@/app/exceptions";
// import type { NextFunction, Request, Response } from "express";
// import type { AuthUser } from "../interface";

// export async function rolePermissionMiddleware(roles: string[], permissions: string[]) {
// 	return (req: Request, res: Response, next: NextFunction) => {
// 		try {
// 			const user = req?.user as AuthUser;

// 			if (!user) {
// 				throw new ForbiddenError(req.t("errors.forbidden.default"));
// 			}

// 			if (user.role === "SUPER ADMIN" && user.permissions.includes("ALL:RESOURCE")) {
// 				return next();
// 			}

// 			const hasRole = roles.includes(user.role);
// 			if (!hasRole) {
// 				throw new ForbiddenError(req.t("errors.forbidden.default"));
// 			}

// 			const hasPermissions = permissions.every((permission) => user.permissions.includes(permission));
// 			if (!hasPermissions) {
// 				throw new ForbiddenError(req.t("errors.forbidden.default"));
// 			}

// 			next();
// 		} catch (error) {
// 			next(error);
// 		}
// 	};
// }
