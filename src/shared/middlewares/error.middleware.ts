import type { NextFunction, Request, Response } from "express";
import jwt from "jsonwebtoken";
import multer from "multer";
import { HttpException } from "@/shared/exceptions";
import logger from "@/shared/lib/logger";
import { captureException } from "@/shared/lib/sentry";

export async function errorMiddleware(err: any, _req: Request, res: Response, next: NextFunction) {
	try {
		logger.error(err);
		const responseError = {
			status: "failed",
			message: "",
		};

		if (err instanceof multer.MulterError) {
			res.status(400).json({
				...responseError,
				message: err.message || "An error occurred while uploading the file",
			});

			return;
		}

		if (err instanceof jwt.JsonWebTokenError) {
			res.status(401).json({
				...responseError,
				message: "Token akses tidak valid atau sudah kadaluarsa",
			});

			return;
		}

		if (err instanceof HttpException) {
			res.status(err.statusCode).json({
				...responseError,
				message: err.message,
			});

			return;
		}

		captureException(err);

		res.status(500).json({
			...responseError,
			message: "Internal Server Error",
		});
	} catch (error) {
		next(error);
	}
}
