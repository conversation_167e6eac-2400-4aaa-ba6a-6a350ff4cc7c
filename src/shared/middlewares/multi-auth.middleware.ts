import type { NextFunction, Request, Response } from "express";
import passport from "passport";
import { UnauthorizedError } from "@/shared/exceptions";
import { setUserContext } from "@/shared/lib/sentry";
import type { AuthUserWithTokenPayload } from "@/shared/types";

export function multiAuthMiddleware(strategyNames: string[]) {
	return (req: Request, res: Response, next: NextFunction) => {
		let currentStrategyIndex = 0;

		const tryNextStrategy = () => {
			if (currentStrategyIndex < strategyNames.length) {
				const strategyName = strategyNames[currentStrategyIndex];
				passport.authenticate(
					strategyName as string,
					{ session: false },
					(err: any, data: AuthUserWithTokenPayload) => {
						if (err || !data.user) {
							currentStrategyIndex++;
							tryNextStrategy();
						} else {
							req.user = data.user;
							req.tokenPayload = data.tokenPayload;

							setUserContext({
								id: data.user.id,
								email: data.user.email,
								username: data.user.name,
							});

							return next();
						}
					},
				)(req, res, next);
			} else {
				return next(new UnauthorizedError("Unauthorized"));
			}
		};

		tryNextStrategy();
	};
}
