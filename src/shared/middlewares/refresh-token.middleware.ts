import type { NextFunction, Request, Response } from "express";
import passport from "passport";
import { AuthStrategy } from "@/shared/enums";
import { UnauthorizedError } from "@/shared/exceptions";
import type { AuthTokenPayload } from "@/shared/types";

export function refreshTokenMiddleware(req: Request, res: Response, next: NextFunction) {
	passport.authenticate(AuthStrategy.JWT_REFRESH_TOKEN, { session: false }, (err: any, payload: AuthTokenPayload) => {
		if (err || !payload) {
			return next(new UnauthorizedError("Sesi login anda telah habis, tolong login kembali"));
		}

		req.tokenPayload = payload;

		return next();
	})(req, res, next);
}
