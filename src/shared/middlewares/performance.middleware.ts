import type { NextFunction, Request, Response } from "express";
import logger from "@/shared/lib/logger";

export const performanceMiddleware = (req: Request, res: Response, next: NextFunction): void => {
	// Skip performance monitoring for static files and API docs
	const skipPaths = ["/api-docs", "/uploads", "/assets", "/docs", "/favicon.ico", "/robots.txt", "/sitemap.xml"];

	const shouldSkip =
		skipPaths.some((path) => req.path.startsWith(path)) ||
		req.path.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/);

	if (shouldSkip) {
		next();
		return;
	}

	const start = Date.now();
	const startTime = process.hrtime.bigint();

	// Add request start time to request object
	req.startTime = start;

	// Use response finish event instead of overriding res.end
	res.on("finish", () => {
		const duration = Date.now() - start;
		const hrDuration = Number(process.hrtime.bigint() - startTime) / 1000000; // Convert to milliseconds

		// Log slow requests (>100ms)
		if (duration > 100) {
			logger.warn("Slow request detected", {
				method: req.method,
				url: req.originalUrl,
				duration: `${duration}ms`,
				hrDuration: `${hrDuration.toFixed(2)}ms`,
				statusCode: res.statusCode,
				userAgent: req.get("User-Agent"),
				ip: req.ip,
				userId: req.user?.id || "anonymous",
			});
		}

		// Log very slow requests (>500ms) as errors
		if (duration > 500) {
			logger.warn("Very slow request detected", {
				method: req.method,
				url: req.originalUrl,
				duration: `${duration}ms`,
				hrDuration: `${hrDuration.toFixed(2)}ms`,
				statusCode: res.statusCode,
				userAgent: req.get("User-Agent"),
				ip: req.ip,
				userId: req.user?.id || "anonymous",
				query: req.query,
				body: req.method === "POST" ? req.body : undefined,
			});
		}
	});

	// Add performance headers early (before response starts)
	if (!res.headersSent) {
		try {
			res.setHeader("X-Request-Start", start.toString());
		} catch (_error) {
			// Ignore if headers already sent
		}
	}

	next();
};

/**
 * Database query performance tracker.
 *
 * @param {string} query - SQL query being executed
 * @param {number} duration - Query execution time in milliseconds
 * @param {string} [model] - Optional model name for context
 * @returns {void}
 *
 * @description
 * Monitors database query performance:
 * - Logs warnings for queries taking >50ms
 * - Logs errors for queries taking >200ms
 * - Truncates long queries in logs
 *
 * @example
 * trackDatabaseQuery("SELECT * FROM users", 75, "User");
 */
export const trackDatabaseQuery = (query: string, duration: number, model?: string): void => {
	if (duration > 50) {
		logger.warn("Slow database query detected", {
			query: query.substring(0, 200) + (query.length > 200 ? "..." : ""),
			duration: `${duration}ms`,
			model,
		});
	}

	if (duration > 200) {
		logger.error("Very slow database query detected", {
			query: query.substring(0, 500) + (query.length > 500 ? "..." : ""),
			duration: `${duration}ms`,
			model,
		});
	}
};

/**
 * Cache operation performance tracker.
 *
 * @param {("get"|"set"|"del")} operation - Type of cache operation
 * @param {string} key - Cache key being accessed
 * @param {number} duration - Operation execution time in milliseconds
 * @param {boolean} [hit] - Optional cache hit indicator
 * @returns {void}
 *
 * @description
 * Monitors cache operation performance:
 * - Logs warnings for operations taking >10ms
 * - Tracks cache hits/misses
 *
 * @example
 * trackCacheOperation("get", "user:123", 15, true);
 */
export const trackCacheOperation = (
	operation: "get" | "set" | "del",
	key: string,
	duration: number,
	hit?: boolean,
): void => {
	if (duration > 10) {
		logger.warn("Slow cache operation detected", {
			operation,
			key,
			duration: `${duration}ms`,
			hit,
		});
	}
};
