import fs from "node:fs";
import path from "node:path";
import type { NextFunction, Response } from "express";
import multer from "multer";
import { nanoid } from "nanoid";
import { fileTypeLabels, getAllowedFileTypeLabels } from "@/shared/lib/file";

interface FieldUploadConfig {
	name: string;
	required?: boolean;
	maxCount?: number;
	allowedMimeTypes?: string[] | null;
	maxFileSize?: number;
	folder?: string;
	userFolder?: boolean;
	fileNamePrefix?: string;
}

interface SingleUploadConfig {
	required?: boolean;
	allowedMimeTypes?: string[] | null;
	maxFileSize?: number;
	folder?: string;
	userFolder?: boolean;
	fileNamePrefix?: string;
}

const defaultFileSize = 3 * 1024 * 1024;
const defaultFolder = "uploads/default";
const defaultImageTypes = ["image/jpeg", "image/png", "image/gif"];

function isAllowAll(mime?: string[] | null): boolean {
	return (
		!mime ||
		mime === null ||
		mime.includes("*") ||
		mime.includes("*/*") ||
		(typeof mime === "string" && mime === "all")
	);
}

// Helper function to flatten field errors into a single message
function flattenFieldErrors(fieldErrors: Record<string, string[]>): string {
	const messages: string[] = [];

	for (const [field, errors] of Object.entries(fieldErrors)) {
		for (const error of errors) {
			messages.push(`${field}: ${error}`);
		}
	}

	return messages.join("; ");
}

export const localUploadMiddleware = {
	single: (fieldName: string, config?: SingleUploadConfig) => {
		const {
			maxFileSize = defaultFileSize,
			allowedMimeTypes = defaultImageTypes,
			folder = defaultFolder,
			userFolder = false,
			fileNamePrefix = fieldName,
			required = false,
		} = config || {};

		const storage = multer.diskStorage({
			destination: (req: any, _file, cb) => {
				let dest = path.resolve(process.cwd(), folder);
				if (userFolder && req.user?.id) {
					dest = path.join(dest, req.user.id);
				}
				fs.mkdirSync(dest, { recursive: true });
				cb(null, dest);
			},
			filename: (_req, file, cb) => {
				const ext = path.extname(file.originalname);
				const filename = `${fileNamePrefix}-${nanoid(16)}${ext}`;
				cb(null, filename);
			},
		});

		const fileFilter = (
			_req: any,
			file: Express.Multer.File,
			cb: multer.FileFilterCallback,
		) => {
			const errors: string[] = [];

			// Check file type
			if (!isAllowAll(allowedMimeTypes) && Array.isArray(allowedMimeTypes)) {
				if (!allowedMimeTypes.includes(file.mimetype)) {
					const allowed = getAllowedFileTypeLabels(allowedMimeTypes);
					errors.push(
						`Tipe file tidak diperbolehkan: ${fileTypeLabels[file.mimetype] || file.mimetype}. Tipe yang diizinkan: ${allowed}`,
					);
				}
			}

			// Check file size
			if (file.size > maxFileSize) {
				const maxMB = (maxFileSize / (1024 * 1024)).toFixed(2);
				const actualMB = (file.size / (1024 * 1024)).toFixed(2);
				errors.push(
					`Ukuran file terlalu besar: ${actualMB}MB. Maksimal: ${maxMB}MB`,
				);
			}

			if (errors.length > 0) {
				// Create field-specific error
				const fieldErrors = { [file.fieldname]: errors };
				cb(new Error(JSON.stringify(fieldErrors)));
				return;
			}

			cb(null, true);
		};

		const upload = multer({
			storage,
			fileFilter,
		}).single(fieldName);

		// Return wrapped middleware that handles missing files and returns 422 directly
		return (req: any, res: Response, next: NextFunction) => {
			upload(req, res, (err) => {
				// Check if file is required but missing
				if (required && !req.file) {
					const fieldErrors = { [fieldName]: ["File wajib diunggah"] };
					res.status(422).json({
						status: "failed",
						message: flattenFieldErrors(fieldErrors),
					});
					return;
				}

				if (err) {
					let fieldErrors: Record<string, string[]> = {};

					// Handle file validation errors (from fileFilter)
					if (err.message?.startsWith("{")) {
						try {
							fieldErrors = JSON.parse(err.message);
						} catch {
							fieldErrors[fieldName] = [err.message || "File upload error"];
						}
					} else if (err instanceof multer.MulterError) {
						// Handle multer errors
						if (err.code === "LIMIT_FILE_SIZE") {
							const maxMB = (maxFileSize / (1024 * 1024)).toFixed(2);
							fieldErrors[err.field || fieldName] = [
								`Ukuran file terlalu besar. Maksimal: ${maxMB}MB`,
							];
						} else if (err.code === "LIMIT_UNEXPECTED_FILE") {
							fieldErrors[err.field || fieldName] = ["Field file tidak sesuai"];
						} else {
							fieldErrors[err.field || fieldName] = [
								err.message || "Terjadi kesalahan saat mengunggah file",
							];
						}
					} else {
						// Other errors
						fieldErrors[fieldName] = [err.message || "File upload error"];
					}

					// Return 422 directly
					res.status(422).json({
						status: "failed",
						message: flattenFieldErrors(fieldErrors),
					});
					return;
				}

				next();
			});
		};
	},

	multiple: (fields: FieldUploadConfig[]) => {
		const fieldConfigs: Record<string, FieldUploadConfig> = {};
		// biome-ignore lint/suspicious/noAssignInExpressions: <reason>
		fields.forEach((f) => (fieldConfigs[f.name] = f));

		const storage = multer.diskStorage({
			destination: (req: any, file, cb) => {
				const config = fieldConfigs[file.fieldname];
				if (!config) {
					return cb(new Error(`Field '${file.fieldname}' not recognized`), "");
				}

				let dest = path.resolve(process.cwd(), config.folder || defaultFolder);
				if (config.userFolder && req.user?.id) {
					dest = path.join(dest, req.user.id);
				}

				fs.mkdirSync(dest, { recursive: true });
				cb(null, dest);
			},

			filename: (_req, file, cb) => {
				const config = fieldConfigs[file.fieldname];
				const ext = path.extname(file.originalname);
				const prefix = config?.fileNamePrefix || file.fieldname;
				cb(null, `${prefix}-${nanoid(16)}${ext}`);
			},
		});

		const fileFilter = (
			_req: any,
			file: Express.Multer.File,
			cb: multer.FileFilterCallback,
		) => {
			const config = fieldConfigs[file.fieldname];
			if (!config) {
				const fieldErrors = { [file.fieldname]: ["Field tidak dikenal"] };
				return cb(new Error(JSON.stringify(fieldErrors)));
			}

			const allowed = config.allowedMimeTypes ?? defaultImageTypes;
			const maxFileSize = config.maxFileSize ?? defaultFileSize;
			const errors: string[] = [];

			// Check file type
			if (!isAllowAll(allowed) && Array.isArray(allowed)) {
				if (!allowed.includes(file.mimetype)) {
					const allowedLabel = getAllowedFileTypeLabels(allowed);
					errors.push(
						`Tipe file tidak diperbolehkan: ${fileTypeLabels[file.mimetype] || file.mimetype}. Tipe yang diizinkan: ${allowedLabel}`,
					);
				}
			}

			// Check file size
			if (file.size > maxFileSize) {
				const maxMB = (maxFileSize / (1024 * 1024)).toFixed(2);
				const actualMB = (file.size / (1024 * 1024)).toFixed(2);
				errors.push(
					`Ukuran file terlalu besar: ${actualMB}MB. Maksimal: ${maxMB}MB`,
				);
			}

			if (errors.length > 0) {
				const fieldErrors = { [file.fieldname]: errors };
				cb(new Error(JSON.stringify(fieldErrors)));
				return;
			}

			cb(null, true);
		};

		const upload = multer({
			storage,
			fileFilter,
		}).fields(fields.map((f) => ({ name: f.name, maxCount: f.maxCount || 1 })));

		// Return wrapped middleware that handles missing required files and returns 422 directly
		return (req: any, res: Response, next: NextFunction) => {
			upload(req, res, (err) => {
				if (err) {
					let fieldErrors: Record<string, string[]> = {};

					// Handle file validation errors (from fileFilter)
					if (err.message?.startsWith("{")) {
						try {
							fieldErrors = JSON.parse(err.message);
						} catch {
							fieldErrors.file = [err.message || "File upload error"];
						}
					} else if (err instanceof multer.MulterError) {
						// Handle multer errors
						if (err.code === "LIMIT_FILE_SIZE") {
							const config = fieldConfigs[err.field || ""];
							const maxFileSize = config?.maxFileSize ?? defaultFileSize;
							const maxMB = (maxFileSize / (1024 * 1024)).toFixed(2);
							fieldErrors[err.field || "file"] = [
								`Ukuran file terlalu besar. Maksimal: ${maxMB}MB`,
							];
						} else if (err.code === "LIMIT_UNEXPECTED_FILE") {
							fieldErrors[err.field || "file"] = ["Field file tidak sesuai"];
						} else {
							fieldErrors[err.field || "file"] = [
								err.message || "Terjadi kesalahan saat mengunggah file",
							];
						}
					} else {
						// Other errors
						fieldErrors.file = [err.message || "File upload error"];
					}

					// Return 422 directly
					res.status(422).json({
						status: "failed",
						message: flattenFieldErrors(fieldErrors),
					});
					return;
				}

				// Check for required but missing files
				const fieldErrors: Record<string, string[]> = {};
				const uploadedFiles =
					(req.files as { [fieldname: string]: Express.Multer.File[] }) || {};

				fields.forEach((fieldConfig) => {
					const fieldName = fieldConfig.name;
					const isRequired = fieldConfig.required !== false; // Default to required unless explicitly set to false

					if (
						isRequired &&
						(!uploadedFiles[fieldName] || uploadedFiles[fieldName].length === 0)
					) {
						fieldErrors[fieldName] = [
							`${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} wajib diunggah`,
						];
					}
				});

				if (Object.keys(fieldErrors).length > 0) {
					res.status(422).json({
						status: "failed",
						message: flattenFieldErrors(fieldErrors),
					});
					return;
				}

				next();
			});
		};
	},
};
