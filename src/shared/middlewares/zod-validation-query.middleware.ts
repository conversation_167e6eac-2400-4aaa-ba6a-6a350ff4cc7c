import type { NextFunction, Request, Response } from "express";
import * as z from "zod";
import type { Locale } from "../constants";
import { formatZodError } from "../utils/validation";

// Query validation middlware, to filter only query that passing the validation will be use
export const validateQuery = (schema: z.ZodType) => {
	return (req: Request, res: Response, next: NextFunction) => {
		try {
			const validatedQuery = schema.parse(req.query);

			(req as any).validatedQuery = validatedQuery;

			next();
		} catch (error) {
			if (error instanceof z.ZodError) {
				const locale = (req.locale as Locale) || "id";

				res.status(422).json({
					status: "failed",
					message: formatZodError(error, locale),
				});
				return;
			}

			next(error);
		}
	};
};
