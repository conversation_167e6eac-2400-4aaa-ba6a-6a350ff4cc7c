import type { NextFunction, Request, Response } from "express";
import * as z from "zod";
import type { Locale } from "../constants";
import { formatZodError } from "../utils/validation";

// Body validation middleware (for use after file upload)
export const validateBody = (schema: z.ZodType) => {
	return async (
		req: Request & { locale?: string },
		res: Response,
		next: NextFunction,
	) => {
		try {
			const validatedData = schema.parse(req.body);
			(req as any).validatedBody = validatedData;
			next();
		} catch (error) {
			if (error instanceof z.ZodError) {
				const locale = (req.locale as Locale) || "id";

				res.status(422).json({
					status: "failed",
					message: formatZodError(error, locale),
				});
				return;
			}

			next(error);
		}
	};
};

// Unified error handler for both file and body validation errors
export const unifiedValidationErrorHandler = (
	error: any,
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	// Handle Zod validation errors
	if (error instanceof z.ZodError) {
		const locale = (req.locale as Locale) || "id";

		res.status(422).json({
			status: "failed",
			message: formatZodError(error, locale),
		});
		return;
	}

	next(error);
};

// Data preprocessing helpers
export const preprocessors = {
	// Convert string numbers to actual numbers (for form data)
	parseNumbers: (fields: string[]) => {
		return (req: Request, _res: Response, next: NextFunction) => {
			fields.forEach((field) => {
				if (req.body[field] && typeof req.body[field] === "string") {
					const parsed = Number.parseFloat(req.body[field]);
					if (!Number.isNaN(parsed)) {
						req.body[field] = parsed;
					}
				}
			});
			next();
		};
	},

	normalizeToArrayFields: (fields: string[]) => {
		return (req: Request, _res: Response, next: NextFunction) => {
			fields.forEach((field) => {
				const value = req.body[field];

				if (value === "" || value == null) {
					delete req.body[field];
				} else if (!Array.isArray(value)) {
					req.body[field] = [value];
				}
			});
			next();
		};
	},

	// Add file paths to body for validation or processing
	addFilePaths: (
		fileFields: Array<{
			field: string;
			bodyKey?: string;
			pathProcessor?: (path: string) => string | undefined;
		}>,
	) => {
		return (req: Request, _res: Response, next: NextFunction) => {
			fileFields.forEach(({ field, bodyKey, pathProcessor }) => {
				const key = bodyKey || field;

				if (req.file) {
					// Single file
					if (req.file.path) {
						req.body[key] = pathProcessor
							? pathProcessor(req.file.path)
							: req.file.path;
					}
				} else if (req.files && typeof req.files === "object") {
					// Multiple files
					const files = req.files as {
						[fieldname: string]: Express.Multer.File[];
					};
					console.log(files, "INI FILES");

					if (files[field]?.[0]) {
						req.body[key] = pathProcessor
							? pathProcessor(files[field][0].path)
							: files[field][0].path;
					}
				}
			});
			next();
		};
	},
};
