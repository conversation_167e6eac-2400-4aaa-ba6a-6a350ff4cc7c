# 🗄️ Database Schema Management (PostgreSQL)

Dokumen ini menjelaskan cara ekspor dan impor struktur database (schema) menggunakan file `schema.sql`.

---

## 📤 Export Schema (struktur database)

Ekspor ini hanya menghasilkan struktur database (tanpa data), dan sangat bersih:
- Tanpa informasi user/owner
- Tanpa privilege atau permission
- Tanpa data
- Bisa digunakan di database manapun

### 💻 Perintah:

```bash
pg_dump --schema-only \
  --no-owner \
  --no-privileges \
  --clean \
  "$DATABASE_URL" \
  > src/db/schema.sql
