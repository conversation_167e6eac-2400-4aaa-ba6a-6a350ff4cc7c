import { CamelCase<PERSON>lug<PERSON>, <PERSON><PERSON><PERSON>, PostgresDialect } from "kysely";
import * as pg from "pg";
import { Env } from "@/shared/config/env.config";
import logger from "@/shared/lib/logger";
import type Database from "./types/Database";

pg.types.setTypeParser(1082, (value) => value); // DATE as string
// pg.types.setTypeParser(1114, (value) => value); // TIMESTAMP as string (optional)
// pg.types.setTypeParser(1184, (value) => value); // TIMESTAMP WITH TIME ZONE as string (optional)

const pool = new pg.Pool({
	connectionString: Env.DATABASE_URI,
	ssl: true,
	max: 10,
});
const dialect = new PostgresDialect({
	pool,
});

pool.on("connect", () => {
	logger.info("✅ Database connected!");
});

export const db = new Kysely<Database>({
	dialect,
	plugins: [new CamelCasePlugin()],
});
