// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.permissions */
export type PermissionsId = string & { __brand: 'public.permissions' };

/** Represents the table public.permissions */
export default interface PermissionsTable {
  id: ColumnType<PermissionsId, PermissionsId, PermissionsId>;

  name: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;
}

export type Permissions = Selectable<PermissionsTable>;

export type NewPermissions = Insertable<PermissionsTable>;

export type PermissionsUpdate = Updateable<PermissionsTable>;
