// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.tasks */
export type TasksId = string & { __brand: 'public.tasks' };

/** Represents the table public.tasks */
export default interface TasksTable {
  id: ColumnType<TasksId, TasksId, TasksId>;

  name: ColumnType<string, string, string>;

  assignerId: ColumnType<UsersId, UsersId, UsersId>;

  assigneeId: ColumnType<UsersId, UsersId, UsersId>;

  deadline: ColumnType<string, string, string>;

  description: ColumnType<string, string, string>;

  documentUrls: ColumnType<string, string, string>;

  ratingPoint: ColumnType<number | null, number | null, number | null>;

  status: ColumnType<string, string | undefined, string>;

  /** Reason for task is rejected by assignees */
  rejectionReason: ColumnType<string | null, string | null, string | null>;

  /** Final feedback from assigner to assignee, this will same as feedback in task submission when task is completed */
  finalFeedback: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Tasks = Selectable<TasksTable>;

export type NewTasks = Insertable<TasksTable>;

export type TasksUpdate = Updateable<TasksTable>;
