// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.accounts */
export type AccountsId = string & { __brand: 'public.accounts' };

/** Represents the table public.accounts */
export default interface AccountsTable {
  id: ColumnType<AccountsId, AccountsId, AccountsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  accountId: ColumnType<string, string, string>;

  providerId: ColumnType<string, string, string>;

  accessToken: ColumnType<string | null, string | null, string | null>;

  refreshToken: ColumnType<string | null, string | null, string | null>;

  accessTokenExpiresAt: ColumnType<string | null, string | null, string | null>;

  refreshTokenExpiresAt: ColumnType<string | null, string | null, string | null>;

  scope: ColumnType<string | null, string | null, string | null>;

  idToken: ColumnType<string | null, string | null, string | null>;

  password: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Accounts = Selectable<AccountsTable>;

export type NewAccounts = Insertable<AccountsTable>;

export type AccountsUpdate = Updateable<AccountsTable>;
