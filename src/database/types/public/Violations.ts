// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ViolationTypesId } from './ViolationTypes';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.violations */
export type ViolationsId = string & { __brand: 'public.violations' };

/** Represents the table public.violations */
export default interface ViolationsTable {
  id: ColumnType<ViolationsId, ViolationsId, ViolationsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  violationTypeId: ColumnType<ViolationTypesId, ViolationTypesId, ViolationTypesId>;

  violationDate: ColumnType<string, string, string>;

  notes: ColumnType<string | null, string | null, string | null>;

  recordedBy: ColumnType<UsersId, UsersId, UsersId>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Violations = Selectable<ViolationsTable>;

export type NewViolations = Insertable<ViolationsTable>;

export type ViolationsUpdate = Updateable<ViolationsTable>;
