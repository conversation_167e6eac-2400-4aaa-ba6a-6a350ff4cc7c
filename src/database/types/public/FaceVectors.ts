// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.face_vectors */
export type FaceVectorsId = string & { __brand: 'public.face_vectors' };

/** Represents the table public.face_vectors */
export default interface FaceVectorsTable {
  id: ColumnType<FaceVectorsId, FaceVectorsId, FaceVectorsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  descriptor: ColumnType<string, string, string>;

  imagePath: ColumnType<string, string, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type FaceVectors = Selectable<FaceVectorsTable>;

export type NewFaceVectors = Insertable<FaceVectorsTable>;

export type FaceVectorsUpdate = Updateable<FaceVectorsTable>;
