// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { WorksitesId } from './Worksites';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.attendance_rules */
export type AttendanceRulesId = string & { __brand: 'public.attendance_rules' };

/** Represents the table public.attendance_rules */
export default interface AttendanceRulesTable {
  id: ColumnType<AttendanceRulesId, AttendanceRulesId, AttendanceRulesId>;

  worksiteId: ColumnType<WorksitesId, WorksitesId, WorksitesId>;

  checkInStartTime: ColumnType<string, string, string>;

  checkInEndTime: ColumnType<string, string, string>;

  checkInToleranceMinutes: ColumnType<number, number, number>;

  checkOutStartTime: ColumnType<string, string, string>;

  checkOutEndTime: ColumnType<string, string, string>;

  checkOutToleranceMinutes: ColumnType<number, number, number>;

  breakStartTime: ColumnType<string, string, string>;

  breakEndTime: ColumnType<string, string, string>;

  breakToleranceMinutes: ColumnType<number, number, number>;

  returnStartTime: ColumnType<string, string, string>;

  returnEndTime: ColumnType<string, string, string>;

  returnToleranceMinutes: ColumnType<number, number, number>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;

  latitude: ColumnType<number, number | undefined, number>;

  longitude: ColumnType<number, number | undefined, number>;

  radiusInMeter: ColumnType<number, number | undefined, number>;

  timezone: ColumnType<string, string | undefined, string>;
}

export type AttendanceRules = Selectable<AttendanceRulesTable>;

export type NewAttendanceRules = Insertable<AttendanceRulesTable>;

export type AttendanceRulesUpdate = Updateable<AttendanceRulesTable>;
