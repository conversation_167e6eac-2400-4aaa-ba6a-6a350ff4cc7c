// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.worksites */
export type WorksitesId = string & { __brand: 'public.worksites' };

/** Represents the table public.worksites */
export default interface WorksitesTable {
  id: ColumnType<WorksitesId, WorksitesId, WorksitesId>;

  name: ColumnType<string, string, string>;

  type: ColumnType<string, string, string>;

  address: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Worksites = Selectable<WorksitesTable>;

export type NewWorksites = Insertable<WorksitesTable>;

export type WorksitesUpdate = Updateable<WorksitesTable>;
