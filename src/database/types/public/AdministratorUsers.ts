// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.administrator_users */
export type AdministratorUsersId = string & { __brand: 'public.administrator_users' };

/** Represents the table public.administrator_users */
export default interface AdministratorUsersTable {
  id: ColumnType<AdministratorUsersId, AdministratorUsersId, AdministratorUsersId>;

  name: ColumnType<string, string, string>;

  email: ColumnType<string, string, string>;

  password: ColumnType<string, string, string>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  emailVerified: ColumnType<boolean, boolean | undefined, boolean>;

  image: ColumnType<string, string | undefined, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type AdministratorUsers = Selectable<AdministratorUsersTable>;

export type NewAdministratorUsers = Insertable<AdministratorUsersTable>;

export type AdministratorUsersUpdate = Updateable<AdministratorUsersTable>;
