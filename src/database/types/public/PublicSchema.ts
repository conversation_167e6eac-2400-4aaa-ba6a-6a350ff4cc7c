// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as ViolationsTable } from './Violations';
import type { default as AdministratorUsersTable } from './AdministratorUsers';
import type { default as UsersTable } from './Users';
import type { default as LeaveUsagesTable } from './LeaveUsages';
import type { default as SessionsTable } from './Sessions';
import type { default as FaceVectorsTable } from './FaceVectors';
import type { default as TasksTable } from './Tasks';
import type { default as WorksitesTable } from './Worksites';
import type { default as AttendanceRulesTable } from './AttendanceRules';
import type { default as HolidaysTable } from './Holidays';
import type { default as KpiRecordsTable } from './KpiRecords';
import type { default as AccountsTable } from './Accounts';
import type { default as PermissionsTable } from './Permissions';
import type { default as UsersWorksitesTable } from './UsersWorksites';
import type { default as UsersRolesTable } from './UsersRoles';
import type { default as NotificationRecipientsTable } from './NotificationRecipients';
import type { default as LeaveRequestsTable } from './LeaveRequests';
import type { default as IncentiveRecordsTable } from './IncentiveRecords';
import type { default as OfficeLeavesTable } from './OfficeLeaves';
import type { default as NotificationsTable } from './Notifications';
import type { default as AttendanceLogsTable } from './AttendanceLogs';
import type { default as SequelizeMetaTable } from './SequelizeMeta';
import type { default as RolesTable } from './Roles';
import type { default as TaskSubmissionsTable } from './TaskSubmissions';
import type { default as AttendanceAttemptsTable } from './AttendanceAttempts';
import type { default as RolesPermissionsTable } from './RolesPermissions';
import type { default as UserHierarchiesTable } from './UserHierarchies';
import type { default as LeavePoliciesTable } from './LeavePolicies';
import type { default as ViolationTypesTable } from './ViolationTypes';
import type { default as VerificationsTable } from './Verifications';
import type { default as FcmTokensTable } from './FcmTokens';

export default interface PublicSchema {
  violations: ViolationsTable;

  administratorUsers: AdministratorUsersTable;

  users: UsersTable;

  leaveUsages: LeaveUsagesTable;

  sessions: SessionsTable;

  faceVectors: FaceVectorsTable;

  tasks: TasksTable;

  worksites: WorksitesTable;

  attendanceRules: AttendanceRulesTable;

  holidays: HolidaysTable;

  kpiRecords: KpiRecordsTable;

  accounts: AccountsTable;

  permissions: PermissionsTable;

  usersWorksites: UsersWorksitesTable;

  usersRoles: UsersRolesTable;

  notificationRecipients: NotificationRecipientsTable;

  leaveRequests: LeaveRequestsTable;

  incentiveRecords: IncentiveRecordsTable;

  officeLeaves: OfficeLeavesTable;

  notifications: NotificationsTable;

  attendanceLogs: AttendanceLogsTable;

  sequelizeMeta: SequelizeMetaTable;

  roles: RolesTable;

  taskSubmissions: TaskSubmissionsTable;

  attendanceAttempts: AttendanceAttemptsTable;

  rolesPermissions: RolesPermissionsTable;

  userHierarchies: UserHierarchiesTable;

  leavePolicies: LeavePoliciesTable;

  violationTypes: ViolationTypesTable;

  verifications: VerificationsTable;

  fcmTokens: FcmTokensTable;
}
