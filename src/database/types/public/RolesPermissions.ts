// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { RolesId } from './Roles';
import type { PermissionsId } from './Permissions';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table public.roles_permissions */
export default interface RolesPermissionsTable {
  roleId: ColumnType<RolesId, RolesId, RolesId>;

  permissionId: ColumnType<PermissionsId, PermissionsId, PermissionsId>;
}

export type RolesPermissions = Selectable<RolesPermissionsTable>;

export type NewRolesPermissions = Insertable<RolesPermissionsTable>;

export type RolesPermissionsUpdate = Updateable<RolesPermissionsTable>;
