// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.holidays */
export type HolidaysId = string & { __brand: 'public.holidays' };

/** Represents the table public.holidays */
export default interface HolidaysTable {
  id: ColumnType<HolidaysId, HolidaysId, HolidaysId>;

  date: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Holidays = Selectable<HolidaysTable>;

export type NewHolidays = Insertable<HolidaysTable>;

export type HolidaysUpdate = Updateable<HolidaysTable>;
