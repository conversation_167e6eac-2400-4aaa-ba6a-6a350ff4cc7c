// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.SequelizeMeta */
export type SequelizeMetaName = string & { __brand: 'public.SequelizeMeta' };

/** Represents the table public.SequelizeMeta */
export default interface SequelizeMetaTable {
  name: ColumnType<SequelizeMetaName, SequelizeMetaName, SequelizeMetaName>;
}

export type SequelizeMeta = Selectable<SequelizeMetaTable>;

export type NewSequelizeMeta = Insertable<SequelizeMetaTable>;

export type SequelizeMetaUpdate = Updateable<SequelizeMetaTable>;
