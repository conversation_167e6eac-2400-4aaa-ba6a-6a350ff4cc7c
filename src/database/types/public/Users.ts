// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.users */
export type UsersId = string & { __brand: 'public.users' };

/** Represents the table public.users */
export default interface UsersTable {
  id: ColumnType<UsersId, UsersId, UsersId>;

  name: ColumnType<string, string, string>;

  email: ColumnType<string, string, string>;

  nik: ColumnType<string, string, string>;

  mobileNumber: ColumnType<string, string, string>;

  emailVerified: ColumnType<boolean, boolean | undefined, boolean>;

  image: ColumnType<string, string | undefined, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Users = Selectable<UsersTable>;

export type NewUsers = Insertable<UsersTable>;

export type UsersUpdate = Updateable<UsersTable>;
