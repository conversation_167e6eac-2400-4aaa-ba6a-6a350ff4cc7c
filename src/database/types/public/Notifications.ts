// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.notifications */
export type NotificationsId = string & { __brand: 'public.notifications' };

/** Represents the table public.notifications */
export default interface NotificationsTable {
  id: ColumnType<NotificationsId, NotificationsId, NotificationsId>;

  type: ColumnType<string, string, string>;

  entityType: ColumnType<string | null, string | null, string | null>;

  entityId: ColumnType<string | null, string | null, string | null>;

  actorId: ColumnType<string | null, string | null, string | null>;

  /** user | admin_user */
  actorType: ColumnType<string | null, string | null, string | null>;

  messageTemplate: ColumnType<string, string, string>;

  messageData: ColumnType<string, string, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Notifications = Selectable<NotificationsTable>;

export type NewNotifications = Insertable<NotificationsTable>;

export type NotificationsUpdate = Updateable<NotificationsTable>;
