// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.violation_types */
export type ViolationTypesId = string & { __brand: 'public.violation_types' };

/** Represents the table public.violation_types */
export default interface ViolationTypesTable {
  id: ColumnType<ViolationTypesId, ViolationTypesId, ViolationTypesId>;

  name: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  penaltyPoints: ColumnType<number, number, number>;

  punishment: ColumnType<string, string, string>;

  isActive: ColumnType<boolean, boolean | undefined, boolean>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type ViolationTypes = Selectable<ViolationTypesTable>;

export type NewViolationTypes = Insertable<ViolationTypesTable>;

export type ViolationTypesUpdate = Updateable<ViolationTypesTable>;
