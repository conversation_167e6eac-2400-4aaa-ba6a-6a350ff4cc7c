// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.leave_policies */
export type LeavePoliciesId = string & { __brand: 'public.leave_policies' };

/** Represents the table public.leave_policies */
export default interface LeavePoliciesTable {
  id: ColumnType<LeavePoliciesId, LeavePoliciesId, LeavePoliciesId>;

  name: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  quota: ColumnType<number, number, number>;

  isCountedAsPresent: ColumnType<boolean, boolean, boolean>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type LeavePolicies = Selectable<LeavePoliciesTable>;

export type NewLeavePolicies = Insertable<LeavePoliciesTable>;

export type LeavePoliciesUpdate = Updateable<LeavePoliciesTable>;
