// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { WorksitesId } from './Worksites';
import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table public.users_worksites */
export default interface UsersWorksitesTable {
  worksiteId: ColumnType<WorksitesId, WorksitesId, WorksitesId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;
}

export type UsersWorksites = Selectable<UsersWorksitesTable>;

export type NewUsersWorksites = Insertable<UsersWorksitesTable>;

export type UsersWorksitesUpdate = Updateable<UsersWorksitesTable>;
