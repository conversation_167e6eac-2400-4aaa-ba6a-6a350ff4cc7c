// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.office_leaves */
export type OfficeLeavesId = string & { __brand: 'public.office_leaves' };

/** Represents the table public.office_leaves */
export default interface OfficeLeavesTable {
  id: ColumnType<OfficeLeavesId, OfficeLeavesId, OfficeLeavesId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  title: ColumnType<string, string, string>;

  description: ColumnType<string, string, string>;

  startTime: ColumnType<string, string, string>;

  endTime: ColumnType<string, string, string>;

  date: ColumnType<string, string, string>;

  isOfficialBusiness: ColumnType<boolean | null, boolean | null, boolean | null>;

  reviewedBy: ColumnType<UsersId | null, UsersId | null, UsersId | null>;

  reviewedAt: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;

  status: ColumnType<string, string, string>;
}

export type OfficeLeaves = Selectable<OfficeLeavesTable>;

export type NewOfficeLeaves = Insertable<OfficeLeavesTable>;

export type OfficeLeavesUpdate = Updateable<OfficeLeavesTable>;
