// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { RolesId } from './Roles';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.user_hierarchies */
export type UserHierarchiesId = string & { __brand: 'public.user_hierarchies' };

/** Represents the table public.user_hierarchies */
export default interface UserHierarchiesTable {
  id: ColumnType<UserHierarchiesId, UserHierarchiesId, UserHierarchiesId>;

  /** One user can only have one active hierarchy record */
  userId: ColumnType<UsersId, UsersId, UsersId>;

  /** Reference to role that determines organizational level */
  roleId: ColumnType<RolesId, RolesId, RolesId>;

  supervisorId: ColumnType<UsersId | null, UsersId | null, UsersId | null>;

  isActive: ColumnType<boolean, boolean | undefined, boolean>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type UserHierarchies = Selectable<UserHierarchiesTable>;

export type NewUserHierarchies = Insertable<UserHierarchiesTable>;

export type UserHierarchiesUpdate = Updateable<UserHierarchiesTable>;
