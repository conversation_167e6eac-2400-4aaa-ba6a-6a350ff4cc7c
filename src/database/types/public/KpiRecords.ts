// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.kpi_records */
export type KpiRecordsId = string & { __brand: 'public.kpi_records' };

/** Represents the table public.kpi_records */
export default interface KpiRecordsTable {
  id: ColumnType<KpiRecordsId, KpiRecordsId, KpiRecordsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  /** Format: YYYY-MM for monthly, YYYY for yearly */
  period: ColumnType<string, string, string>;

  taskAverageScore: ColumnType<number, number | undefined, number>;

  attendancePercentage: ColumnType<number, number | undefined, number>;

  violationAverageScore: ColumnType<number, number | undefined, number>;

  finalKpiScore: ColumnType<number, number | undefined, number>;

  totalTasks: ColumnType<number, number | undefined, number>;

  totalWorkDays: ColumnType<number, number | undefined, number>;

  totalAttendanceDays: ColumnType<number, number | undefined, number>;

  totalViolations: ColumnType<number, number | undefined, number>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type KpiRecords = Selectable<KpiRecordsTable>;

export type NewKpiRecords = Insertable<KpiRecordsTable>;

export type KpiRecordsUpdate = Updateable<KpiRecordsTable>;
