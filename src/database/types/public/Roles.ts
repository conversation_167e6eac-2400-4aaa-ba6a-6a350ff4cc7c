// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.roles */
export type RolesId = string & { __brand: 'public.roles' };

/** Represents the table public.roles */
export default interface RolesTable {
  id: ColumnType<RolesId, RolesId, RolesId>;

  name: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  parentId: ColumnType<RolesId | null, RolesId | null, RolesId | null>;

  createdAt: ColumnType<string, string | undefined, string>;

  updatedAt: ColumnType<string, string | undefined, string>;
}

export type Roles = Selectable<RolesTable>;

export type NewRoles = Insertable<RolesTable>;

export type RolesUpdate = Updateable<RolesTable>;
