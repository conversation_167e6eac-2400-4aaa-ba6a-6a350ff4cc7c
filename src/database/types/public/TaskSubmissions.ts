// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { TasksId } from './Tasks';
import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.task_submissions */
export type TaskSubmissionsId = string & { __brand: 'public.task_submissions' };

/** Represents the table public.task_submissions */
export default interface TaskSubmissionsTable {
  id: ColumnType<TaskSubmissionsId, TaskSubmissionsId, TaskSubmissionsId>;

  taskId: ColumnType<TasksId, TasksId, TasksId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  reviewedBy: ColumnType<UsersId | null, UsersId | null, UsersId | null>;

  submissionNumber: ColumnType<number, number, number>;

  documentUrls: ColumnType<string, string, string>;

  status: ColumnType<string, string | undefined, string>;

  reviewDate: ColumnType<string | null, string | null, string | null>;

  submittedAt: ColumnType<string, string, string>;

  feedback: ColumnType<string | null, string | null, string | null>;

  notes: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type TaskSubmissions = Selectable<TaskSubmissionsTable>;

export type NewTaskSubmissions = Insertable<TaskSubmissionsTable>;

export type TaskSubmissionsUpdate = Updateable<TaskSubmissionsTable>;
