// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { WorksitesId } from './Worksites';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.attendance_logs */
export type AttendanceLogsId = string & { __brand: 'public.attendance_logs' };

/** Represents the table public.attendance_logs */
export default interface AttendanceLogsTable {
  id: ColumnType<AttendanceLogsId, AttendanceLogsId, AttendanceLogsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  /** Detected sites */
  worksiteId: ColumnType<WorksitesId, WorksitesId, WorksitesId>;

  type: ColumnType<string, string, string>;

  logDate: ColumnType<string, string, string>;

  logTime: ColumnType<string, string, string>;

  status: ColumnType<string, string, string>;

  locationLat: ColumnType<number, number, number>;

  locationLong: ColumnType<number, number, number>;

  distanceScore: ColumnType<number, number, number>;

  photo: ColumnType<string, string, string>;

  deviceInfo: ColumnType<string, string, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type AttendanceLogs = Selectable<AttendanceLogsTable>;

export type NewAttendanceLogs = Insertable<AttendanceLogsTable>;

export type AttendanceLogsUpdate = Updateable<AttendanceLogsTable>;
