// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.attendance_attempts */
export type AttendanceAttemptsId = string & { __brand: 'public.attendance_attempts' };

/** Represents the table public.attendance_attempts */
export default interface AttendanceAttemptsTable {
  id: ColumnType<AttendanceAttemptsId, AttendanceAttemptsId, AttendanceAttemptsId>;

  userId: ColumnType<string, string, string>;

  photo: ColumnType<string, string, string>;

  distance: ColumnType<number, number, number>;

  isMatch: ColumnType<boolean, boolean, boolean>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;

  threshold: ColumnType<number | null, number | null, number | null>;
}

export type AttendanceAttempts = Selectable<AttendanceAttemptsTable>;

export type NewAttendanceAttempts = Insertable<AttendanceAttemptsTable>;

export type AttendanceAttemptsUpdate = Updateable<AttendanceAttemptsTable>;
