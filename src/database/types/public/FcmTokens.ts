// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.fcm_tokens */
export type FcmTokensId = string & { __brand: 'public.fcm_tokens' };

/** Represents the table public.fcm_tokens */
export default interface FcmTokensTable {
  id: ColumnType<FcmTokensId, FcmTokensId, FcmTokensId>;

  token: ColumnType<string, string, string>;

  ownerId: ColumnType<string, string, string>;

  ownerType: ColumnType<string, string, string>;

  timestamp: ColumnType<string, string, string>;
}

export type FcmTokens = Selectable<FcmTokensTable>;

export type NewFcmTokens = Insertable<FcmTokensTable>;

export type FcmTokensUpdate = Updateable<FcmTokensTable>;
