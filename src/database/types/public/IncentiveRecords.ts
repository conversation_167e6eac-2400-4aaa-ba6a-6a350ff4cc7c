// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.incentive_records */
export type IncentiveRecordsId = string & { __brand: 'public.incentive_records' };

/** Represents the table public.incentive_records */
export default interface IncentiveRecordsTable {
  id: ColumnType<IncentiveRecordsId, IncentiveRecordsId, IncentiveRecordsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  /** ATTENDANCE, PRESENCE, MOTOR */
  incentiveType: ColumnType<string, string, string>;

  /** Period format based on incentive type cut-off */
  period: ColumnType<string, string, string>;

  isEligible: ColumnType<boolean, boolean, boolean>;

  amount: ColumnType<number | null, number | null, number | null>;

  notes: ColumnType<string | null, string | null, string | null>;

  calculatedAt: ColumnType<string, string, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;

  noLateCheckIn: ColumnType<boolean, boolean | undefined, boolean>;

  noEarlyCheckOut: ColumnType<boolean, boolean | undefined, boolean>;

  noMissedAttendance: ColumnType<boolean, boolean | undefined, boolean>;

  noLeaveNotCountedAsPresent: ColumnType<boolean, boolean | undefined, boolean>;

  fullMonthPresence: ColumnType<boolean, boolean | undefined, boolean>;

  allOfficeLeaveOfficial: ColumnType<boolean, boolean | undefined, boolean>;
}

export type IncentiveRecords = Selectable<IncentiveRecordsTable>;

export type NewIncentiveRecords = Insertable<IncentiveRecordsTable>;

export type IncentiveRecordsUpdate = Updateable<IncentiveRecordsTable>;
