// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { LeaveUsagesId } from './LeaveUsages';
import type { LeavePoliciesId } from './LeavePolicies';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.leave_requests */
export type LeaveRequestsId = string & { __brand: 'public.leave_requests' };

/** Represents the table public.leave_requests */
export default interface LeaveRequestsTable {
  id: ColumnType<LeaveRequestsId, LeaveRequestsId, LeaveRequestsId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  leaveUsageId: ColumnType<LeaveUsagesId, LeaveUsagesId, LeaveUsagesId>;

  leavePolicyId: ColumnType<LeavePoliciesId, LeavePoliciesId, LeavePoliciesId>;

  startDate: ColumnType<string, string, string>;

  endDate: ColumnType<string, string, string>;

  reason: ColumnType<string, string, string>;

  status: ColumnType<string, string, string>;

  effectiveLeaveDays: ColumnType<number, number, number>;

  documentUrl: ColumnType<string | null, string | null, string | null>;

  reviewedBy: ColumnType<UsersId | null, UsersId | null, UsersId | null>;

  reviewedAt: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type LeaveRequests = Selectable<LeaveRequestsTable>;

export type NewLeaveRequests = Insertable<LeaveRequestsTable>;

export type LeaveRequestsUpdate = Updateable<LeaveRequestsTable>;
