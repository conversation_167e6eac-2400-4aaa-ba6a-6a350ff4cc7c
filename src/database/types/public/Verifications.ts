// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.verifications */
export type VerificationsId = string & { __brand: 'public.verifications' };

/** Represents the table public.verifications */
export default interface VerificationsTable {
  id: ColumnType<VerificationsId, VerificationsId, VerificationsId>;

  identifier: ColumnType<string, string, string>;

  value: ColumnType<string, string, string>;

  expiresAt: ColumnType<string, string, string>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Verifications = Selectable<VerificationsTable>;

export type NewVerifications = Insertable<VerificationsTable>;

export type VerificationsUpdate = Updateable<VerificationsTable>;
