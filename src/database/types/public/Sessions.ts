// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.sessions */
export type SessionsId = string & { __brand: 'public.sessions' };

/** Represents the table public.sessions */
export default interface SessionsTable {
  id: ColumnType<SessionsId, SessionsId, SessionsId>;

  ownerId: ColumnType<string, string, string>;

  ownerType: ColumnType<string, string, string>;

  token: ColumnType<string, string, string>;

  expiresAt: ColumnType<string, string, string>;

  ipAddress: ColumnType<string | null, string | null, string | null>;

  userAgent: ColumnType<string | null, string | null, string | null>;

  impersonatedBy: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type Sessions = Selectable<SessionsTable>;

export type NewSessions = Insertable<SessionsTable>;

export type SessionsUpdate = Updateable<SessionsTable>;
