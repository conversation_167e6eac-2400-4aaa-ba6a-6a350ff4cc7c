// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UsersId } from './Users';
import type { LeavePoliciesId } from './LeavePolicies';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.leave_usages */
export type LeaveUsagesId = string & { __brand: 'public.leave_usages' };

/** Represents the table public.leave_usages */
export default interface LeaveUsagesTable {
  id: ColumnType<LeaveUsagesId, LeaveUsagesId, LeaveUsagesId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;

  leavePolicyId: ColumnType<LeavePoliciesId, LeavePoliciesId, LeavePoliciesId>;

  used: ColumnType<number, number, number>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type LeaveUsages = Selectable<LeaveUsagesTable>;

export type NewLeaveUsages = Insertable<LeaveUsagesTable>;

export type LeaveUsagesUpdate = Updateable<LeaveUsagesTable>;
