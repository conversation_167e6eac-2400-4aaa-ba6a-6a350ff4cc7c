// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { RolesId } from './Roles';
import type { UsersId } from './Users';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table public.users_roles */
export default interface UsersRolesTable {
  roleId: ColumnType<RolesId, RolesId, RolesId>;

  userId: ColumnType<UsersId, UsersId, UsersId>;
}

export type UsersRoles = Selectable<UsersRolesTable>;

export type NewUsersRoles = Insertable<UsersRolesTable>;

export type UsersRolesUpdate = Updateable<UsersRolesTable>;
