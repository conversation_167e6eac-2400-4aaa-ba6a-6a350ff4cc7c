// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { NotificationsId } from './Notifications';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for public.notification_recipients */
export type NotificationRecipientsId = string & { __brand: 'public.notification_recipients' };

/** Represents the table public.notification_recipients */
export default interface NotificationRecipientsTable {
  id: ColumnType<NotificationRecipientsId, NotificationRecipientsId, NotificationRecipientsId>;

  recipientId: ColumnType<string, string, string>;

  /** user | admin_user */
  recipientType: ColumnType<string, string, string>;

  notificationId: ColumnType<NotificationsId, NotificationsId, NotificationsId>;

  status: ColumnType<string, string, string>;

  readAt: ColumnType<string | null, string | null, string | null>;

  createdAt: ColumnType<string, string, string>;

  updatedAt: ColumnType<string, string, string>;
}

export type NotificationRecipients = Selectable<NotificationRecipientsTable>;

export type NewNotificationRecipients = Insertable<NotificationRecipientsTable>;

export type NotificationRecipientsUpdate = Updateable<NotificationRecipientsTable>;
