"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		// Insert root level roles first (parent_id = null)
		await queryInterface.bulkInsert("roles", [
			{
				id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
				name: "President Director",
				description: null,
				parent_id: null,
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 1 roles (children of root roles)
		await queryInterface.bulkInsert("roles", [
			{
				id: "CysYGuQiUkYSmoUfMHk6l",
				name: "HSE Representative",
				description: null,
				parent_id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "qneH46X7zQUZLn83Epohc",
				name: "Security",
				description: null,
				parent_id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "xKqCTTtDhdX_ORcCDfpiS",
				name: "Director",
				description: null,
				parent_id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 2 roles (children of level 1 roles)
		await queryInterface.bulkInsert("roles", [
			{
				id: "HleKDskuENqice4M7xD7t",
				name: "Site Coordinator PAMA",
				description: null,
				parent_id: "xKqCTTtDhdX_ORcCDfpiS",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "vRiTQrxjVuMI475p0MjHT",
				name: "General Ops. Manager",
				description: null,
				parent_id: "xKqCTTtDhdX_ORcCDfpiS",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 3 roles (children of level 2 roles)
		await queryInterface.bulkInsert("roles", [
			{
				id: "f5hYssHIk4Fo_YueMZ7Z7",
				name: "Finance & HR Coord.",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "iqOUc2OGB_ZMFGER1WB1Q",
				name: "Sales & Marketing",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "ieDyOc72w0RyoeFJfoHv7",
				name: "Spv. Workshop",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "EMEBzB5C41BSr15KC2VuF",
				name: "Operational Coord.",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "u6C6r1GRaJOyIwSCTMzvj",
				name: "IT & Communication",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "w_4ADb3FPVLPZO7xln4qk",
				name: "Admin Purchasing",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "Oyd_nUMjzWV-d-UH0ZhuX",
				name: "Admin Marketing",
				description: null,
				parent_id: "vRiTQrxjVuMI475p0MjHT",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 4 roles (children of level 3 roles)
		await queryInterface.bulkInsert("roles", [
			// Subordinate Finance & HR Coord.
			{
				id: "Qe9HqcGqht2lAotODNgWw",
				name: "Finance",
				description: null,
				parent_id: "f5hYssHIk4Fo_YueMZ7Z7",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "T2I6lZ7PyTma1IC0VzNN0",
				name: "HR/GA",
				description: null,
				parent_id: "f5hYssHIk4Fo_YueMZ7Z7",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate Spv. Workshop
			{
				id: "GhUU3RPYOcZ10sZSLqISr",
				name: "Lead Workshop Admin",
				description: null,
				parent_id: "ieDyOc72w0RyoeFJfoHv7",
				created_at: new Date(),
				updated_at: new Date(),
			},
		

			// Subordinate Operasional Coord.
			{
				id: "JnDGjYcIwzpdKVv2j_-iT",
				name: "General Purpose",
				description: null,
				parent_id: "EMEBzB5C41BSr15KC2VuF",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate IT & Communication
			{
				id: "k_4XW7RDAmqsWm7lECQSe",
				name: "Content Creative",
				description: null,
				parent_id: "u6C6r1GRaJOyIwSCTMzvj",
				created_at: new Date(),
				updated_at: new Date(),
			},
			
		]);

		// Insert level 5 roles (children of level 4 roles)
		await queryInterface.bulkInsert("roles", [
			// Subordinate Finance
			{
				id: "6EKgeycRK4lIUpXw9FbSn",
				name: "Acc Staff",
				description: null,
				parent_id: "Qe9HqcGqht2lAotODNgWw",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate Lear Workshop Admin
			{
				id: "T2qJgkLwYnAdap99Pu9Nk",
				name: "Workshop Admin",
				description: null,
				parent_id: "GhUU3RPYOcZ10sZSLqISr",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate Admin Purchasing
			{
				id: "WFFU22_Aeny4Ar8hUe0Fn",
				name: "Staff Purchasing",
				description: null,
				parent_id: "w_4ADb3FPVLPZO7xln4qk",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 6 roles (children of level 5 roles)
		await queryInterface.bulkInsert("roles", [
			// Spv Workshop subordinates
			{
				id: "7tMUwP5JZuABepX-fWSvw",
				name: "Electrical Coord.",
				description: null,
				parent_id: "ieDyOc72w0RyoeFJfoHv7",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "tU17Ib4N7PzHPfT85qM8u",
				name: "Group Leader",
				description: null,
				parent_id: "ieDyOc72w0RyoeFJfoHv7",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "wHJEzwXi7r87o-qsXteRg",
				name: "Fabrication Coord.",
				description: null,
				parent_id: "ieDyOc72w0RyoeFJfoHv7",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 7 roles (children of level 6 roles)
		await queryInterface.bulkInsert("roles", [
			// Subordinate electrical coord
			{
				id: "8RqOsFXOZ_utTR4X8yGcb",
				name: "Sr. Electrician",
				description: null,
				parent_id: "7tMUwP5JZuABepX-fWSvw",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate Group leader
			{
				id: "q-gPheO1FHkDVlcfwlmKS",
				name: "Sr. Mechanic",
				description: null,
				parent_id: "tU17Ib4N7PzHPfT85qM8u",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);

		// Insert level 8 roles (children of level 7 roles)
		await queryInterface.bulkInsert("roles", [
			// Subodinate Sr.Electician
			{
				id: "JTUPtuwL0mdX8LeZVAsZL",
				name: "Jr. Electrician",
				description: null,
				parent_id: "8RqOsFXOZ_utTR4X8yGcb",
				created_at: new Date(),
				updated_at: new Date(),
			},

			// Subordinate Senior Mechanic
			{
				id: "9d9914f9a164a318f06dc",
				name: "Washpad Section",
				description: null,
				parent_id: "q-gPheO1FHkDVlcfwlmKS",
				created_at: new Date(),
				updated_at: new Date(),
			},
			{
				id: "e1abb2bff489dbcfad6a7",
				name: "Jr. Mechanic",
				description: null,
				parent_id: "q-gPheO1FHkDVlcfwlmKS",
				created_at: new Date(),
				updated_at: new Date(),
			},
		]);
	},

	async down(queryInterface, Sequelize) {
		await queryInterface.bulkDelete("roles_permissions", null, {});
		await queryInterface.bulkDelete("roles", null, {});
		await queryInterface.bulkDelete("permissions", null, {});
	},
};
