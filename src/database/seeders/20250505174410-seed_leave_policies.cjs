"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

    await queryInterface.bulkInsert("leave_policies", [
      {
        id: process.env.ANNUAL_LEAVE_POLICY_ID,
        name: "<PERSON><PERSON>",
        description: "Policy kuota untuk cuti tahunan",
        quota: 20,
        effective_year: 2025, // Policy di set pertahun
        is_active: true,
        is_counted_as_present: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: process.env.MARRIAGE_LEAVE_POLICY_ID,
        name: "<PERSON><PERSON>",
        description: "Policy kuota untuk cuti menikah",
        quota: 3,
        effective_year: 2025, // Policy di set pertahun
        is_active: true,
        is_counted_as_present: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: process.env.SICK_LEAVE_POLICY_ID,
        name: "<PERSON>i <PERSON>kit",
        description: "Policy kuota untuk cuti sakit",
        quota: 30,
        effective_year: 2025, // Policy di set pertahun
        is_active: true,
        is_counted_as_present: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: process.env.MATERNITY_LEAVE_POLICY_ID,
        name: "Cuti Melahirkan",
        description: "Policy kuota untuk cuti melahirkan",
        quota: 90,
        effective_year: 2025, // Policy di set pertahun
        is_active: true,
        is_counted_as_present: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("leave_policies", null, {});
  },
};
