"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

     await queryInterface.bulkInsert("attendance_rules", [
      {
        id: nanoid(),
        worksite_id: process.env.DEFAULT_WORK_SITE_ID,

        check_in_start_time: "06:00",
        check_in_end_time: "10:15",
        check_in_tolerance_minutes: 30,

        check_out_start_time: "17:00",
        check_out_end_time: "23:59",
        check_out_tolerance_minutes: 30,

        break_start_time: "12:00",
        break_end_time: "13:00",
        break_tolerance_minutes: 30,

        return_start_time: "13:00",
        return_end_time: "14:00",
        return_tolerance_minutes: 30,

        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("attendance_rules", {
      worksite_id: process.env.DEFAULT_WORK_SITE_ID,
    });
  },
};
