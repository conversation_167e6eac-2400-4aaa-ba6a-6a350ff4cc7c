"use strict";

const { config } = require("dotenv");
const bcrypt = require("bcryptjs");

config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

    await queryInterface.bulkInsert(
      "administrator_users",
      [
        {
          id: nanoid(),
          name: "<PERSON><PERSON> Administrator",
          email: "<EMAIL>",
          password: bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD),
          email_verified: true,
          user_id: process.env.PRESIDENT_DIRECTOR_ID,
          image: "public/uploads/users/default-user.png",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ],
      {}
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('administrator_users', null, {});
  },
};
