"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

    await queryInterface.bulkInsert("leave_balances", [
      {
        id: nanoid(),
        user_id: process.env.PRESIDENT_DIRECTOR_ID,
        leave_policy_id: process.env.ANNUAL_LEAVE_POLICY_ID,
        quota: 20,
        used: 0,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: nanoid(),
        user_id: process.env.PRESIDENT_DIRECTOR_ID,
        leave_policy_id: process.env.MARRIAGE_LEAVE_POLICY_ID,
        quota: 3,
        used: 0,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: nanoid(),
        user_id: process.env.PRESIDENT_DIRECTOR_ID,
        leave_policy_id: process.env.SICK_LEAVE_POLICY_ID,
        quota: 30,
        used: 0,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: nanoid(),
        user_id: process.env.PRESIDENT_DIRECTOR_ID,
        leave_policy_id: process.env.MATERNITY_LEAVE_POLICY_ID,
        quota: 30,
        used: 0,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('leave_balances', null, {});
  },
};
