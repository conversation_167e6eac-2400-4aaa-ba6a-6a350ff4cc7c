"use strict";

const { config } = require("dotenv");
const bcrypt = require("bcryptjs");

config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");
    const presidentDirectorId = process.env.PRESIDENT_DIRECTOR_ID;

    await queryInterface.bulkInsert(
      "users",
      [
        {
          id: presidentDirectorId,
          name: "<PERSON><PERSON>",
          email: "<EMAIL>",
          nik: nanoid(),
          mobile_number: nanoid(),
          email_verified: true,
          image: "public/uploads/users/default-user.png",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ],
      {}
    );

    // await queryInterface.

    await queryInterface.bulkInsert(
      "accounts",
      [
        {
          id: nanoid(),
          user_id: presidentDire<PERSON>Id,
          account_id: president<PERSON>ire<PERSON><PERSON>d,
          provider_id: "credential",
          access_token: null,
          refresh_token: null,
          access_token_expires_at: null,
          refresh_token_expires_at: null,
          scope: null,
          id_token: null,
          password: bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD),
          created_at: new Date(),
          updated_at: new Date(),
        },
      ],
      {}
    );

    await queryInterface.bulkInsert(
      "users_roles",
      [
        {
          user_id: presidentDirectorId,
          role_id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
        },
      ],
      {}
    );

    await queryInterface.bulkInsert("user_hierarchies", [
      {
        id: nanoid(),
        user_id: presidentDirectorId,
        role_id: process.env.PRESIDENT_DIRECTOR_ROLE_ID,
        supervisor_id: null,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete("user_hierarchies", null, {});
    await queryInterface.bulkDelete("users_roles", null, {});
    await queryInterface.bulkDelete("accounts", null, {});
    await queryInterface.bulkDelete("users", null, {});
  },
};
