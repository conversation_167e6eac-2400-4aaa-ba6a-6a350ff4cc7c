"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

    await queryInterface.bulkInsert("users_worksites", [
      { 
        worksite_id: process.env.DEFAULT_WORK_SITE_ID,
        user_id: process.env.PRESIDENT_DIRECTOR_ID, 
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users_worksites', null, {});
  },
};
