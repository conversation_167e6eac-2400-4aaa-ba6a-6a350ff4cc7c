"use strict";

const { config } = require("dotenv");
config();

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { nanoid } = await import("nanoid");

    await queryInterface.bulkInsert("worksites", [
      {
        id: process.env.DEFAULT_WORK_SITE_ID,
        name: "<PERSON><PERSON><PERSON>",
        type: "office",
        address: "Jalan Haji Agus Salim No. 75 Samarinda Kota, Samarinda",
        description: "Alamat kantor utama",
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('worksites', null, {});
  },
};
