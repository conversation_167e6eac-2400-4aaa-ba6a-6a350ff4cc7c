--
-- PostgreSQL database dump
--

-- Dumped from database version 16.9
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE ONLY public.violations DROP CONSTRAINT violations_violation_type_id_fkey;
ALTER TABLE ONLY public.violations DROP CONSTRAINT violations_user_id_fkey;
ALTER TABLE ONLY public.violations DROP CONSTRAINT violations_recorded_by_fkey;
ALTER TABLE ONLY public.users_worksites DROP CONSTRAINT users_worksites_worksite_id_fkey;
ALTER TABLE ONLY public.users_worksites DROP CONSTRAINT users_worksites_user_id_fkey;
ALTER TABLE ONLY public.users_roles DROP CONSTRAINT users_roles_user_id_fkey;
ALTER TABLE ONLY public.users_roles DROP CONSTRAINT users_roles_role_id_fkey;
ALTER TABLE ONLY public.user_hierarchies DROP CONSTRAINT user_hierarchies_user_id_fkey;
ALTER TABLE ONLY public.user_hierarchies DROP CONSTRAINT user_hierarchies_supervisor_id_fkey;
ALTER TABLE ONLY public.user_hierarchies DROP CONSTRAINT user_hierarchies_role_id_fkey;
ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_assigner_id_fkey;
ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_assignee_id_fkey;
ALTER TABLE ONLY public.task_submissions DROP CONSTRAINT task_submissions_user_id_fkey;
ALTER TABLE ONLY public.task_submissions DROP CONSTRAINT task_submissions_task_id_fkey;
ALTER TABLE ONLY public.task_submissions DROP CONSTRAINT task_submissions_reviewed_by_fkey;
ALTER TABLE ONLY public.roles_permissions DROP CONSTRAINT roles_permissions_role_id_fkey;
ALTER TABLE ONLY public.roles_permissions DROP CONSTRAINT roles_permissions_permission_id_fkey;
ALTER TABLE ONLY public.roles DROP CONSTRAINT roles_parent_id_fkey;
ALTER TABLE ONLY public.office_leaves DROP CONSTRAINT office_leaves_user_id_fkey;
ALTER TABLE ONLY public.office_leaves DROP CONSTRAINT office_leaves_reviewed_by_fkey;
ALTER TABLE ONLY public.notification_recipients DROP CONSTRAINT notification_recipients_notification_id_fkey;
ALTER TABLE ONLY public.leave_requests DROP CONSTRAINT leave_requests_user_id_fkey;
ALTER TABLE ONLY public.leave_requests DROP CONSTRAINT leave_requests_reviewed_by_fkey;
ALTER TABLE ONLY public.leave_requests DROP CONSTRAINT leave_requests_leave_policy_id_fkey;
ALTER TABLE ONLY public.leave_requests DROP CONSTRAINT leave_requests_leave_balance_id_fkey;
ALTER TABLE ONLY public.leave_balances DROP CONSTRAINT leave_balances_user_id_fkey;
ALTER TABLE ONLY public.leave_balances DROP CONSTRAINT leave_balances_leave_policy_id_fkey;
ALTER TABLE ONLY public.kpi_records DROP CONSTRAINT kpi_records_user_id_fkey;
ALTER TABLE ONLY public.incentive_records DROP CONSTRAINT incentive_records_user_id_fkey;
ALTER TABLE ONLY public.face_vectors DROP CONSTRAINT face_vectors_user_id_fkey;
ALTER TABLE ONLY public.attendance_rules DROP CONSTRAINT attendance_rules_worksite_id_fkey;
ALTER TABLE ONLY public.attendance_logs DROP CONSTRAINT attendance_logs_worksite_id_fkey;
ALTER TABLE ONLY public.attendance_logs DROP CONSTRAINT attendance_logs_user_id_fkey;
ALTER TABLE ONLY public.administrator_users DROP CONSTRAINT administrator_users_user_id_fkey;
ALTER TABLE ONLY public.administrator_users DROP CONSTRAINT administrator_users_role_id_fkey;
ALTER TABLE ONLY public.accounts DROP CONSTRAINT accounts_user_id_fkey;
DROP INDEX public.violations_violation_type_id;
DROP INDEX public.violations_violation_date;
DROP INDEX public.violations_user_id;
DROP INDEX public.violation_types_name;
DROP INDEX public.violation_types_is_active;
DROP INDEX public.verifications_value;
DROP INDEX public.verifications_identifier;
DROP INDEX public.users_worksites_user_id_is_main;
DROP INDEX public.users_nik;
DROP INDEX public.users_mobile_number;
DROP INDEX public.users_email;
DROP INDEX public.user_hierarchies_user_id;
DROP INDEX public.user_hierarchies_supervisor_id;
DROP INDEX public.user_hierarchies_role_id;
DROP INDEX public.user_hierarchies_is_active;
DROP INDEX public.tasks_status_assigner_id;
DROP INDEX public.tasks_status_assignee_id;
DROP INDEX public.tasks_deadline;
DROP INDEX public.tasks_assigner_id;
DROP INDEX public.tasks_assignee_id;
DROP INDEX public.task_submissions_user_id;
DROP INDEX public.task_submissions_task_id_user_id_submission_number;
DROP INDEX public.task_submissions_task_id_user_id_status;
DROP INDEX public.task_submissions_task_id;
DROP INDEX public.task_submissions_reviewed_by;
DROP INDEX public.sessions_token;
DROP INDEX public.sessions_owner_type_owner_id;
DROP INDEX public.roles_parent_id;
DROP INDEX public.roles_name;
DROP INDEX public.permissions_name;
DROP INDEX public.office_leaves_user_id;
DROP INDEX public.office_leaves_start_time;
DROP INDEX public.office_leaves_reviewed_by;
DROP INDEX public.office_leaves_reviewed_at;
DROP INDEX public.office_leaves_is_official_business;
DROP INDEX public.office_leaves_end_time;
DROP INDEX public.notifications_actor_id_actor_type;
DROP INDEX public.notification_recipients_recipient_id_recipient_type;
DROP INDEX public.notification_recipients_notification_id;
DROP INDEX public.leave_requests_user_id_status_start_date_end_date;
DROP INDEX public.leave_requests_user_id_status;
DROP INDEX public.leave_requests_user_id;
DROP INDEX public.leave_policies_name_effective_year;
DROP INDEX public.leave_policies_is_active;
DROP INDEX public.leave_balances_user_id_leave_policy_id;
DROP INDEX public.leave_balances_is_active;
DROP INDEX public.kpi_records_user_id_period_period_type;
DROP INDEX public.kpi_records_user_id;
DROP INDEX public.kpi_records_period_type;
DROP INDEX public.kpi_records_period;
DROP INDEX public.incentive_records_user_id_incentive_type_period;
DROP INDEX public.incentive_records_user_id;
DROP INDEX public.incentive_records_period;
DROP INDEX public.incentive_records_is_eligible;
DROP INDEX public.incentive_records_incentive_type;
DROP INDEX public.holidays_date;
DROP INDEX public.fcm_tokens_token;
DROP INDEX public.fcm_tokens_owner_id_owner_type;
DROP INDEX public.face_vectors_user_id;
DROP INDEX public.attendance_rules_worksite_id;
DROP INDEX public.attendance_logs_user_id_log_date_type;
DROP INDEX public.attendance_logs_user_id_log_date;
DROP INDEX public.accounts_user_id;
ALTER TABLE ONLY public.worksites DROP CONSTRAINT worksites_pkey;
ALTER TABLE ONLY public.violations DROP CONSTRAINT violations_pkey;
ALTER TABLE ONLY public.violation_types DROP CONSTRAINT violation_types_pkey;
ALTER TABLE ONLY public.violation_types DROP CONSTRAINT violation_types_name_key;
ALTER TABLE ONLY public.verifications DROP CONSTRAINT verifications_pkey;
ALTER TABLE ONLY public.users_worksites DROP CONSTRAINT users_worksites_pkey;
ALTER TABLE ONLY public.users_roles DROP CONSTRAINT users_roles_pkey;
ALTER TABLE ONLY public.users DROP CONSTRAINT users_pkey;
ALTER TABLE ONLY public.users DROP CONSTRAINT users_nik_key;
ALTER TABLE ONLY public.users DROP CONSTRAINT users_mobile_number_key;
ALTER TABLE ONLY public.users DROP CONSTRAINT users_email_key;
ALTER TABLE ONLY public.user_hierarchies DROP CONSTRAINT user_hierarchies_user_id_key;
ALTER TABLE ONLY public.user_hierarchies DROP CONSTRAINT user_hierarchies_pkey;
ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_pkey;
ALTER TABLE ONLY public.task_submissions DROP CONSTRAINT task_submissions_pkey;
ALTER TABLE ONLY public.sessions DROP CONSTRAINT sessions_pkey;
ALTER TABLE ONLY public.roles DROP CONSTRAINT roles_pkey;
ALTER TABLE ONLY public.roles_permissions DROP CONSTRAINT roles_permissions_pkey;
ALTER TABLE ONLY public.roles DROP CONSTRAINT roles_name_key;
ALTER TABLE ONLY public.permissions DROP CONSTRAINT permissions_pkey;
ALTER TABLE ONLY public.permissions DROP CONSTRAINT permissions_name_key;
ALTER TABLE ONLY public.office_leaves DROP CONSTRAINT office_leaves_pkey;
ALTER TABLE ONLY public.notifications DROP CONSTRAINT notifications_pkey;
ALTER TABLE ONLY public.notification_recipients DROP CONSTRAINT notification_recipients_pkey;
ALTER TABLE ONLY public.leave_requests DROP CONSTRAINT leave_requests_pkey;
ALTER TABLE ONLY public.leave_policies DROP CONSTRAINT leave_policies_pkey;
ALTER TABLE ONLY public.leave_balances DROP CONSTRAINT leave_balances_pkey;
ALTER TABLE ONLY public.kpi_records DROP CONSTRAINT kpi_records_pkey;
ALTER TABLE ONLY public.incentive_records DROP CONSTRAINT incentive_records_pkey;
ALTER TABLE ONLY public.holidays DROP CONSTRAINT holidays_pkey;
ALTER TABLE ONLY public.holidays DROP CONSTRAINT holidays_date_key;
ALTER TABLE ONLY public.fcm_tokens DROP CONSTRAINT fcm_tokens_token_key;
ALTER TABLE ONLY public.fcm_tokens DROP CONSTRAINT fcm_tokens_pkey;
ALTER TABLE ONLY public.face_vectors DROP CONSTRAINT face_vectors_pkey;
ALTER TABLE ONLY public.attendance_rules DROP CONSTRAINT attendance_rules_pkey;
ALTER TABLE ONLY public.attendance_logs DROP CONSTRAINT attendance_logs_pkey;
ALTER TABLE ONLY public.administrator_users DROP CONSTRAINT administrator_users_pkey;
ALTER TABLE ONLY public.administrator_users DROP CONSTRAINT administrator_users_email_key;
ALTER TABLE ONLY public.accounts DROP CONSTRAINT accounts_pkey;
DROP TABLE public.worksites;
DROP TABLE public.violations;
DROP TABLE public.violation_types;
DROP TABLE public.verifications;
DROP TABLE public.users_worksites;
DROP TABLE public.users_roles;
DROP TABLE public.users;
DROP TABLE public.user_hierarchies;
DROP TABLE public.tasks;
DROP TABLE public.task_submissions;
DROP TABLE public.sessions;
DROP TABLE public.roles_permissions;
DROP TABLE public.roles;
DROP TABLE public.permissions;
DROP TABLE public.office_leaves;
DROP TABLE public.notifications;
DROP TABLE public.notification_recipients;
DROP TABLE public.leave_requests;
DROP TABLE public.leave_policies;
DROP TABLE public.leave_balances;
DROP TABLE public.kpi_records;
DROP TABLE public.incentive_records;
DROP TABLE public.holidays;
DROP TABLE public.fcm_tokens;
DROP TABLE public.face_vectors;
DROP TABLE public.attendance_rules;
DROP TABLE public.attendance_logs;
DROP TABLE public.administrator_users;
DROP TABLE public.accounts;
-- *not* dropping schema, since initdb creates it
--
-- Name: public; Type: SCHEMA; Schema: -; Owner: -
--

-- *not* creating schema, since initdb creates it


--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON SCHEMA public IS '';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.accounts (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    account_id text NOT NULL,
    provider_id text NOT NULL,
    access_token text,
    refresh_token text,
    access_token_expires_at timestamp with time zone,
    refresh_token_expires_at timestamp with time zone,
    scope text,
    id_token text,
    password text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: administrator_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.administrator_users (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    password text NOT NULL,
    role_id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    email_verified boolean DEFAULT false NOT NULL,
    image text DEFAULT 'public/uploads/users/default-user.png'::text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: attendance_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.attendance_logs (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    worksite_id character varying(255) NOT NULL,
    type character varying(255) NOT NULL,
    log_date date NOT NULL,
    log_time time without time zone NOT NULL,
    status character varying(255) NOT NULL,
    location_lat double precision NOT NULL,
    location_long double precision NOT NULL,
    similarity_score double precision NOT NULL,
    photo text NOT NULL,
    device_info text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN attendance_logs.worksite_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.attendance_logs.worksite_id IS 'Detected sites';


--
-- Name: attendance_rules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.attendance_rules (
    id character varying(255) NOT NULL,
    worksite_id character varying(255) NOT NULL,
    check_in_start_time time without time zone NOT NULL,
    check_in_end_time time without time zone NOT NULL,
    check_in_tolerance_minutes integer NOT NULL,
    check_out_start_time time without time zone NOT NULL,
    check_out_end_time time without time zone NOT NULL,
    check_out_tolerance_minutes integer NOT NULL,
    break_start_time time without time zone NOT NULL,
    break_end_time time without time zone NOT NULL,
    break_tolerance_minutes integer NOT NULL,
    return_start_time time without time zone NOT NULL,
    return_end_time time without time zone NOT NULL,
    return_tolerance_minutes integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    latitude double precision DEFAULT 0 NOT NULL,
    longitude double precision DEFAULT 0 NOT NULL,
    radius_in_meter integer DEFAULT 30 NOT NULL,
    timezone character varying(255) DEFAULT 'Asia/Jakarta'::character varying NOT NULL
);


--
-- Name: face_vectors; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.face_vectors (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    descriptor text NOT NULL,
    image_path character varying(255) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: fcm_tokens; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.fcm_tokens (
    id character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    owner_id character varying(255) NOT NULL,
    owner_type character varying(255) NOT NULL,
    "timestamp" timestamp with time zone NOT NULL
);


--
-- Name: holidays; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.holidays (
    id character varying(255) NOT NULL,
    date date NOT NULL,
    description character varying(255),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: incentive_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.incentive_records (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    incentive_type character varying(255) NOT NULL,
    period character varying(255) NOT NULL,
    is_eligible boolean NOT NULL,
    amount numeric(10,2),
    notes text,
    requirements jsonb NOT NULL,
    calculated_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN incentive_records.incentive_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.incentive_records.incentive_type IS 'ATTENDANCE, PRESENCE, MOTOR';


--
-- Name: COLUMN incentive_records.period; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.incentive_records.period IS 'Period format based on incentive type cut-off';


--
-- Name: kpi_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.kpi_records (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    period character varying(255) NOT NULL,
    period_type character varying(255) NOT NULL,
    task_average_score double precision DEFAULT '0'::double precision NOT NULL,
    attendance_percentage double precision DEFAULT '0'::double precision NOT NULL,
    violation_penalty double precision DEFAULT '0'::double precision NOT NULL,
    final_kpi_score double precision DEFAULT '0'::double precision NOT NULL,
    total_tasks integer DEFAULT 0 NOT NULL,
    total_work_days integer DEFAULT 0 NOT NULL,
    total_attendance_days integer DEFAULT 0 NOT NULL,
    total_violations integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN kpi_records.period; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.kpi_records.period IS 'Format: YYYY-MM for monthly, YYYY for yearly';


--
-- Name: COLUMN kpi_records.period_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.kpi_records.period_type IS 'MONTHLY, YEARLY';


--
-- Name: leave_balances; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.leave_balances (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    leave_policy_id character varying(255) NOT NULL,
    quota integer NOT NULL,
    used integer NOT NULL,
    is_active boolean NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: leave_policies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.leave_policies (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    effective_year integer NOT NULL,
    quota integer NOT NULL,
    is_counted_as_present boolean NOT NULL,
    is_active boolean NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: leave_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.leave_requests (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    leave_balance_id character varying(255) NOT NULL,
    leave_policy_id character varying(255) NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    reason text NOT NULL,
    status character varying(255) NOT NULL,
    effective_leave_days integer NOT NULL,
    document_url text,
    reviewed_by character varying(255),
    reviewed_at timestamp with time zone,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: notification_recipients; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notification_recipients (
    id character varying(255) NOT NULL,
    recipient_id character varying(255) NOT NULL,
    recipient_type character varying(255) NOT NULL,
    notification_id character varying(255) NOT NULL,
    status character varying(255) NOT NULL,
    read_at timestamp with time zone,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN notification_recipients.recipient_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.notification_recipients.recipient_type IS 'user | admin_user';


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    id character varying(255) NOT NULL,
    type character varying(255) NOT NULL,
    entity_type character varying(255),
    entity_id character varying(255),
    actor_id character varying(255),
    actor_type character varying(255),
    message_template text NOT NULL,
    message_data text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN notifications.actor_type; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.notifications.actor_type IS 'user | admin_user';


--
-- Name: office_leaves; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.office_leaves (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    date date NOT NULL,
    is_official_business boolean,
    reviewed_by character varying(255),
    reviewed_at timestamp with time zone,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.permissions (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    description character varying(255)
);


--
-- Name: roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    description character varying(255),
    parent_id character varying(255),
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: roles_permissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.roles_permissions (
    role_id character varying(255) NOT NULL,
    permission_id character varying(255) NOT NULL
);


--
-- Name: sessions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sessions (
    id character varying(255) NOT NULL,
    owner_id character varying(255) NOT NULL,
    owner_type character varying(255) NOT NULL,
    token text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    ip_address text,
    user_agent text,
    impersonated_by character varying(255),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: task_submissions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.task_submissions (
    id character varying(255) NOT NULL,
    task_id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    reviewed_by character varying(255),
    submission_number integer NOT NULL,
    document_urls text NOT NULL,
    status character varying(255) DEFAULT 'PENDING_REVIEW'::character varying NOT NULL,
    review_date timestamp with time zone,
    submitted_at timestamp with time zone NOT NULL,
    feedback text,
    notes text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tasks (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    assigner_id character varying(255) NOT NULL,
    assignee_id character varying(255) NOT NULL,
    deadline date NOT NULL,
    description text NOT NULL,
    document_urls text NOT NULL,
    rating_point integer,
    status character varying(255) DEFAULT 'PENDING'::character varying NOT NULL,
    rejection_reason text,
    final_feedback text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN tasks.rejection_reason; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.tasks.rejection_reason IS 'Reason for task is rejected by assignees';


--
-- Name: COLUMN tasks.final_feedback; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.tasks.final_feedback IS 'Final feedback from assigner to assignee, this will same as feedback in task submission when task is completed';


--
-- Name: user_hierarchies; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_hierarchies (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    role_id character varying(255) NOT NULL,
    supervisor_id character varying(255),
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: COLUMN user_hierarchies.user_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_hierarchies.user_id IS 'One user can only have one active hierarchy record';


--
-- Name: COLUMN user_hierarchies.role_id; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON COLUMN public.user_hierarchies.role_id IS 'Reference to role that determines organizational level';


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    nik character varying(255) NOT NULL,
    mobile_number character varying(255) NOT NULL,
    email_verified boolean DEFAULT false NOT NULL,
    image text DEFAULT 'public/uploads/users/default-user.png'::text NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: users_roles; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users_roles (
    role_id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL
);


--
-- Name: users_worksites; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users_worksites (
    worksite_id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    is_main boolean DEFAULT true NOT NULL
);


--
-- Name: verifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.verifications (
    id character varying(255) NOT NULL,
    identifier character varying(255) NOT NULL,
    value character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: violation_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.violation_types (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    penalty_points integer NOT NULL,
    punishment character varying(255) NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: violations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.violations (
    id character varying(255) NOT NULL,
    user_id character varying(255) NOT NULL,
    violation_type_id character varying(255) NOT NULL,
    violation_date date NOT NULL,
    notes text,
    recorded_by character varying(255) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: worksites; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.worksites (
    id character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    type character varying(255) NOT NULL,
    address text NOT NULL,
    description text,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: administrator_users administrator_users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.administrator_users
    ADD CONSTRAINT administrator_users_email_key UNIQUE (email);


--
-- Name: administrator_users administrator_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.administrator_users
    ADD CONSTRAINT administrator_users_pkey PRIMARY KEY (id);


--
-- Name: attendance_logs attendance_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendance_logs
    ADD CONSTRAINT attendance_logs_pkey PRIMARY KEY (id);


--
-- Name: attendance_rules attendance_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendance_rules
    ADD CONSTRAINT attendance_rules_pkey PRIMARY KEY (id);


--
-- Name: face_vectors face_vectors_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.face_vectors
    ADD CONSTRAINT face_vectors_pkey PRIMARY KEY (id);


--
-- Name: fcm_tokens fcm_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fcm_tokens
    ADD CONSTRAINT fcm_tokens_pkey PRIMARY KEY (id);


--
-- Name: fcm_tokens fcm_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.fcm_tokens
    ADD CONSTRAINT fcm_tokens_token_key UNIQUE (token);


--
-- Name: holidays holidays_date_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.holidays
    ADD CONSTRAINT holidays_date_key UNIQUE (date);


--
-- Name: holidays holidays_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.holidays
    ADD CONSTRAINT holidays_pkey PRIMARY KEY (id);


--
-- Name: incentive_records incentive_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.incentive_records
    ADD CONSTRAINT incentive_records_pkey PRIMARY KEY (id);


--
-- Name: kpi_records kpi_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.kpi_records
    ADD CONSTRAINT kpi_records_pkey PRIMARY KEY (id);


--
-- Name: leave_balances leave_balances_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT leave_balances_pkey PRIMARY KEY (id);


--
-- Name: leave_policies leave_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_policies
    ADD CONSTRAINT leave_policies_pkey PRIMARY KEY (id);


--
-- Name: leave_requests leave_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_pkey PRIMARY KEY (id);


--
-- Name: notification_recipients notification_recipients_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_recipients
    ADD CONSTRAINT notification_recipients_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: office_leaves office_leaves_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.office_leaves
    ADD CONSTRAINT office_leaves_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_name_key UNIQUE (name);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: roles roles_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_name_key UNIQUE (name);


--
-- Name: roles_permissions roles_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles_permissions
    ADD CONSTRAINT roles_permissions_pkey PRIMARY KEY (role_id, permission_id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: task_submissions task_submissions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.task_submissions
    ADD CONSTRAINT task_submissions_pkey PRIMARY KEY (id);


--
-- Name: tasks tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);


--
-- Name: user_hierarchies user_hierarchies_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_hierarchies
    ADD CONSTRAINT user_hierarchies_pkey PRIMARY KEY (id);


--
-- Name: user_hierarchies user_hierarchies_user_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_hierarchies
    ADD CONSTRAINT user_hierarchies_user_id_key UNIQUE (user_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_mobile_number_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_mobile_number_key UNIQUE (mobile_number);


--
-- Name: users users_nik_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_nik_key UNIQUE (nik);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users_roles users_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_roles
    ADD CONSTRAINT users_roles_pkey PRIMARY KEY (role_id, user_id);


--
-- Name: users_worksites users_worksites_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_worksites
    ADD CONSTRAINT users_worksites_pkey PRIMARY KEY (worksite_id, user_id);


--
-- Name: verifications verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.verifications
    ADD CONSTRAINT verifications_pkey PRIMARY KEY (id);


--
-- Name: violation_types violation_types_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violation_types
    ADD CONSTRAINT violation_types_name_key UNIQUE (name);


--
-- Name: violation_types violation_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violation_types
    ADD CONSTRAINT violation_types_pkey PRIMARY KEY (id);


--
-- Name: violations violations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violations
    ADD CONSTRAINT violations_pkey PRIMARY KEY (id);


--
-- Name: worksites worksites_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.worksites
    ADD CONSTRAINT worksites_pkey PRIMARY KEY (id);


--
-- Name: accounts_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX accounts_user_id ON public.accounts USING btree (user_id);


--
-- Name: attendance_logs_user_id_log_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX attendance_logs_user_id_log_date ON public.attendance_logs USING btree (user_id, log_date);


--
-- Name: attendance_logs_user_id_log_date_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX attendance_logs_user_id_log_date_type ON public.attendance_logs USING btree (user_id, log_date, type);


--
-- Name: attendance_rules_worksite_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX attendance_rules_worksite_id ON public.attendance_rules USING btree (worksite_id);


--
-- Name: face_vectors_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX face_vectors_user_id ON public.face_vectors USING btree (user_id);


--
-- Name: fcm_tokens_owner_id_owner_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX fcm_tokens_owner_id_owner_type ON public.fcm_tokens USING btree (owner_id, owner_type);


--
-- Name: fcm_tokens_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX fcm_tokens_token ON public.fcm_tokens USING btree (token);


--
-- Name: holidays_date; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX holidays_date ON public.holidays USING btree (date);


--
-- Name: incentive_records_incentive_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX incentive_records_incentive_type ON public.incentive_records USING btree (incentive_type);


--
-- Name: incentive_records_is_eligible; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX incentive_records_is_eligible ON public.incentive_records USING btree (is_eligible);


--
-- Name: incentive_records_period; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX incentive_records_period ON public.incentive_records USING btree (period);


--
-- Name: incentive_records_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX incentive_records_user_id ON public.incentive_records USING btree (user_id);


--
-- Name: incentive_records_user_id_incentive_type_period; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX incentive_records_user_id_incentive_type_period ON public.incentive_records USING btree (user_id, incentive_type, period);


--
-- Name: kpi_records_period; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX kpi_records_period ON public.kpi_records USING btree (period);


--
-- Name: kpi_records_period_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX kpi_records_period_type ON public.kpi_records USING btree (period_type);


--
-- Name: kpi_records_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX kpi_records_user_id ON public.kpi_records USING btree (user_id);


--
-- Name: kpi_records_user_id_period_period_type; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX kpi_records_user_id_period_period_type ON public.kpi_records USING btree (user_id, period, period_type);


--
-- Name: leave_balances_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX leave_balances_is_active ON public.leave_balances USING btree (is_active);


--
-- Name: leave_balances_user_id_leave_policy_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX leave_balances_user_id_leave_policy_id ON public.leave_balances USING btree (user_id, leave_policy_id);


--
-- Name: leave_policies_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX leave_policies_is_active ON public.leave_policies USING btree (is_active);


--
-- Name: leave_policies_name_effective_year; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX leave_policies_name_effective_year ON public.leave_policies USING btree (name, effective_year);


--
-- Name: leave_requests_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX leave_requests_user_id ON public.leave_requests USING btree (user_id);


--
-- Name: leave_requests_user_id_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX leave_requests_user_id_status ON public.leave_requests USING btree (user_id, status);


--
-- Name: leave_requests_user_id_status_start_date_end_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX leave_requests_user_id_status_start_date_end_date ON public.leave_requests USING btree (user_id, status, start_date, end_date);


--
-- Name: notification_recipients_notification_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX notification_recipients_notification_id ON public.notification_recipients USING btree (notification_id);


--
-- Name: notification_recipients_recipient_id_recipient_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX notification_recipients_recipient_id_recipient_type ON public.notification_recipients USING btree (recipient_id, recipient_type);


--
-- Name: notifications_actor_id_actor_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX notifications_actor_id_actor_type ON public.notifications USING btree (actor_id, actor_type);


--
-- Name: office_leaves_end_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_end_time ON public.office_leaves USING btree (end_time);


--
-- Name: office_leaves_is_official_business; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_is_official_business ON public.office_leaves USING btree (is_official_business);


--
-- Name: office_leaves_reviewed_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_reviewed_at ON public.office_leaves USING btree (reviewed_at);


--
-- Name: office_leaves_reviewed_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_reviewed_by ON public.office_leaves USING btree (reviewed_by);


--
-- Name: office_leaves_start_time; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_start_time ON public.office_leaves USING btree (start_time);


--
-- Name: office_leaves_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX office_leaves_user_id ON public.office_leaves USING btree (user_id);


--
-- Name: permissions_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX permissions_name ON public.permissions USING btree (name);


--
-- Name: roles_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX roles_name ON public.roles USING btree (name);


--
-- Name: roles_parent_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX roles_parent_id ON public.roles USING btree (parent_id);


--
-- Name: sessions_owner_type_owner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sessions_owner_type_owner_id ON public.sessions USING btree (owner_type, owner_id);


--
-- Name: sessions_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX sessions_token ON public.sessions USING btree (token);


--
-- Name: task_submissions_reviewed_by; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX task_submissions_reviewed_by ON public.task_submissions USING btree (reviewed_by);


--
-- Name: task_submissions_task_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX task_submissions_task_id ON public.task_submissions USING btree (task_id);


--
-- Name: task_submissions_task_id_user_id_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX task_submissions_task_id_user_id_status ON public.task_submissions USING btree (task_id, user_id, status);


--
-- Name: task_submissions_task_id_user_id_submission_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX task_submissions_task_id_user_id_submission_number ON public.task_submissions USING btree (task_id, user_id, submission_number);


--
-- Name: task_submissions_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX task_submissions_user_id ON public.task_submissions USING btree (user_id);


--
-- Name: tasks_assignee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX tasks_assignee_id ON public.tasks USING btree (assignee_id);


--
-- Name: tasks_assigner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX tasks_assigner_id ON public.tasks USING btree (assigner_id);


--
-- Name: tasks_deadline; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX tasks_deadline ON public.tasks USING btree (deadline);


--
-- Name: tasks_status_assignee_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX tasks_status_assignee_id ON public.tasks USING btree (status, assignee_id);


--
-- Name: tasks_status_assigner_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX tasks_status_assigner_id ON public.tasks USING btree (status, assigner_id);


--
-- Name: user_hierarchies_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX user_hierarchies_is_active ON public.user_hierarchies USING btree (is_active);


--
-- Name: user_hierarchies_role_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX user_hierarchies_role_id ON public.user_hierarchies USING btree (role_id);


--
-- Name: user_hierarchies_supervisor_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX user_hierarchies_supervisor_id ON public.user_hierarchies USING btree (supervisor_id);


--
-- Name: user_hierarchies_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX user_hierarchies_user_id ON public.user_hierarchies USING btree (user_id);


--
-- Name: users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_email ON public.users USING btree (email);


--
-- Name: users_mobile_number; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_mobile_number ON public.users USING btree (mobile_number);


--
-- Name: users_nik; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_nik ON public.users USING btree (nik);


--
-- Name: users_worksites_user_id_is_main; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX users_worksites_user_id_is_main ON public.users_worksites USING btree (user_id, is_main);


--
-- Name: verifications_identifier; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX verifications_identifier ON public.verifications USING btree (identifier);


--
-- Name: verifications_value; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX verifications_value ON public.verifications USING btree (value);


--
-- Name: violation_types_is_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX violation_types_is_active ON public.violation_types USING btree (is_active);


--
-- Name: violation_types_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX violation_types_name ON public.violation_types USING btree (name);


--
-- Name: violations_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX violations_user_id ON public.violations USING btree (user_id);


--
-- Name: violations_violation_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX violations_violation_date ON public.violations USING btree (violation_date);


--
-- Name: violations_violation_type_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX violations_violation_type_id ON public.violations USING btree (violation_type_id);


--
-- Name: accounts accounts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: administrator_users administrator_users_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.administrator_users
    ADD CONSTRAINT administrator_users_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: administrator_users administrator_users_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.administrator_users
    ADD CONSTRAINT administrator_users_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: attendance_logs attendance_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendance_logs
    ADD CONSTRAINT attendance_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: attendance_logs attendance_logs_worksite_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendance_logs
    ADD CONSTRAINT attendance_logs_worksite_id_fkey FOREIGN KEY (worksite_id) REFERENCES public.worksites(id);


--
-- Name: attendance_rules attendance_rules_worksite_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attendance_rules
    ADD CONSTRAINT attendance_rules_worksite_id_fkey FOREIGN KEY (worksite_id) REFERENCES public.worksites(id);


--
-- Name: face_vectors face_vectors_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.face_vectors
    ADD CONSTRAINT face_vectors_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: incentive_records incentive_records_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.incentive_records
    ADD CONSTRAINT incentive_records_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: kpi_records kpi_records_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.kpi_records
    ADD CONSTRAINT kpi_records_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: leave_balances leave_balances_leave_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT leave_balances_leave_policy_id_fkey FOREIGN KEY (leave_policy_id) REFERENCES public.leave_policies(id);


--
-- Name: leave_balances leave_balances_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_balances
    ADD CONSTRAINT leave_balances_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: leave_requests leave_requests_leave_balance_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_leave_balance_id_fkey FOREIGN KEY (leave_balance_id) REFERENCES public.leave_balances(id);


--
-- Name: leave_requests leave_requests_leave_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_leave_policy_id_fkey FOREIGN KEY (leave_policy_id) REFERENCES public.leave_policies(id);


--
-- Name: leave_requests leave_requests_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: leave_requests leave_requests_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.leave_requests
    ADD CONSTRAINT leave_requests_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: notification_recipients notification_recipients_notification_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_recipients
    ADD CONSTRAINT notification_recipients_notification_id_fkey FOREIGN KEY (notification_id) REFERENCES public.notifications(id);


--
-- Name: office_leaves office_leaves_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.office_leaves
    ADD CONSTRAINT office_leaves_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: office_leaves office_leaves_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.office_leaves
    ADD CONSTRAINT office_leaves_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: roles roles_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.roles(id);


--
-- Name: roles_permissions roles_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles_permissions
    ADD CONSTRAINT roles_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id);


--
-- Name: roles_permissions roles_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.roles_permissions
    ADD CONSTRAINT roles_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: task_submissions task_submissions_reviewed_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.task_submissions
    ADD CONSTRAINT task_submissions_reviewed_by_fkey FOREIGN KEY (reviewed_by) REFERENCES public.users(id);


--
-- Name: task_submissions task_submissions_task_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.task_submissions
    ADD CONSTRAINT task_submissions_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id);


--
-- Name: task_submissions task_submissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.task_submissions
    ADD CONSTRAINT task_submissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: tasks tasks_assignee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_assignee_id_fkey FOREIGN KEY (assignee_id) REFERENCES public.users(id);


--
-- Name: tasks tasks_assigner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_assigner_id_fkey FOREIGN KEY (assigner_id) REFERENCES public.users(id);


--
-- Name: user_hierarchies user_hierarchies_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_hierarchies
    ADD CONSTRAINT user_hierarchies_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_hierarchies user_hierarchies_supervisor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_hierarchies
    ADD CONSTRAINT user_hierarchies_supervisor_id_fkey FOREIGN KEY (supervisor_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_hierarchies user_hierarchies_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_hierarchies
    ADD CONSTRAINT user_hierarchies_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: users_roles users_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_roles
    ADD CONSTRAINT users_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id);


--
-- Name: users_roles users_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_roles
    ADD CONSTRAINT users_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: users_worksites users_worksites_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_worksites
    ADD CONSTRAINT users_worksites_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: users_worksites users_worksites_worksite_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users_worksites
    ADD CONSTRAINT users_worksites_worksite_id_fkey FOREIGN KEY (worksite_id) REFERENCES public.worksites(id);


--
-- Name: violations violations_recorded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violations
    ADD CONSTRAINT violations_recorded_by_fkey FOREIGN KEY (recorded_by) REFERENCES public.users(id);


--
-- Name: violations violations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violations
    ADD CONSTRAINT violations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: violations violations_violation_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.violations
    ADD CONSTRAINT violations_violation_type_id_fkey FOREIGN KEY (violation_type_id) REFERENCES public.violation_types(id);


--
-- PostgreSQL database dump complete
--

