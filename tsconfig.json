{
	"compilerOptions": {
		"incremental": true,
		"target": "ES2022",
		"module": "CommonJS",
		"lib": ["ES2022"],
		"outDir": "./dist",
		"rootDir": "./",
		"strict": true,
		"esModuleInterop": true,
		"skipLibCheck": true,
		"forceConsistentCasingInFileNames": true,
		"resolveJsonModule": true,
		"declaration": true,
		"declarationMap": true,
		"sourceMap": true,
		"removeComments": true,
		"noImplicitAny": true,
		"strictNullChecks": true,
		"strictFunctionTypes": true,
		"noImplicitThis": true,
		"noImplicitReturns": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedIndexedAccess": true,
		"noImplicitOverride": true,
		"allowUnusedLabels": false,
		"allowUnreachableCode": false,
		// "exactOptionalPropertyTypes": true,
		"experimentalDecorators": true,
		"emitDecoratorMetadata": true,
		"baseUrl": "./",
		"paths": {
			"@/*": ["./src/*"],
			"#/*": ["./*"]
		}
	},
	"include": ["src/**/*"],
	"exclude": ["node_modules", "dist"],
}
