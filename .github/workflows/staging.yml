name: Deployment to Staging Workflows

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v2

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: lts/*
      
      - name: Install dependencies
        run: npm install --frozen-lockfile
      
      - name: Build project
        run: npm run build

      - name: Deploy to Server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST_DEV }}
          username: ${{ secrets.USERNAME_DEV }}
          password: ${{ secrets.PASSWORD_DEV }}
          source: "."
          target: ${{ secrets.PATH_DEV }}

      - name: Install Dependencies on Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_DEV }}
          username: ${{ secrets.USERNAME_DEV }}
          password: ${{ secrets.PASSWORD_DEV }}
          script: |
            cd ${{ secrets.PATH_DEV }}
            npm install --frozen-lockfile

      - name: Restart PM2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST_DEV }}
          username: ${{ secrets.USERNAME_DEV }}
          password: ${{ secrets.PASSWORD_DEV }}
          timeout: 60s
          script: |
            pm2 restart ${{ secrets.PM2_NAME }}

      - name: Notify job failed to Build
        if: ${{ failure() }}
        run: |
          curl -H "Content-Type: application/json" -d '{"username": "Satpam Build Backend E-Money", "content": "Hello, **Backend E-Money** failed to build 😔, please check <@809657628476178452> "}' "https://discord.com/api/webhooks/1080830222548602951/ge0M1avU9v8LN0OkMc9RFv4kx3olmTZvuyZXkf4RrCR2zwHdG6ZVNLeoWInF3ssRJe3o"
