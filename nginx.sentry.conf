# Nginx Configuration for Attendance API + Sentry Self-hosted
# Use this when running both services on same server

events {
    worker_connections 1024;
}

http {
    upstream attendance_api {
        server attendance-api:9000;
    }

    upstream sentry_web {
        server sentry:9000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=sentry:10m rate=5r/s;

    # Main API Server
    server {
        listen 80;
        server_name your-api-domain.com;

        # Rate limiting
        limit_req zone=api burst=20 nodelay;

        # API routes
        location / {
            proxy_pass http://attendance_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Health check
        location /health {
            proxy_pass http://attendance_api/health;
            access_log off;
        }
    }

    # Sentry Server
    server {
        listen 80;
        server_name sentry.your-domain.com;

        # Rate limiting
        limit_req zone=sentry burst=10 nodelay;

        # Sentry routes
        location / {
            proxy_pass http://sentry_web;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Sentry specific headers
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffer settings
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
        }
    }

    # SSL Configuration (when ready)
    # server {
    #     listen 443 ssl http2;
    #     server_name your-api-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/your-domain.crt;
    #     ssl_certificate_key /etc/nginx/ssl/your-domain.key;
    #     
    #     location / {
    #         proxy_pass http://attendance_api;
    #         # ... same proxy settings as above
    #     }
    # }
    
    # server {
    #     listen 443 ssl http2;
    #     server_name sentry.your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/sentry-domain.crt;
    #     ssl_certificate_key /etc/nginx/ssl/sentry-domain.key;
    #     
    #     location / {
    #         proxy_pass http://sentry_web;
    #         # ... same proxy settings as above
    #     }
    # }
}
